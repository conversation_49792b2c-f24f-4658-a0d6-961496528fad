version: 2.1 # Use 2.1 to enable using orbs and other features.

orbs:
  ruby: circleci/ruby@1.1.2
  node: circleci/node@2

jobs:
  test: # our first job, named "build"
    parallelism: 1

    docker:
      - image: circleci/ruby:2.7.2-node-browsers # this is our primary docker image, where step commands run.
      - image: circleci/postgres:11-alpine
        environment: # add POSTGRES environment variables.
          POSTGRES_USER: postgres
          POSTGRES_DB: app_test
          POSTGRES_PASSWORD: ""
    environment:
      BUNDLE_JOBS: "3"
      BUNDLE_RETRY: "3"
      PGHOST: 127.0.0.1
      PGUSER: postgres
      PGPASSWORD: ""
      RAILS_ENV: test
      NODE_ENV: test

    steps:
      - checkout
      - ruby/install-deps
      - node/install-packages:
          pkg-manager: yarn
          cache-key: "yarn.lock"
      - run:
          name: Wait for DB
          command: dockerize -wait tcp://localhost:5432 -timeout 1m
      - run:
          name: Database setup
          command: bundle exec rails db:schema:load --trace
      - run:
          name: Build assets
          command: bundle exec rails assets:precompile
      - run:
          name: Run Rspec tests
          command: bundle exec rspec


# We use workflows to orchestrate the jobs that we declared above.
workflows:
  version: 2
  ci:     # The name of our workflow is "build_and_test"
    jobs:             # The list of jobs we run as part of this workflow.
      - test         # Run build first.
