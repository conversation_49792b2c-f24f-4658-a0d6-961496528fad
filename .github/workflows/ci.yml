name: CI
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      db:
        image: postgres:11
        env:
          POSTGRES_USER: postgres
          POSTGRES_DB: app_test
          POSTGRES_HOST: "localhost"
          POSTGRES_HOST_AUTH_METHOD: "trust"
        ports: ['5432:5432']

      redis:
        image: redis
        ports: ['6379:6379']
        options: --entrypoint redis-server

    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      
      - name: Install Native Deps
        run: |
          sudo apt-get -yqq install libpq-dev

      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '3.0.4'
          bundler-cache: true
      

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 16
          cache: yarn
      
      - name: Install packages
        run: |
          yarn install --pure-lockfile

      - name: Compile Assets
        env:
          RAILS_ENV: test
          NODE_ENV: test
          RAILS_MASTER_KEY: ${{ secrets.RAILS_MASTER_KEY }}
        shell: bash
        run: |
          yarn run build  

      - name: Build and run tests
        env:
          DATABASE_URL: postgres://postgres:@localhost:5432/app_test
          REDIS_URL: redis://localhost:6379/0
          RAILS_ENV: test
          NODE_ENV: test
          RAILS_MASTER_KEY: ${{ secrets.RAILS_MASTER_KEY }}
        run: |
          bundle exec rails db:schema:load --trace
          bundle exec rspec spec/graphql
