# Expired Medicals Report

This document explains how to generate a list of all people with expired medicals, separated by company and ordered by expiry date.

## Features

The expired medicals report provides the following information for each person:
- Full name
- Contact number
- ID number
- Company
- Medical reference
- Medical expiry date

## Available Methods

### 1. GraphQL Query

A new GraphQL query `expiredMedicals` has been added that can be used to fetch expired medicals data.

**Query:**
```graphql
query ExpiredMedicals($organisationId: ID!) {
  expiredMedicals(organisationId: $organisationId) {
    id
    fullName
    contactNumber
    idNumber
    company
    medicalRef
    medicalExpiryDate
    patient {
      id
      firstName
      lastName
    }
    employment {
      id
      company {
        name
      }
    }
    evaluation {
      id
      name
    }
  }
}
```

**Variables:**
```json
{
  "organisationId": "1"
}
```

### 2. Web Interface

Access the expired medicals report through the web interface:

**URL:** `/expired_medicals?organisation_id=1`

Features:
- View expired medicals grouped by company
- Export to CSV format
- Responsive design with summary statistics

### 3. Rake Tasks

Two rake tasks are available for generating reports:

#### Console Report
```bash
rake reports:expired_medicals[organisation_id]
```

Example:
```bash
rake reports:expired_medicals[1]
```

This will output a formatted report to the console showing:
- Company groupings
- Tabular data with all required fields
- Summary statistics

#### CSV Export
```bash
rake reports:expired_medicals_csv[organisation_id,output_file]
```

Example:
```bash
rake reports:expired_medicals_csv[1,expired_report.csv]
```

If no output file is specified, it defaults to `expired_medicals_YYYYMMDD.csv`.

## Data Structure

The report queries the following database relationships:
- `Evaluation` (medical records) with `medical_expiry_date < current_date`
- `Patient` (person information)
- `Employment` (employment relationship)
- `Company` (employer information)

## Sorting and Grouping

- **Primary grouping:** By company name (alphabetical)
- **Secondary sorting:** By medical expiry date (oldest first within each company)

## Sample Output

### Console Format
```
Expired Medicals Report for ABC Organisation
Generated on: 21 September 2025
================================================================================

COMPANY: Acme Corporation
--------------------------------------------------------------------------------
Full Name                 Contact Number  ID Number       Medical Ref         Expired On     
--------------------------------------------------------------------------------
John Smith                +27123456789    8001015009087   MED-2023-001       15 Jan 2024
Jane Doe                  +27987654321    9005125008088   MED-2023-002       20 Feb 2024

Total expired medicals for Acme Corporation: 2

================================================================================

COMPANY: Beta Industries
--------------------------------------------------------------------------------
Full Name                 Contact Number  ID Number       Medical Ref         Expired On     
--------------------------------------------------------------------------------
Bob Johnson               +27555123456    7512105009089   MED-2023-003       10 Mar 2024

Total expired medicals for Beta Industries: 1

================================================================================

SUMMARY
--------------------------------------------------------------------------------
Total companies with expired medicals: 2
Total expired medicals: 3
```

### CSV Format
```csv
Full Name,Contact Number,ID Number,Company,Medical Reference,Medical Expired On
John Smith,+27123456789,8001015009087,Acme Corporation,MED-2023-001,2024-01-15
Jane Doe,+27987654321,9005125008088,Acme Corporation,MED-2023-002,2024-02-20
Bob Johnson,+27555123456,7512105009089,Beta Industries,MED-2023-003,2024-03-10
```

## Files Created/Modified

1. **GraphQL Query:** `app/graphql/queries/expired_medicals.rb`
2. **GraphQL Type:** `app/graphql/types/expired_medical_type.rb`
3. **Controller:** `app/controllers/expired_medicals_controller.rb`
4. **View:** `app/views/expired_medicals/index.html.erb`
5. **Rake Tasks:** `lib/tasks/expired_medicals_report.rake`
6. **Routes:** Updated `config/routes.rb`
7. **Schema:** Updated `app/graphql/types/query_type.rb`

## Usage Examples

### Web Interface
1. Navigate to `/expired_medicals?organisation_id=1`
2. View the report grouped by company
3. Click "Export CSV" to download the data

### Command Line
```bash
# Generate console report
rake reports:expired_medicals[1]

# Generate CSV file
rake reports:expired_medicals_csv[1,my_expired_report.csv]
```

### GraphQL API
Use any GraphQL client to query the `expiredMedicals` field with the organisation ID.

## Error Handling

- Invalid organisation ID will show appropriate error messages
- Empty results will display "No expired medicals found"
- Missing parameters will redirect with error alerts

## Dependencies

- Rails 7.0+
- GraphQL
- PostgreSQL (uses date comparison queries)
- CSV library (for export functionality)
