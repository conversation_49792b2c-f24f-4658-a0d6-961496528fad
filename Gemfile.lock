GIT
  remote: https://github.com/basecamp/name_of_person.git
  revision: f0884a4e98a823eddb50ac2e24f671683b7c12cb
  branch: master
  specs:
    name_of_person (1.1.1)
      activesupport (>= 5.2.0)

GIT
  remote: https://github.com/dabit/annotate_models.git
  revision: b0d0041e06137514fa2bf5d2e10ca4af101cbfb9
  branch: rails-7
  specs:
    annotate (3.1.1)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)

GIT
  remote: https://github.com/rmosolgo/graphiql-rails.git
  revision: 6b34eb17bf13262eef6edf9a069cfc69b5db9708
  branch: master
  specs:
    graphiql-rails (1.8.0)
      railties
      sprockets-rails

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (7.0.4)
      actionpack (= 7.0.4)
      activesupport (= 7.0.4)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (7.0.4)
      actionpack (= 7.0.4)
      activejob (= 7.0.4)
      activerecord (= 7.0.4)
      activestorage (= 7.0.4)
      activesupport (= 7.0.4)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (7.0.4)
      actionpack (= 7.0.4)
      actionview (= 7.0.4)
      activejob (= 7.0.4)
      activesupport (= 7.0.4)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (7.0.4)
      actionview (= 7.0.4)
      activesupport (= 7.0.4)
      rack (~> 2.0, >= 2.2.0)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (7.0.4)
      actionpack (= 7.0.4)
      activerecord (= 7.0.4)
      activestorage (= 7.0.4)
      activesupport (= 7.0.4)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (7.0.4)
      activesupport (= 7.0.4)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    active_record-acts_as (4.0.3)
      activerecord (>= 4.2)
      activesupport (>= 4.2)
    active_storage_validations (1.0.3)
      activejob (>= 5.2.0)
      activemodel (>= 5.2.0)
      activestorage (>= 5.2.0)
      activesupport (>= 5.2.0)
    activejob (7.0.4)
      activesupport (= 7.0.4)
      globalid (>= 0.3.6)
    activemodel (7.0.4)
      activesupport (= 7.0.4)
    activerecord (7.0.4)
      activemodel (= 7.0.4)
      activesupport (= 7.0.4)
    activestorage (7.0.4)
      actionpack (= 7.0.4)
      activejob (= 7.0.4)
      activerecord (= 7.0.4)
      activesupport (= 7.0.4)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (7.0.4)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.1)
      public_suffix (>= 2.0.2, < 6.0)
    administrate (0.18.0)
      actionpack (>= 5.0)
      actionview (>= 5.0)
      activerecord (>= 5.0)
      jquery-rails (>= 4.0)
      kaminari (>= 1.0)
      sassc-rails (~> 2.1)
      selectize-rails (~> 0.6)
    apollo_upload_server (2.1.0)
      actionpack (>= 4.2)
      graphql (>= 1.8)
    ast (2.4.2)
    awesome_print (1.9.2)
    aws-eventstream (1.2.0)
    aws-partitions (1.655.0)
    aws-sdk-core (3.166.0)
      aws-eventstream (~> 1, >= 1.0.2)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.5)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.59.0)
      aws-sdk-core (~> 3, >= 3.165.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.117.1)
      aws-sdk-core (~> 3, >= 3.165.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.4)
    aws-sigv4 (1.5.2)
      aws-eventstream (~> 1, >= 1.0.2)
    backport (1.2.0)
    base64 (0.2.0)
    bcrypt (3.1.18)
    benchmark (0.2.0)
    better_errors (2.9.1)
      coderay (>= 1.0.0)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
    better_html (1.0.16)
      actionview (>= 4.0)
      activesupport (>= 4.0)
      ast (~> 2.0)
      erubi (~> 1.4)
      html_tokenizer (~> 0.0.6)
      parser (>= 2.4)
      smart_properties
    binding_of_caller (1.0.0)
      debug_inspector (>= 0.0.1)
    bootsnap (1.13.0)
      msgpack (~> 1.2)
    builder (3.2.4)
    bullet (7.0.3)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    byebug (11.1.3)
    cable_ready (5.0.0.pre9)
      actioncable (>= 5.2)
      actionpack (>= 5.2)
      actionview (>= 5.2)
      activerecord (>= 5.2)
      activesupport (>= 5.2)
      railties (>= 5.2)
      thread-local (>= 1.1.0)
    capybara (3.37.1)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.8)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    childprocess (4.1.0)
    choice (0.2.0)
    coderay (1.1.3)
    combine_pdf (1.0.27)
      matrix
      ruby-rc4 (>= 0.1.5)
    concurrent-ruby (1.1.10)
    connection_pool (2.3.0)
    crass (1.0.6)
    cssbundling-rails (1.1.1)
      railties (>= 6.0.0)
    cuprite (0.14.2)
      capybara (~> 3.0)
      ferrum (~> 0.12.0)
    debug_inspector (1.1.0)
    declarative (0.0.20)
    devise (4.8.1)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise_invitable (2.0.6)
      actionmailer (>= 5.0)
      devise (>= 4.6)
    diff-lcs (1.5.0)
    disposable (0.6.3)
      declarative (>= 0.0.9, < 1.0.0)
      representable (>= 3.1.1, < 4)
    e2mmap (0.1.0)
    em-websocket (0.5.3)
      eventmachine (>= 0.12.9)
      http_parser.rb (~> 0)
    erb_lint (0.1.3)
      activesupport
      better_html (~> 1.0.7)
      html_tokenizer
      parser (>= *******)
      rainbow
      rubocop
      smart_properties
    erubi (1.11.0)
    eventmachine (1.2.7)
    factory_bot (6.2.1)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.2.0)
      factory_bot (~> 6.2.0)
      railties (>= 5.0.0)
    faker (3.0.0)
      i18n (>= 1.8.11, < 2)
    ferrum (0.12)
      addressable (~> 2.5)
      concurrent-ruby (~> 1.1)
      webrick (~> 1.7)
      websocket-driver (>= 0.6, < 0.8)
    ffi (1.15.5)
    fiber-storage (1.0.0)
    formatador (1.1.0)
    geared_pagination (1.1.2)
      activesupport (>= 5.0)
      addressable (>= 2.5.0)
    globalid (1.0.0)
      activesupport (>= 5.0)
    graphql (2.4.3)
      base64
      fiber-storage
    graphql-rails_logger (1.2.3)
      actionpack (> 5.0)
      activesupport (> 5.0)
      railties (> 5.0)
      rouge (~> 3.0)
    grover (1.1.11)
      combine_pdf (~> 1.0)
      nokogiri (~> 1.0)
    guard (2.18.0)
      formatador (>= 0.2.4)
      listen (>= 2.7, < 4.0)
      lumberjack (>= 1.0.12, < 2.0)
      nenv (~> 0.1)
      notiffany (~> 0.0)
      pry (>= 0.13.0)
      shellany (~> 0.0)
      thor (>= 0.18.1)
    guard-compat (1.2.1)
    guard-livereload (2.5.2)
      em-websocket (~> 0.5)
      guard (~> 2.8)
      guard-compat (~> 1.0)
      multi_json (~> 1.8)
    hiredis (0.6.3)
    hotwire-rails (0.1.3)
      rails (>= 6.0.0)
      stimulus-rails
      turbo-rails
    html_tokenizer (0.0.7)
    http_parser.rb (0.8.0)
    i18n (1.12.0)
      concurrent-ruby (~> 1.0)
    image_processing (1.12.2)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    inline_svg (1.8.0)
      activesupport (>= 3.0)
      nokogiri (>= 1.6)
    jaro_winkler (1.5.4)
    jmespath (1.6.1)
    jquery-rails (4.5.1)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    jsbundling-rails (1.0.3)
      railties (>= 6.0.0)
    json (2.6.2)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    kramdown (2.4.0)
      rexml
    kramdown-parser-gfm (1.1.0)
      kramdown (~> 2.0)
    listen (3.7.1)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    lograge (0.11.2)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    loofah (2.19.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    lumberjack (1.2.8)
    mail (2.7.1)
      mini_mime (>= 0.1.1)
    marcel (1.0.2)
    matrix (0.4.2)
    method_source (1.0.0)
    mini_magick (4.11.0)
    mini_mime (1.1.2)
    minitest (5.16.3)
    mocha (2.0.1)
      ruby2_keywords (>= 0.0.5)
    msgpack (1.6.0)
    multi_json (1.15.0)
    nenv (0.3.0)
    net-imap (0.3.1)
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.1.3)
      timeout
    net-smtp (0.3.3)
      net-protocol
    nio4r (2.5.8)
    nokogiri (1.13.9-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.13.9-x86_64-linux)
      racc (~> 1.4)
    notiffany (0.1.3)
      nenv (~> 0.1)
      shellany (~> 0.0)
    orm_adapter (0.5.0)
    parallel (1.22.1)
    parser (3.1.2.1)
      ast (~> 2.4.1)
    pg (1.5.9)
    pg_search (2.3.6)
      activerecord (>= 5.2)
      activesupport (>= 5.2)
    pry (0.14.1)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-rails (0.3.9)
      pry (>= 0.10.4)
    public_suffix (5.0.0)
    puma (4.3.12)
      nio4r (~> 2.0)
    racc (1.6.0)
    rack (2.2.4)
    rack-cors (1.1.1)
      rack (>= 2.0.0)
    rack-livereload (0.3.17)
      rack
    rack-mini-profiler (2.3.4)
      rack (>= 1.2.0)
    rack-test (2.0.2)
      rack (>= 1.3)
    rails (7.0.4)
      actioncable (= 7.0.4)
      actionmailbox (= 7.0.4)
      actionmailer (= 7.0.4)
      actionpack (= 7.0.4)
      actiontext (= 7.0.4)
      actionview (= 7.0.4)
      activejob (= 7.0.4)
      activemodel (= 7.0.4)
      activerecord (= 7.0.4)
      activestorage (= 7.0.4)
      activesupport (= 7.0.4)
      bundler (>= 1.15.0)
      railties (= 7.0.4)
    rails-dom-testing (2.0.3)
      activesupport (>= 4.2.0)
      nokogiri (>= 1.6)
    rails-erd (1.7.2)
      activerecord (>= 4.2)
      activesupport (>= 4.2)
      choice (~> 0.2.0)
      ruby-graphviz (~> 1.2)
    rails-html-sanitizer (1.4.3)
      loofah (~> 2.3)
    railties (7.0.4)
      actionpack (= 7.0.4)
      activesupport (= 7.0.4)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rainbow (3.1.1)
    rake (13.0.6)
    rb-fsevent (0.11.2)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    redis (5.0.5)
      redis-client (>= 0.9.0)
    redis-client (0.11.0)
      connection_pool
    reform (2.6.2)
      disposable (>= 0.5.0, < 1.0.0)
      representable (>= 3.1.1, < 4)
      uber (< 0.2.0)
    reform-rails (0.2.3)
      activemodel (>= 5.0)
      reform (>= 2.3.1, < 3.0.0)
    regexp_parser (2.6.0)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    request_store (1.5.1)
      rack (>= 1.4)
    responders (3.0.1)
      actionpack (>= 5.0)
      railties (>= 5.0)
    reverse_markdown (2.1.1)
      nokogiri
    rexml (3.2.5)
    rouge (3.30.0)
    rspec-core (3.12.0)
      rspec-support (~> 3.12.0)
    rspec-expectations (3.12.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.12.0)
    rspec-mocks (3.12.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.12.0)
    rspec-rails (5.1.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      railties (>= 5.2)
      rspec-core (~> 3.10)
      rspec-expectations (~> 3.10)
      rspec-mocks (~> 3.10)
      rspec-support (~> 3.10)
    rspec-support (3.12.0)
    rspec_junit_formatter (0.6.0)
      rspec-core (>= 2, < 4, != 2.12.0)
    rubocop (1.38.0)
      json (~> 2.3)
      parallel (~> 1.10)
      parser (>= 3.1.2.1)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 1.8, < 3.0)
      rexml (>= 3.2.5, < 4.0)
      rubocop-ast (>= 1.23.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 1.4.0, < 3.0)
    rubocop-ast (1.23.0)
      parser (>= *******)
    rubocop-graphql (0.18.0)
      rubocop (>= 0.87, < 2)
    rubocop-performance (1.15.0)
      rubocop (>= 1.7.0, < 2.0)
      rubocop-ast (>= 0.4.0)
    rubocop-rails (2.17.2)
      activesupport (>= 4.2.0)
      rack (>= 1.1)
      rubocop (>= 1.33.0, < 2.0)
    rubocop-rspec (2.14.2)
      rubocop (~> 1.33)
    ruby-graphviz (1.2.5)
      rexml
    ruby-progressbar (1.11.0)
    ruby-rc4 (0.1.5)
    ruby-vips (2.1.4)
      ffi (~> 1.12)
    ruby2_keywords (0.0.5)
    rubyzip (2.3.2)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    selectize-rails (0.12.6)
    selenium-webdriver (4.5.0)
      childprocess (>= 0.5, < 5.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    shellany (0.0.1)
    shoulda (4.0.0)
      shoulda-context (~> 2.0)
      shoulda-matchers (~> 4.0)
    shoulda-context (2.0.0)
    shoulda-matchers (4.5.1)
      activesupport (>= 4.2.0)
    sidekiq (7.0.0)
      concurrent-ruby (< 2)
      connection_pool (>= 2.3.0)
      rack (>= 2.2.4)
      redis-client (>= 0.9.0)
    skylight (6.0.4)
      activesupport (>= 5.2.0)
    smart_properties (1.17.0)
    solargraph (0.40.4)
      backport (~> 1.1)
      benchmark
      bundler (>= 1.17.2)
      e2mmap
      jaro_winkler (~> 1.5)
      kramdown (~> 2.3)
      kramdown-parser-gfm (~> 1.1)
      parser (~> 3.0)
      reverse_markdown (>= 1.0.5, < 3)
      rubocop (>= 0.52)
      thor (~> 1.0)
      tilt (~> 2.0)
      yard (~> 0.9, >= 0.9.24)
    solargraph-rails (0.2.0.pre)
      solargraph (~> 0.40.0)
    spring (3.1.1)
    sprockets (4.1.1)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    standard (0.0.36)
      rubocop (>= 0.63)
    stimulus-rails (1.1.1)
      railties (>= 6.0.0)
    stimulus_reflex (3.5.0.pre9)
      actioncable (>= 5.2)
      actionpack (>= 5.2)
      actionview (>= 5.2)
      activesupport (>= 5.2)
      cable_ready (>= 5.0.0.pre9)
      nokogiri
      rack
      railties (>= 5.2)
      redis
    strong_migrations (1.4.0)
      activerecord (>= 5.2)
    thor (1.2.1)
    thread-local (1.1.0)
    tilt (2.0.11)
    timeout (0.3.0)
    trailblazer-option (0.1.2)
    turbo-rails (0.5.4)
      rails (>= 6.0.0)
    tzinfo (2.0.5)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    unicode-display_width (2.3.0)
    uniform_notifier (1.16.0)
    view_component (2.49.1)
      activesupport (>= 5.0.0, < 8.0)
      method_source (~> 1.0)
    view_component_reflex (3.2.0.pre2)
      rails (>= 5.2, < 8.0)
      stimulus_reflex (>= 3.5.0.pre9)
      view_component (>= 2.28.0)
    warden (1.2.9)
      rack (>= 2.0.9)
    webdrivers (5.2.0)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0)
    webrick (1.7.0)
    websocket (1.2.9)
    websocket-driver (0.7.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    yard (0.9.28)
      webrick (~> 1.7.0)
    zeitwerk (2.6.4)

PLATFORMS
  arm64-darwin-22
  x86_64-linux

DEPENDENCIES
  active_record-acts_as (~> 4.0, >= 4.0.3)
  active_storage_validations
  administrate (~> 0.18.0)
  annotate!
  apollo_upload_server (= 2.1)
  awesome_print
  aws-sdk-s3 (~> 1.14)
  better_errors
  binding_of_caller
  bootsnap (>= 1.4.2)
  bullet
  byebug
  cable_ready
  capybara (>= 2.15)
  cssbundling-rails
  cuprite
  devise (~> 4.8, >= 4.8.1)
  devise_invitable (~> 2.0, >= 2.0.6)
  erb_lint (~> 0.1.1)
  factory_bot_rails (~> 6.2)
  faker
  geared_pagination (~> 1.0, >= 1.0.1)
  graphiql-rails!
  graphql (~> 2.4.0)
  graphql-rails_logger
  grover (~> 1.1.9)
  guard-livereload (~> 2.5.2)
  hiredis
  hotwire-rails (~> 0.1.3)
  image_processing (>= 1.2)
  inline_svg (~> 1.7, >= 1.7.1)
  jsbundling-rails
  listen (~> 3.2)
  lograge (~> 0.11.2)
  marcel (~> 1.0, >= 1.0.2)
  mocha
  name_of_person!
  pg (~> 1.5.8)
  pg_search
  pry-rails
  puma (~> 4.1)
  rack-cors
  rack-livereload
  rack-mini-profiler (~> 2.3)
  rails (~> 7.0)
  rails-erd
  redis (>= 4.0)
  reform (~> 2.6, >= 2.6.1)
  reform-rails (~> 0.2.3)
  rspec-rails (~> 5.0, >= 5.0.2)
  rspec_junit_formatter
  rubocop
  rubocop-graphql
  rubocop-performance
  rubocop-rails
  rubocop-rspec
  selenium-webdriver
  shoulda (~> 4.0)
  sidekiq
  skylight
  solargraph
  solargraph-rails (= 0.2.0.pre)
  spring (~> 3.0)
  sprockets-rails (~> 3.4.2)
  standard
  stimulus-rails
  stimulus_reflex
  strong_migrations
  turbo-rails (= 0.5.4)
  tzinfo-data
  view_component (~> 2.49.0)
  view_component_reflex (~> 3.1, >= 3.1.12)
  webdrivers

RUBY VERSION
   ruby 3.1.2p20

BUNDLED WITH
   2.3.25
