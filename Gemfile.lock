GIT
  remote: https://github.com/dabit/annotate_models.git
  revision: b0d0041e06137514fa2bf5d2e10ca4af101cbfb9
  branch: rails-7
  specs:
    annotate (3.1.1)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)

GIT
  remote: https://github.com/rmosolgo/graphiql-rails.git
  revision: 6b34eb17bf13262eef6edf9a069cfc69b5db9708
  branch: master
  specs:
    graphiql-rails (1.8.0)
      railties
      sprockets-rails

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4, < 3.2)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_record-acts_as (4.0.3)
      activerecord (>= 4.2)
      activesupport (>= 4.2)
    active_storage_validations (2.0.3)
      activejob (>= 6.1.4)
      activemodel (>= 6.1.4)
      activestorage (>= 6.1.4)
      activesupport (>= 6.1.4)
      marcel (>= 1.0.3)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
    addressable (2.8.1)
      public_suffix (>= 2.0.2, < 6.0)
    administrate (0.18.0)
      actionpack (>= 5.0)
      actionview (>= 5.0)
      activerecord (>= 5.0)
      jquery-rails (>= 4.0)
      kaminari (>= 1.0)
      sassc-rails (~> 2.1)
      selectize-rails (~> 0.6)
    apollo_upload_server (2.1.0)
      actionpack (>= 4.2)
      graphql (>= 1.8)
    awesome_print (1.9.2)
    aws-eventstream (1.2.0)
    aws-partitions (1.655.0)
    aws-sdk-core (3.166.0)
      aws-eventstream (~> 1, >= 1.0.2)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.5)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.59.0)
      aws-sdk-core (~> 3, >= 3.165.0)
      aws-sigv4 (~> 1.1)
    aws-sdk-s3 (1.117.1)
      aws-sdk-core (~> 3, >= 3.165.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.4)
    aws-sigv4 (1.5.2)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    bcrypt (3.1.18)
    benchmark (0.4.0)
    better_errors (2.10.1)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
      rouge (>= 1.0.0)
    bigdecimal (3.1.9)
    binding_of_caller (1.0.0)
      debug_inspector (>= 0.0.1)
    bootsnap (1.13.0)
      msgpack (~> 1.2)
    builder (3.2.4)
    bullet (8.0.5)
      activesupport (>= 3.0.0)
      uniform_notifier (~> 1.11)
    byebug (11.1.3)
    cable_ready (5.0.6)
      actionpack (>= 5.2)
      actionview (>= 5.2)
      activesupport (>= 5.2)
      observer (~> 0.1)
      railties (>= 5.2)
      thread-local (>= 1.1.0)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    choice (0.2.0)
    coderay (1.1.3)
    combine_pdf (1.0.27)
      matrix
      ruby-rc4 (>= 0.1.5)
    concurrent-ruby (1.3.5)
    connection_pool (2.3.0)
    crass (1.0.6)
    cssbundling-rails (1.4.3)
      railties (>= 6.0.0)
    cuprite (0.15.1)
      capybara (~> 3.0)
      ferrum (~> 0.15.0)
    date (3.4.1)
    debug_inspector (1.1.0)
    declarative (0.0.20)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    devise_invitable (2.0.10)
      actionmailer (>= 5.0)
      devise (>= 4.6)
    diff-lcs (1.5.0)
    disposable (0.6.3)
      declarative (>= 0.0.9, < 1.0.0)
      representable (>= 3.1.1, < 4)
    drb (2.2.1)
    em-websocket (0.5.3)
      eventmachine (>= 0.12.9)
      http_parser.rb (~> 0)
    erubi (1.11.0)
    eventmachine (1.2.7)
    factory_bot (6.2.1)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.2.0)
      factory_bot (~> 6.2.0)
      railties (>= 5.0.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    ferrum (0.15)
      addressable (~> 2.5)
      concurrent-ruby (~> 1.1)
      webrick (~> 1.7)
      websocket-driver (~> 0.7)
    ffi (1.15.5)
    fiber-storage (1.0.0)
    formatador (1.1.0)
    geared_pagination (1.2.0)
      activesupport (>= 5.0)
      addressable (>= 2.5.0)
    globalid (1.0.0)
      activesupport (>= 5.0)
    graphql (2.4.16)
      base64
      fiber-storage
      logger
    graphql-rails_logger (1.2.5)
      actionpack (> 5.0)
      activesupport (> 5.0)
      railties (> 5.0)
      rouge (>= 3.0)
    grover (1.1.11)
      combine_pdf (~> 1.0)
      nokogiri (~> 1.0)
    guard (2.19.1)
      formatador (>= 0.2.4)
      listen (>= 2.7, < 4.0)
      logger (~> 1.6)
      lumberjack (>= 1.0.12, < 2.0)
      nenv (~> 0.1)
      notiffany (~> 0.0)
      ostruct (~> 0.6)
      pry (>= 0.13.0)
      shellany (~> 0.0)
      thor (>= 0.18.1)
    guard-compat (1.2.1)
    guard-livereload (2.5.2)
      em-websocket (~> 0.5)
      guard (~> 2.8)
      guard-compat (~> 1.0)
      multi_json (~> 1.8)
    hiredis (0.6.3)
    hotwire-rails (0.1.3)
      rails (>= 6.0.0)
      stimulus-rails
      turbo-rails
    http_parser.rb (0.8.0)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    image_processing (1.12.2)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    inline_svg (1.10.0)
      activesupport (>= 3.0)
      nokogiri (>= 1.6)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jmespath (1.6.1)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    jsbundling-rails (1.0.3)
      railties (>= 6.0.0)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    listen (3.7.1)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.7.0)
    lograge (0.11.2)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    loofah (2.24.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    lumberjack (1.2.8)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    method_source (1.0.0)
    mini_magick (4.11.0)
    mini_mime (1.1.2)
    minitest (5.16.3)
    mocha (2.0.1)
      ruby2_keywords (>= 0.0.5)
    msgpack (1.6.0)
    multi_json (1.15.0)
    name_of_person (1.1.1)
      activesupport (>= 5.2.0)
    nenv (0.3.0)
    net-imap (0.3.1)
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.1.3)
      timeout
    net-smtp (0.3.3)
      net-protocol
    nio4r (2.5.8)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    nokogiri-html5-inference (0.3.0)
      nokogiri (~> 1.14)
    notiffany (0.1.3)
      nenv (~> 0.1)
      shellany (~> 0.0)
    observer (0.1.2)
    orm_adapter (0.5.0)
    ostruct (0.6.1)
    pg (1.5.9)
    pg_search (2.3.7)
      activerecord (>= 6.1)
      activesupport (>= 6.1)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    pry (0.14.1)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-rails (0.3.9)
      pry (>= 0.10.4)
    psych (5.2.3)
      date
      stringio
    public_suffix (5.0.0)
    puma (4.3.12)
      nio4r (~> 2.0)
    racc (1.6.0)
    rack (3.1.13)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-livereload (0.6.1)
      rack (>= 3.0, < 3.2)
    rack-mini-profiler (2.3.4)
      rack (>= 1.2.0)
    rack-session (2.1.0)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-erd (1.7.2)
      activerecord (>= 4.2)
      activesupport (>= 4.2)
      choice (~> 0.2.0)
      ruby-graphviz (~> 1.2)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rake (13.0.6)
    rb-fsevent (0.11.2)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    rdoc (6.13.1)
      psych (>= 4.0.0)
    redis (5.0.5)
      redis-client (>= 0.9.0)
    redis-client (0.11.0)
      connection_pool
    reform (2.6.2)
      disposable (>= 0.5.0, < 1.0.0)
      representable (>= 3.1.1, < 4)
      uber (< 0.2.0)
    reform-rails (0.2.6)
      activemodel (>= 5.0)
      reform (>= 2.3.1, < 3.0.0)
    regexp_parser (2.6.0)
    reline (0.6.1)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    request_store (1.7.0)
      rack (>= 1.4)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rexml (3.4.1)
    rouge (3.30.0)
    rspec-core (3.12.0)
      rspec-support (~> 3.12.0)
    rspec-expectations (3.12.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.12.0)
    rspec-mocks (3.12.0)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.12.0)
    rspec-rails (5.1.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      railties (>= 5.2)
      rspec-core (~> 3.10)
      rspec-expectations (~> 3.10)
      rspec-mocks (~> 3.10)
      rspec-support (~> 3.10)
    rspec-support (3.12.0)
    rspec_junit_formatter (0.6.0)
      rspec-core (>= 2, < 4, != 2.12.0)
    ruby-graphviz (1.2.5)
      rexml
    ruby-rc4 (0.1.5)
    ruby-vips (2.1.4)
      ffi (~> 1.12)
    ruby2_keywords (0.0.5)
    rubyzip (2.4.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    securerandom (0.4.1)
    selectize-rails (0.12.6)
    selenium-webdriver (4.10.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    shellany (0.0.1)
    shoulda (4.0.0)
      shoulda-context (~> 2.0)
      shoulda-matchers (~> 4.0)
    shoulda-context (2.0.0)
    shoulda-matchers (4.5.1)
      activesupport (>= 4.2.0)
    sidekiq (7.0.9)
      concurrent-ruby (< 2)
      connection_pool (>= 2.3.0)
      rack (>= 2.2.4)
      redis-client (>= 0.11.0)
    skylight (6.0.4)
      activesupport (>= 5.2.0)
    spring (3.1.1)
    sprockets (4.2.2)
      concurrent-ruby (~> 1.0)
      logger
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    stimulus-rails (1.1.1)
      railties (>= 6.0.0)
    stimulus_reflex (3.5.3)
      actioncable (>= 5.2)
      actionpack (>= 5.2)
      actionview (>= 5.2)
      activesupport (>= 5.2)
      cable_ready (~> 5.0)
      nokogiri (~> 1.0)
      nokogiri-html5-inference (~> 0.3)
      rack (>= 2, < 4)
      railties (>= 5.2)
      redis (>= 4.0, < 6.0)
    stringio (3.1.7)
    strong_migrations (1.4.0)
      activerecord (>= 5.2)
    thor (1.3.2)
    thread-local (1.1.0)
    tilt (2.0.11)
    timeout (0.4.3)
    trailblazer-option (0.1.2)
    turbo-rails (0.5.4)
      rails (>= 6.0.0)
    tzinfo (2.0.5)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    uniform_notifier (1.16.0)
    useragent (0.16.11)
    view_component (2.49.1)
      activesupport (>= 5.0.0, < 8.0)
      method_source (~> 1.0)
    view_component_reflex (3.3.5)
      rails (>= 5.2, < 9.0)
      stimulus_reflex (>= 3.5.0.pre9)
      view_component (>= 2.28.0)
    warden (1.2.9)
      rack (>= 2.0.9)
    webdrivers (5.3.1)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0, < 4.11)
    webrick (1.7.0)
    websocket (1.2.11)
    websocket-driver (0.7.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.6.4)

PLATFORMS
  arm64-darwin-22
  arm64-darwin-24
  x86_64-linux

DEPENDENCIES
  active_record-acts_as (~> 4.0, >= 4.0.3)
  active_storage_validations
  administrate (~> 0.18.0)
  annotate!
  apollo_upload_server (= 2.1)
  awesome_print
  aws-sdk-s3 (~> 1.14)
  better_errors
  binding_of_caller
  bootsnap (>= 1.4.2)
  bullet
  byebug
  cable_ready
  capybara (>= 2.15)
  cssbundling-rails
  cuprite
  devise (~> 4.8, >= 4.8.1)
  devise_invitable (~> 2.0, >= 2.0.6)
  factory_bot_rails (~> 6.2)
  faker
  geared_pagination (~> 1.0, >= 1.0.1)
  graphiql-rails!
  graphql (~> 2.4.0)
  graphql-rails_logger
  grover (~> 1.1.9)
  guard-livereload (~> 2.5.2)
  hiredis
  hotwire-rails (~> 0.1.3)
  image_processing (>= 1.2)
  inline_svg (~> 1.7, >= 1.7.1)
  jsbundling-rails
  listen (~> 3.2)
  lograge (~> 0.11.2)
  marcel (~> 1.0, >= 1.0.2)
  mocha
  name_of_person
  pg (~> 1.5.8)
  pg_search
  pry-rails
  puma (~> 4.1)
  rack-cors
  rack-livereload
  rack-mini-profiler (~> 2.3)
  rails (~> 7.1)
  rails-erd
  redis (>= 4.0)
  reform (~> 2.6, >= 2.6.1)
  reform-rails (~> 0.2.3)
  rspec-rails (~> 5.0, >= 5.0.2)
  rspec_junit_formatter
  selenium-webdriver
  shoulda (~> 4.0)
  sidekiq
  skylight
  spring (~> 3.0)
  sprockets-rails (~> 3.4.2)
  stimulus-rails
  stimulus_reflex
  strong_migrations
  turbo-rails (= 0.5.4)
  tzinfo-data
  view_component (~> 2.49.0)
  view_component_reflex
  webdrivers

RUBY VERSION
   ruby 3.3.6p108

BUNDLED WITH
   2.3.25
