Heroku Hosting: https://intense-waters-66995.herokuapp.com/

# To Dos

- Status and Delete Locking of assessments
- Mail Intergration in Production with SendGrid
- Redis for SideKiq in Dev and Prod
- Logs to service like Papertrail
- CDN cloudfront
- S3 in prod
- Error Tracking with Errbit
- LightSail for Hosting
- OJ for Json Processing , if needed
- Pagy for pagination
- Ankane/ Rails for security
- Auth Trail for Devise - Login Activity
- Add Test for the Schema using the rake task


# Features

- Evaluations
- NAV stimulus.js
- CRUD, Testing
- Vision Screening
- Pysical
- Heat Stress Testing
- Working at Heights

When add a patient , ability to add employment info, new or existing company

Implement Clinics for the Reports
Implement Signitures, Name,Surname , Qualifications for signture on reports for signoff

Remove polymorphic Association of the Audio

Patient Show

- Style the Employment History like the Medical Certificate
- Fix the Styling of the first 2 items in the timeline
- Investigate if possible to use Futurism for the timeline, to improve load times

Exclusions

- CRUD
- Test

Refferals

- CRUD
- Test

Employment

- Test
- CRUD

Spiro

- Test
- CRUD, similar to Audio

Client Required Docs

- Test
- CRUD, similar to Audio

ECG

- Test
- CRUD, similar to Audio

Roles

Use forest Admin for Admin Dashboards - https://www.forestadmin.com/premium

Use strong Migration for Catch unsafe migrations in development

Use Papertrail for logs

Use squeen for security

Use self hosted countly for analytics
https://count.ly/product
or research for analytics like Mixpanel or Ahoy&Blazor - watch Gorails

Standardize script - use codefund

add Rack Deflator for compression
https://thoughtbot.com/blog/content-compression-with-rack-deflater

- Can only delete if not associated to any documents

Testing PDF uploads <-----
https://github.com/nteract/bookstore/blob/196d7c06555a2194008c094d0365b4c0832662e8/.circleci/config.yml#L11-L16
https://github.com/transloadit/uppy/issues/1273
Remove Shrine Memory test

Invitable Module

Add User factory that creates the required organisation setups
https://github.com/thoughtbot/factory_bot/blob/master/GETTING_STARTED.md#best-practices-1

Add Settings sections for default organisation -> under preferences

Conter Cache on Org - users, teams, patients and companies

Signature Pads

Routing of Multiple Orgs - Deleting thereof

Name Assessment & use Audio 2020/06/26
Lab Test Fulfilment - same as signoff

SignOff

- only if not signed off status
- Edit change status to Eddited/Drafts -> last Editted by

Generate Report link

LabTest Create go to -> Show for review

Remove Attachment Bar

Signoff comments on Signoff

Job Status Pending

Organisation Logo

List of Tests on Evaluation

Refferals and Exclusions for Evaluation

Construction Gazette Ann 3
Working at Heights
Occ Medical Questionaire
Vision Screening
Occ Med Exam

Add snellan rating with glasses and without glasses
add night vision question to vision screening
wearing of glasses or contacts

Investigate:

- webconsole, currently breaking things
- airbrake error in rails 7
  Remove:
- Stimulus-reflex
- sprockets
- view_component_reflex
- cable_ready
- marcel??
- spring
