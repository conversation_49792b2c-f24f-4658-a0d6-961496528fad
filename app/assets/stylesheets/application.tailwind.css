@tailwind base;
@tailwind components;
@tailwind utilities;


 @import 'flatpickr/dist/flatpickr.css';
 @import 'dropzone/dist/basic.css';
 @import 'dropzone/dist/dropzone.css';

 .field_with_errors > input {
   @apply border-red-600;
 }

 .field_with_errors > select {
   @apply border-red-600;
 }

 .dropzone {
   @apply bg-gray-100 border-none;

   &.dz-drag-hover {
     @apply font-bold border-none;
   }
 }

 .dropzone.dz-drag-hover {
   @apply font-bold border-none;
 }

 .dropzone .dz-preview .dz-error-message {
   top: 150px !important;
 }

 .dz-message {
   @apply hidden;
 }

 .dz-remove {
   @apply bg-gray-100;
 }

 .page.portrait {
   width: 8.3in;
   height: 11.6in;
 }

 .page {
   position: relative;
   overflow: hidden;
   padding: 0.4in;
   page-break-after: always;
 }

 .page.landscape {
   width: 11.7in;
   height: 8.2in;
 }

 .footer.right {
   position: absolute;
   right: 0.2in;
   bottom: 0.6in;
 }

 .footer.left {
   position: absolute;
   left: 0.4in;
   bottom: 0.8in;
 }

 .uppy-DragDrop-container {
   display: -ms-flexbox;
   display: flex;
   -ms-flex-align: center;
   align-items: center;
   -ms-flex-pack: center;
   justify-content: center;
   border-radius: 7px;
   background-color: #fff;
   cursor: pointer;
   font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial,
     sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
   max-width: 100%;
 }
 .uppy-DragDrop-container:focus {
   outline: none;
   box-shadow: 0 0 0 3px rgba(34, 117, 215, 0.4);
 }
 .uppy-DragDrop-container::-moz-focus-inner {
   border: 0;
 }

 .uppy-DragDrop-inner {
   margin: 0;
   text-align: center;
   padding: 80px 20px;
   line-height: 1.4;
 }

 .uppy-DragDrop-arrow {
   width: 60px;
   height: 60px;
   fill: #e0e0e0;
   margin-bottom: 17px;
 }

 .uppy-DragDrop--isDragDropSupported {
   border: 2px dashed #adadad;
 }

 .uppy-DragDrop--isDraggingOver {
   border: 2px dashed #2275d7;
   background: #eaeaea;
 }
 .uppy-DragDrop--isDraggingOver .uppy-DragDrop-arrow {
   fill: #939393;
 }

 .uppy-DragDrop-label {
   @apply font-medium text-sm text-indigo-600;
   display: block;
   margin-bottom: 5px;
 }

 .uppy-DragDrop-browse {
   @apply text-sm text-gray-600;
 }

 .uppy-DragDrop-note {
   @apply text-xs text-gray-500;
 }

.direct-upload {
  display: inline-block;
  position: relative;
  padding: 2px 4px;
  margin: 0 3px 3px 0;
  border: 1px solid rgba(0, 0, 0, 0.3);
  border-radius: 3px;
  font-size: 11px;
  line-height: 13px;
}

.direct-upload--pending {
  opacity: 0.6;
}

.direct-upload__progress {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  opacity: 0.2;
  background: #0076ff;
  transition: width 120ms ease-out, opacity 60ms 60ms ease-in;
  transform: translate3d(0, 0, 0);
}

.direct-upload--complete .direct-upload__progress {
  opacity: 0.4;
}

.direct-upload--error {
  border-color: red;
}

input[type=file][data-direct-upload-url][disabled] {
  display: none;
}


 @import 'tailwindcss/utilities';

