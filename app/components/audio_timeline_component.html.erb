<li class="pb-6">
  <div class="flex items-center mb-1">
    <div class="border-gray-200 border-2 items-center justify-center flex rounded-full bg-white w-10 h-10 z-10">
      <%= svg_icon %>
    </div>
    <div class="flex-1 ml-4 text-xs text-gray-500"> at <%= audio.created_at.strftime("%d %B %Y - %H:%M") %> &middot;

      <span class="text-indigo-600"><%= link_to "edit", edit_link( audio ), data: {turbolinks: false} %></span></div>
  </div>
  <div class="inline-block p-2 rounded-md ml-12 bg-indigo-100 text-gray-800">
    <span class="mb-2 text-indigo-600">
      <%= link_to "Audiometry", edit_audio_path( audio ) %>
    </span>
    <% if audio.result.blank? %>
      - no abnormalities detected
    <% else %>
      - <%= audio.result %>
    <% end %>

    <% audio.attachments.each do |att| %>

      <div class="flex items-center mt-2 text-xs text-indigo-400">
        <span class="text-indigo-600">
          <svg class="w-4 h-4 " fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor"><path d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path></svg>
        </span>
        <span class="ml-1 hover:text-indigo-700">
          <%= link_to att.content.original_filename, att.content_url, target: :blank %>
        </span>
      </div>
    <% end %>
  </div>

<% if audio.respond_to?(:last_edited_by) %>
  <div class='flex justify-start ml-14 mt-2 text-xs text-gray-400'> by <%= audio.last_edited_by %>  </div>
<% end %>
</li>
