# frozen_string_literal: true

class DrugScreeningReportItemComponent < ViewComponent::Base
  attr_reader :name, :result
  def initialize(result:, name:)
    @name = name
    @result = result
  end

  def svg
    svg = if result == "Positive"
      <<~FOO
        <svg viewBox="0 0 20 20" fill="currentColor" class="flex-shrink-0 h-5 w-5 text-red-400 check-circle w-6 h-6"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>
      FOO
    else
      <<~FOO
        <svg viewBox="0 0 20 20" fill="currentColor" class="x-circle w-6 h-6"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>
      FOO
    end
    svg.html_safe
  end

  def render?
    result != "Not Performed"
  end

  def text_color
    if result == "Positive"
      "text-red-600 hover:text-red-500"
    else
      "text-green-600 hover:text-green-500"
    end
  end
end
