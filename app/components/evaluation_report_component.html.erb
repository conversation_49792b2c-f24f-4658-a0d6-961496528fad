<div class="flex-col justify-center bg-white">
  <div class="flex flex-col h-full report ">
    <div class="flex items-start justify-between w-full ">
      <div class="w-48">
        <%= image_tag "Logo.png" %>
      </div>
      <div class="pt-2 text-right">
        <div class="text-lg font-bold text-gray-600">
          <%= clinic.clinic_name %>
        </div>
        <div class="text-xs text-gray-500">
          Reg. No. <%= clinic.organisation.registration_number %>
        </div>
        <div class="text-xs text-gray-500">
          <%= clinic.physical_address %>
        </div>
        <% if clinic.physical_address_2.present?  %>
          <div class="text-xs text-gray-500">
            <%= clinic.physical_address_2 %>
          </div>
        <% end %>
        <div class="text-xs text-gray-500">
          Tel: <%= clinic.phone_number %>
        </div>
        <div class="text-xs text-gray-500 ">
          <%= clinic.organisation.email_address %>
        </div>
        <div class="text-xs text-gray-500 ">
          <%= clinic.organisation.web_address %>
        </div>
      </div>
    </div>
    <div class="flex justify-center my-6 text-2xl font-bold text-gray-700">
      Certification of Occupational Medical Fitness
    </div>
    <div
      style="background-image: url('https://www.transparenttextures.com/patterns/triangles.png')"
      class="grid grid-flow-col grid-cols-2 grid-rows-4 gap-1 col-gap-16 px-4 py-4 border-t border-b border-gray-200 gap-x-3 ">
      <div class="flex items-baseline justify-between text-left">
        <div class="text-sm text-gray-700">Patient</div>
        <div class="font-bold truncate text-ellipsis text-gray-700 text-md "><%= patient.name.full %></div>
      </div>
      <div class="flex items-baseline justify-between text-left ">
        <div class="text-sm text-gray-700">ID No</div>
        <div class="font-bold text-gray-700 text-md"><%= patient.identification_number %></div>
      </div>
      <div class="flex items-baseline justify-between">
        <div class="text-sm text-gray-700">Date of Birth</div>
        <div class="font-bold text-gray-700 text-md"><%= patient.dob %></div>
      </div>
      <div class="flex items-baseline justify-between">
        <div class="text-sm text-gray-700">Gender</div>
        <div class="font-bold text-gray-700 text-md"><%= patient.gender.capitalize %></div>
      </div>
      <div class="flex items-baseline justify-between">
        <div class="text-sm text-gray-700">Employer</div>
        <div class="font-bold truncate text-gray-700 text-md">
          <% if patient.employments.empty? %>
            Not Specified
          <% else %>
            <% if patient.employments.empty? %>
              <%= patient.employments.last.company.name %>
            <% else %>
              <%= "#{ patient.employments.last.company.name }" %>
            <% end %>
          <% end %>
        </div>
      </div>
      <div class="flex items-baseline justify-between">
        <div class="text-sm text-gray-700">Position</div>
        <div class="font-bold truncate text-gray-700 text-md">
          <% if patient.employments.empty? %>
            Not Specified
          <% else %>
            <%= "[#{patient.employments.last.department}] #{ patient.employments.last.position } " %>
          <% end %>
        </div>
      </div>
      <div class="flex items-baseline justify-between">
        <div class="text-sm text-gray-700">Date of Medical Examination</div>
        <div class="font-bold text-gray-700 text-md"><%= evaluation.medical_examination_date %></div>
      </div>
      <div class="flex items-baseline justify-between">
        <div class="text-sm text-gray-700">Expiry Date of Medical</div>
        <div class="font-bold text-gray-700 text-md"><%= evaluation.medical_expiry_date %></div>
      </div>
    </div>
    <div class="flex flex-col w-full py-1 mt-4 text-xs text-gray-500">
      <span>This is to certify that the following has been examined in accordance with the Occupational Health and Safety Act</span>
      <span>(Act 85 of 1993)</span>
    </div>
    <div class="flex items-center px-4 mt-2 mb-2 text-sm font-bold text-gray-700">
      <scan>Medical Evaluation Information</scan>
    </div>
    <div class="mb-4">
      <div class="py-0.5 w-full pl-16 flex odd:bg-gray-100 bg-white flex items-center">
        <div class="w-full py-0.5 font-bold text-gray-600 items-center text-xs flex">
          <span>
            <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
          </span>
          <span>
            Ref.
          </span>
          <span class="ml-1 text-red-600">
            <%= evaluation.name %>
          </span>
        </div>
        <div class="w-full py-0.5 font-bold text-gray-600 items-center text-xs flex">
          <span>
            <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
          </span>
          <span>
            Type: <%= evaluation.medical_type %>
          </span>
        </div>
      </div>
      <div class="flex items-center px-4 mt-2 mb-2 text-sm font-bold text-gray-700">
        <scan>Special Investigations Performed</scan>
      </div>
      <div class="mb-4">
        <div class="grid grid-flow-col grid-cols-3 grid-rows-3 gap-1 pl-16 ">
          <% if @evaluation.cannabis_performed %>
            <div class="">
              <div class="py-0.5  text-gray-600 text-xs items-center flex">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
                </span>
                <span>
                  Lab Screening 
                </span>
              </div>
            </div>
          <% end %>
          <% if @evaluation.physical_exam_performed %>
            <div class="">
              <div class="py-0.5  text-gray-600 text-xs items-center flex">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
                </span>
                <span>
                  Physical Exam
                </span>
              </div>
            </div>
          <% end %>
          <% if @evaluation.visual_performed %>
            <div class="">
              <div class="py-0.5  text-gray-600 text-xs items-center flex">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
                </span>
                <span>
                  Vision Screening
                </span>
              </div>
            </div>
          <% end %>
          <% if @evaluation.audio_performed %>
            <div class="">
              <div class="py-0.5 text-gray-600 text-xs items-center flex">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
                </span>
                <span>
                  Audiometry
                </span>
              </div>
            </div>
          <% end %>
          <% if @evaluation.spiro_performed %>
            <div class="">
              <div class="py-0.5 text-gray-600 text-xs items-center flex">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
                </span>
                <span>
                  Spirometry
                </span>
              </div>
            </div>
          <% end %>
          <% if @evaluation.xray_performed %>
            <div class="">
              <div class="py-0.5 text-gray-600 text-xs items-center flex">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
                </span>
                <span>
                  Chest X Ray
                </span>
              </div>
            </div>
          <% end %>
          <% if @evaluation.ecg_performed %>
            <div class="">
              <div class="py-0.5 text-gray-600 text-xs items-center flex">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
                </span>
                <span>
                  Resting ECG
                </span>
              </div>
            </div>
          <% end %>
          <% if @evaluation.heat_performed %>
            <div class="">
              <div class="py-0.5 text-gray-600 text-xs items-center flex">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
                </span>
                <span>
                  Heat Stress Evaluation
                </span>
              </div>
            </div>
          <% end %>
          <% if @evaluation.height_performed %>
            <div class="">
              <div class="py-0.5 text-gray-600 text-xs items-center flex">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
                </span>
                <span>
                  Working @ Heights Evaluation
                </span>
              </div>
            </div>
          <% end %>
        </div>
        <div class="flex items-center px-4 mt-2 mb-2 text-sm font-bold text-gray-700">
          <scan>Result</scan>
        </div>
        <div class="mb-2">
          <div class="py-0.5 w-full pl-16 flex odd:bg-gray-100 bg-white flex items-center">
            <div class="w-full py-0.5 font-bold text-gray-600 items-center text-xs flex">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
              </span>
              <span>
                <%= @evaluation.outcome %>
              </span>
            </div>
          </div>
        </div>
        <div class="flex items-center px-4 mt-2 mb-2 text-sm font-bold text-gray-700">
          <scan>Further Notes</scan>
        </div>
        <div class="pt-0.5 w-full pl-16 flex bg-white flex">
          <div class="w-full py-0.5 text-gray-600 text-xs flex">
            <div class="flex w-2/12">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
              </span>
              <span class="font-bold">
                Exclusion:
              </span>
            </div>
            <div class="w-10/12 ml-2" >
              <% if evaluation.exclusion %>
                <%=  evaluation.exclusion_comment %>
              <% else %>
                -- No exclusion required --
              <% end %>
            </div>
          </div>
        </div>
        <div class="pt-0.5 w-full pl-16 flex bg-white flex">
          <div class="w-full py-0.5 text-gray-600 text-xs flex">
            <div class="flex w-2/12 ">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
              </span>
              <span class="font-bold">
                Referral:
              </span>
            </div>
            <div class="w-10/12 ml-2" >
              <% if evaluation.referral %>
                <%=  evaluation.referral_comment %>
              <% else %>
                -- No referral required --
              <% end %>
            </div>
          </div>
        </div>
        <div class="pt-0.5 w-full pl-16 flex bg-white flex">
          <div class="w-full py-0.5 text-gray-600 text-xs flex">
            <div class="flex w-2/12">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
              </span>
              <span class="font-bold">
                Comments:
              </span>
            </div>
            <div class="w-10/12 ml-2" >
              <% if evaluation.outcome_comment.blank? %>
                -- No notable comments --
              <% else %>
                <%=  evaluation.outcome_comment %>
              <% end %>
            </div>
          </div>
        </div>
      </div>
      <div class="flex justify-end text-gray-200 border-t border-gray-100">
        End of Report
      </div>
    </div>
  </div>
  <div data-controller='qrcode' class="opacity-50 footer right">
    <canvas data-qrcode-target="output"><%= qrcode_result %></canvas>
  </div>
  <div class="flex-col items-center justify-center h-40 pb-56 footer left w-90"
         style="background-image: url('https://www.transparenttextures.com/patterns/triangles.png')">
    <%= image_tag signature.image.url, class: "fill-current w-80 h-36 text-gray-500" %>
    <div class="text-sm text-center border-t">
      <%= signature.first_line %>
    </div>
    <% if signature.second_line.present? %>
      <div class="text-sm text-center">
        <%= signature.second_line %>
      </div>
    <% end %>
    <% if signature.third_line.present? %>
      <div class="text-sm text-center">
        <%= signature.third_line %>
      </div>
    <% end %>
    <% if signature.forth_line.present? %>
      <div class="text-sm text-center">
        <%= signature.forth_line %>
      </div>
    <% end %>
  </div>
</div>
