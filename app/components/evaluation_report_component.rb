class EvaluationReportComponent < ViewComponent::Base
  attr_reader :evaluation

  def initialize(evaluation:)
    @evaluation = evaluation
  end

  delegate :clinic, to: :evaluation

  def signoff
    evaluation.lab_test_signoff
  end

  def signature
    evaluation.evaluation_signoff.user.signatures.first
  end

  delegate :patient, to: :evaluation

  def qrcode_result
    "ID:#{patient.identification_number}|Result:#{evaluation.outcome}|Expiry:#{evaluation.medical_expiry_date}"
  end
end
