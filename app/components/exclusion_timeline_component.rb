# frozen_string_literal: true

class ExclusionTimelineComponent < ViewComponent::Base
  attr_accessor :exclusion

  def initialize(data:)
    @exclusion = data
  end

  def edit_link
    Rails.application.routes.url_helpers.edit_exclusion_path(@exclusion.id)
  end

  def svg_icon
    svg = <<~FOO
      <svg class="text-indigo-600 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.618 5.984A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016zM12 9v2m0 4h.01"></path></svg>
    FOO
    svg.html_safe
  end
end
