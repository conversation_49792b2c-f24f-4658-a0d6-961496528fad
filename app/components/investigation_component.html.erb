<div class="mt-4" data-controller="toggle">

  <div class="relative flex items-start">
    <div class="absolute flex items-center h-5">
      <%= text_field_tag "#{ form_builder }[#{ input_checkbox }]", nil,
        id: "#{ form_builder }[#{ input_checkbox }]",
        checked: true,
        type: :checkbox,
        class: "form-checkbox h-4 w-4 text-indigo-600
                transition duration-150 ease-in-out",
                data: {
                  target: "toggle.checkbox",
                  action: "change->toggle#toggle"
                }
              %>
    </div>

    <div class="pl-7 text-sm leading-5">
      <%= label_tag input_checkbox , input_name,
        class: "font-medium text-gray-700" do %>
        <%= title %>
      <% end %>
    </div>


  </div>

  <div data-target='toggle.toggleable' class="pt-4 mt-4 sm:mt-0 sm:col-span-2">
    <div class="max-w-xs rounded-md shadow-sm">
      <%= select_tag "evaluation_form[#{ input_name }]",
        options_from_collection_for_select(select_collection, "id", "result"),
        prompt: "Link a test performed ",
        class: "block form-select w-full text-gray-600 transition duration-150
                ease-in-out sm:text-sm sm:leading-5" %>
    </div>
  </div>

  <div id=<%= "#{ form_builder }_#{ input_name }_error" %> class="mt-4 text-sm text-red-600">
  </div>


</div>

