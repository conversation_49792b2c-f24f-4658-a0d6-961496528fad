class InvestigationComponent < ViewComponent::Base
  attr_reader :title
  attr_reader :select_collection
  attr_reader :input_checkbox
  attr_reader :input_name
  attr_reader :form_builder

  def initialize(form_builder:, input_name:, title:, select_collection:, input_checkbox:)
    @title = title
    @select_collection = select_collection
    @input_checkbox = input_checkbox
    @input_name = input_name
    @form_builder = "evaluation_form"
  end

  def form_object
    form_builder.class.to_s.downcase
  end
end
