<div class="flex-col justify-center bg-white">
  <div class="report h-full flex flex-col ">
    <div class=" flex justify-between w-full items-start">
      <div class="w-40">
        <%= image_tag "Logo.png" %>
      </div>

      <div class="text-right pt-2">
        <div class="text-lg font-bold text-gray-600">
          <%= clinic.clinic_name %>
        </div>
        <div class="text-xs text-gray-500">
          Reg. No. <%= clinic.organisation.registration_number %>
        </div>
        <div class="text-xs text-gray-500">
          <%= clinic.physical_address %>
        </div>
        <% if clinic.physical_address_2.present?  %>
          <div class="text-xs text-gray-500">
            <%= clinic.physical_address_2 %>
          </div>
        <% end %>
        <div class="text-xs text-gray-500">
          Tel: <%= clinic.phone_number %>
        </div>
        <div class="text-xs text-gray-500 ">
          <%= clinic.organisation.email_address %>
        </div>
        <div class="text-xs text-gray-500 ">
          <%= clinic.organisation.web_address %>
        </div>
      </div>
    </div>

    <div class="text-2xl text-gray-700 font-bold
                  my-6 flex justify-center">
      Lab Test Results
    </div>

    <div
      style="background-image: url('https://www.transparenttextures.com/patterns/black-thread-light.png')"
      class="px-4 py-4  border-t border-b border-gray-200
               grid grid-flow-col grid-cols-2 grid-rows-4 col-gap-16 gap-1 ">
      <div class="flex justify-between text-left items-baseline">
        <div class="text-gray-700 text-sm">Patient</div>
        <div class="text-gray-700 font-bold text-md truncate"><%= patient.name.full %></div>
      </div>

      <div class="flex justify-between text-left items-baseline ">
        <div class="text-gray-700 text-sm">ID No</div>
        <div class="text-gray-700 font-bold text-md"><%= patient.identification_number %></div>
      </div>
      <div class="flex justify-between items-baseline">
        <div class="text-gray-700 text-sm">Date of Birth</div>
        <div class="text-gray-700 font-bold text-md"><%= patient.dob %></div>
      </div>
      <div class="flex justify-between items-baseline">
        <div class="text-gray-700 text-sm">Gender</div>
        <div class="text-gray-700 font-bold text-md">
          <% if patient.gender.present? %>
            <%= patient.gender.capitalize %>
          <% else %>
            Not Specified
          <% end %>
        </div>
      </div>
      <div class="flex justify-between items-baseline">
        <div class="text-gray-700 text-sm">Employer</div>
        <div class="text-gray-700 font-bold text-md">
          <% if patient.employments.empty? %>
            Not Specified
          <% else %>
            <%= patient.employments.last.company.name %>
          <% end %>
        </div>
      </div>
      <div class="flex justify-between items-baseline">
        <div class="text-gray-700 text-sm">Department</div>
        <div class="text-gray-700 font-bold text-md">
          <% if patient.employments.empty? %>
            Not Specified
          <% else %>
            <%= patient.employments.last.department %>
          <% end %>
        </div>
      </div>
      <div class="flex justify-between items-baseline">
        <div class="text-gray-700 text-sm">Position</div>
        <div class="text-gray-700 font-bold text-md">
          <% if patient.employments.empty? %>
            Not Specified
          <% else %>
            <%= patient.employments.last.position %>
          <% end %>
        </div>
      </div>
      <div class="flex justify-between items-baseline">
        <div class="text-gray-700 text-sm">Date of Tests Performed</div>
        <div class="text-gray-700 font-bold text-md"><%= lab_test.date_performed || Date.today %></div>
      </div>
    </div>

    <div class="items-center flex mt-8 mb-4 px-4 font-bold text-xl text-gray-700">
      <scan>Tests Performed</scan>
    </div>
    <div class="mb-4">
      <% if lab_test.cannabis != "Not Performed" %>
        <div class="py-1 w-full pl-16 flex odd:bg-gray-100 bg-white flex items-center">
          <div class="w-2/5 py-1 font-bold text-gray-600 items-center flex">
            <span>
              <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
            </span>
            <span>
              Cannabis
            </span>
          </div>
          <div class="w-4/5 py-1 font-bold">
            <span class="<%= lab_test.cannabis == "Positive" ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
              <%= lab_test.cannabis %>
            </span>
          </div>
        </div>
      <% end %>

      <% if lab_test.gamma != "Not Performed" %>
        <div class="py-1 w-full pl-16 flex odd:bg-gray-100 bg-white flex items-center">
          <div class="w-2/5 py-1 font-bold text-gray-600 items-center flex">
            <span>
              <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
            </span>
            <span>
              Gamma GT
            </span>
          </div>
          <div class="w-4/5 py-1 font-bold">
            <span class="<%= lab_test.gamma == "Positive" ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
              <%= lab_test.gamma %>
            </span>
          </div>
        </div>
      <% end %>

      <% if lab_test.six_panel != "Not Performed" %>
        <div class="py-1 w-full pl-16 flex odd:bg-gray-100 bg-white flex items-center">
          <div class="w-2/5 py-1 font-bold text-gray-600 items-center flex">
            <span>
              <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
            </span>
            <span>
              Six Panel Drug Test
            </span>
          </div>
          <div class="w-4/5 py-1 font-bold">
            <span class="<%= lab_test.six_panel == "Positive" ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
              <%= lab_test.six_panel %>
            </span>
          </div>
        </div>
      <% end %>

      <% if lab_test.ast != "Not Performed" %>
        <div class="py-1 w-full pl-16 flex odd:bg-gray-100 bg-white flex items-center">
          <div class="w-2/5 py-1 font-bold text-gray-600 items-center flex">
            <span>
              <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
            </span>
            <span>
              AST
            </span>
          </div>
          <div class="w-4/5 py-1 font-bold">
            <span class="<%= lab_test.ast == "Positive" ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
              <%= lab_test.ast %>
            </span>
          </div>
        </div>
      <% end %>

      <% if lab_test.fbc != "Not Performed" %>
        <div class="py-1 w-full pl-16 flex odd:bg-gray-100 bg-white flex items-center">
          <div class="w-2/5 py-1 font-bold text-gray-600 items-center flex">
            <span>
              <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
            </span>
            <span>
              FBC
            </span>
          </div>
          <div class="w-4/5 py-1 font-bold">
            <span class="<%= lab_test.fbc == "Positive" ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
              <%= lab_test.fbc %>
            </span>
          </div>
        </div>
      <% end %>

      <% if lab_test.hiv != "Not Performed" %>
        <div class="py-1 w-full pl-16 flex odd:bg-gray-100 bg-white items-center">
          <div class="w-2/5 py-1 font-bold text-gray-600 items-center flex">
            <span>
              <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
            </span>
            <span>
              HIV
            </span>
          </div>
          <div class="w-4/5 py-1 font-bold">
            <span class="<%= lab_test.hiv == "Positive" ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
              <%= lab_test.hiv %>
            </span>
          </div>
        </div>
      <% end %>

    </div>
    <div class="border-t border-gray-100 flex justify-end text-gray-200">
      End of Report
    </div>
  </div>
</div>

<div data-controller='qrcode' class="opacity-50 footer right">
  <canvas data-target="qrcode.output"><%= qrcode_result %></canvas>
</div>

<div class="pb-56 flex-col items-center justify-center footer left w-90 h-40"
         style="background-image: url('https://www.transparenttextures.com/patterns/triangles.png')">
     <% if signature.present? %>
       <div class="text-center ">
         <%= image_tag signature.image.url, class: "fill-current w-80 h-36 text-gray-500" %>
       </div>
     <% else %>
       <div class="text-center mt-24">
         No signature for this user
       </div>
     <% end%>


     <% if signature.present? %>
       <div class="border-t text-center">
         <%= signature.present? ? signature.first_line : 'Not Specified'%>
       </div>
       <% if signature.second_line.present? %>
         <div class="text-center">
           <%= signature.second_line %>
         </div>
       <% end %>
       <% if signature.third_line.present? %>
         <div class="text-center">
           <%= signature.third_line %>
         </div>
       <% end %>
       <% if signature.forth_line.present? %>
         <div class="text-center">
           <%= signature.forth_line %>
         </div>
       <% end %>
     <% end %>
</div>

</div>
