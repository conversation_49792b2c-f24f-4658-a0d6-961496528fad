class LabTestReportComponent < ViewComponent::Base
  attr_reader :lab_test

  def initialize(lab_test:)
    @lab_test = lab_test
  end

  def signoff
    lab_test.lab_test_signoff
  end

  def signature
    lab_test.lab_test_signoff.user.signatures.first
  end

  delegate :patient, to: :lab_test
  delegate :clinic, to: :lab_test

  def qrcode_result
    result = "ID:#{patient.identification_number}"

    if lab_test.cannabis != "Not Performed"
      result << "|Cannabis=#{lab_test.cannabis}"
    end

    if lab_test.gamma != "Not Performed"
      result << "|GammaGT=#{lab_test.gamma}"
    end

    if lab_test.ast != "Not Performed"
      result << "|AST=#{lab_test.ast}"
    end

    if lab_test.fbc != "Not Performed"
      result << "|FBC=#{lab_test.fbc}"
    end

    if lab_test.six_panel != "Not Performed"
      result << "|6Panel=#{lab_test.six_panel}"
    end

    if lab_test.hiv != "Not Performed"
      result << "|HIV=#{lab_test.hiv}"
    end

    result
  end
end
