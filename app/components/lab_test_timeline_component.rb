# frozen_string_literal: true

class LabTestTimelineComponent < ViewComponent::Base
  attr_reader :labs

  def initialize(labs:)
    @labs = labs
  end

  def edit_link
    Rails.application.routes.url_helpers.edit_lab_test_path(labs.id)
  end

  def svg_icon
    svg = <<~FOO
      <svg class="text-indigo-600 w-6"fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor"><path d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path></svg>
    FOO
    svg.html_safe
  end
end
