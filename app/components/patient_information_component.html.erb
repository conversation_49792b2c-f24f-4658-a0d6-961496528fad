  <div class="bg-gray-800 shadow-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-10 grid 
                grid-cols-6 row-gap-8 sm:grid-cols-12 text-white">
      <div class="col-span-5">
        <div class="flex">
          <div>
            <div class="flex items-center">
              <h1 class="text-xl font-semibold">
                <%= patient.name.full %>
              </h1>
              <div class="ml-4 text-blue-200">
              </div>
            </div>
            <span class="flex text-gray-400 items-center">
              <svg fill="none" viewBox="0 0 24 24" stroke="currentColor" class="finger-print mr-2 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 11c0 3.517-1.009 6.799-2.753 9.571m-3.44-2.04l.054-.09A13.916 13.916 0 008 11a4 4 0 118 0c0 1.017-.07 2.019-.203 3m-2.118 6.844A21.88 21.88 0 0015.171 17m3.839 1.132c.645-2.266.99-4.659.99-7.132A8 8 0 008 4.07M3 15.364c.64-1.319 1-2.8 1-4.364 0-1.457.39-2.823 1.07-4"></path></svg>
              <div class="text-sm text-gray-300">
                <%= patient.identification_number %>
              </div>
            </span>
            <div class="mt-4">
              <%= link_to "Edit patient details", edit_patient_path( patient ),
              class: "inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs
              leading-4 font-medium rounded text-white bg-indigo-600 hover:bg-indigo-500
              focus:outline-none focus:border-indigo-700 focus:shadow-outline-indigo
              active:bg-indigo-700 transition ease-in-out duration-150" %>
            </div>
          </div>
        </div>
      </div>
      <div class="col-span-3 text-center text-sm">
        <div class="flex items-center mt-2">
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor" class="mr-2 text-gray-400 device-mobile w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path></svg>
          <%= patient.phone_number %>
        </div>
        <div class="flex items-center mt-2">
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor" class="text-gray-400 mr-2 mail w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg>
          <%= patient.email %>
        </div>
      </div>
    </div>
  </div>
