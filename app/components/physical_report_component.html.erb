<div class="flex-col justify-center bg-white">
  <div class="flex flex-col h-full report ">
    <div class="flex items-start justify-between w-full ">
      <div class="w-40">
        <%= image_tag "Logo.png" %>
      </div>
      <div class="pt-2 text-right">
        <div class="text-lg font-bold text-gray-600">
          <%= clinic.clinic_name %>
        </div>
        <div class="text-xs text-gray-500">
          Reg. No. <%= clinic.organisation.registration_number %>
        </div>
        <div class="text-xs text-gray-500">
          <%= clinic.physical_address %>
        </div>
        <% if clinic.physical_address_2.present?  %>
          <div class="text-xs text-gray-500">
            <%= clinic.physical_address_2 %>
          </div>
        <% end %>
        <div class="text-xs text-gray-500">
          Tel: <%= clinic.phone_number %>
        </div>
        <div class="text-xs text-gray-500 ">
          <%= clinic.organisation.email_address %>
        </div>
        <div class="text-xs text-gray-500 ">
          <%= clinic.organisation.web_address %>
        </div>
      </div>
    </div>
    <div class="flex justify-center my-3 text-2xl font-bold text-gray-700">
      Physical Assessment Report 
    </div>
    <div
      style="background-image: url('https://www.transparenttextures.com/patterns/black-thread-light.png')"
      class="grid grid-flow-col grid-cols-2 grid-rows-4 col-gap-24 px-4 py-2 border-t border-b border-gray-200 gap-x-3 gap-y-1">
      <div class="flex items-baseline justify-between text-left">
        <div class="text-sm text-gray-700">Patient</div>
        <div class="font-bold text-gray-700 text-md truncate"><%= patient.name.full %></div>
      </div>
      <div class="flex items-baseline justify-between text-left ">
        <div class="text-sm text-gray-700">ID No</div>
        <div class="font-bold text-gray-700 text-md"><%= patient.identification_number %></div>
      </div>
      <div class="flex items-baseline justify-between">
        <div class="text-sm text-gray-700">Date of Birth</div>
        <div class="font-bold text-gray-700 text-md"><%= patient.dob %></div>
      </div>
      <div class="flex items-baseline justify-between">
        <div class="text-sm text-gray-700">Gender</div>
        <div class="font-bold text-gray-700 text-md">
          <% if patient.gender.present? %>
            <%= patient.gender.capitalize %>
          <% else %>
            Not Specified
          <% end %>
        </div>
      </div>
      <div class="flex items-baseline justify-between">
        <div class="text-sm text-gray-700">Employer</div>
        <div class="font-bold text-gray-700 text-md">
          <% if patient.employments.empty? %>
            Not Specified
          <% else %>
            <%= patient.employments.last.company.name %>
          <% end %>
        </div>
      </div>
      <div class="flex items-baseline justify-between">
        <div class="text-sm text-gray-700">Department</div>
        <div class="font-bold text-gray-700 text-md">
          <% if patient.employments.empty? %>
            Not Specified
          <% else %>
            <%= patient.employments.last.department %>
          <% end %>
        </div>
      </div>
      <div class="flex items-baseline justify-between">
        <div class="text-sm text-gray-700">Position</div>
        <div class="font-bold text-gray-700 text-md">
          <% if patient.employments.empty? %>
            Not Specified
          <% else %>
            <%= patient.employments.last.position %>
          <% end %>
        </div>
      </div>
      <div class="flex items-baseline justify-between">
        <div class="text-sm text-gray-700">Date of Tests Performed</div>
        <div class="font-bold text-gray-700 text-md"><%= physical.date_performed || Date.today %></div>
      </div>
    </div>
    <div class="flex items-center px-4 mt-2 mb-2 text-sm font-bold text-gray-700">
      <span>Vitals</span>
    </div>
    <div class="py-0.5 w-full pl-16  odd:bg-gray-100 bg-white flex items-center">
      <div class="w-full py-0.5 font-bold text-gray-600 items-center text-xs flex">
        <span>
          <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
        </span>
        <span>
          Height: 
        </span>
        <span class="ml-1 text-gray-600">
          <%= physical.height %>
        </span>
      </div>
      <div class="w-full py-0.5 font-bold text-gray-600 items-center text-xs flex">
        <span>
          <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
        </span>
        <span>
          Weight: <%= physical.weight %>
        </span>
      </div>
      <div class="w-full py-0.5 font-bold text-gray-600 items-center text-xs flex">
        <span>
          <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
        </span>
        <span>
          Blood Pressure: <%= physical.blood_pressure %>
        </span>
      </div>
    </div>
    <div class="py-0.5 w-full pl-16  odd:bg-gray-100 bg-white flex items-center">
      <div class="w-full py-0.5 font-bold text-gray-600 items-center text-xs flex">
        <span>
          <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
        </span>
        <span>
          Pulse: 
        </span>
        <span class="ml-1 text-gray-600">
          <%= physical.pulse %>
        </span>
      </div>
      <div class="w-full py-0.5 font-bold text-gray-600 items-center text-xs flex">
        <span>
          <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
        </span>
        <span>
          Blood Sugar: <%= physical.blood_sugar %>
        </span>
      </div>
      <div class="w-full py-0.5 font-bold text-gray-600 items-center text-xs flex">
        <span>
          <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
        </span>
        <span>
          BMI: <%= number_with_precision((physical.weight.to_d/physical.height.to_d/physical.height.to_d)*10000,precision: 2) %>
        </span>
      </div>
    </div>
    <div class="py-0.5 mb-4 w-full pl-16  odd:bg-gray-100 bg-white flex items-center">
      <div class="w-full py-0.5 font-bold text-gray-600 items-center text-xs flex">
        <span>
          <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-4 " %>
        </span>
        <span>
          General Appearance: 
        </span>
        <span class="ml-1 text-gray-600">
          <%= physical.general_appearance %>
        </span>
      </div>
    </div>

    <div class="flex text-xs ">
      <div class="w-1/3 ">
        <div class="flex items-start px-2 mt-2 mb-2 font-bold text-gray-700 text-md">
          <span>Urine Test Detected</span>
        </div>
        <div class="mb-4">
          <% if physical.nad_test == true %>

          <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
            <div class="flex items-center py-1 text-xs font-medium text-gray-600">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
              </span>
              <span>
                No Abnormality 
              </span>
            </div>
          </div>
        <% end  %>

          <% if physical.nad_test == false %>

          <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
            <div class="flex items-center py-1 text-xs font-medium text-gray-600">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
              </span>
              <span>
                Leucocytes 
              </span>
            </div>
            <div class="text-xs font-bold uppercase">
              <span class="<%= physical.leucocytes_test == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                <%= physical.leucocytes_test == true ? "Yes" : "No"%>
              </span>
            </div>
          </div>

        <% end  %>

          <% if physical.nad_test == false %>

          <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
            <div class="flex items-center py-1 text-xs font-medium text-gray-600">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
              </span>
              <span>
                Nitrite
              </span>
            </div>
            <div class="text-xs font-bold uppercase">
              <span class="<%= physical.nitrite_test == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                <%= physical.nitrite_test == true ? "Yes" : "No"%>
              </span>
            </div>
          </div>
        <% end  %>

          <% if physical.nad_test == false %>

          <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
            <div class="flex items-center py-1 text-xs font-medium text-gray-600">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
              </span>
              <span>
                Blood Sugar
              </span>
            </div>
            <div class="text-xs font-bold uppercase">
              <span class="<%= physical.blood_test == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                <%= physical.blood_test == true ? "Yes" : "No"%>
              </span>
            </div>
          </div>
        <% end  %>

          <% if physical.nad_test == false %>

          <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
            <div class="flex items-center py-1 text-xs font-medium text-gray-600">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
              </span>
              <span>
                Protein 
              </span>
            </div>
            <div class="text-xs font-bold uppercase">
              <span class="<%= physical.protein_test == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                <%= physical.protein_test == true ? "Yes" : "No"%>
              </span>
            </div>
          </div>

        <% end  %>
          <% if physical.nad_test == false %>

          <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
            <div class="flex items-center py-1 text-xs font-medium text-gray-600">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
              </span>
              <span>
                Glucose 
              </span>
            </div>
            <div class="text-xs font-bold uppercase">
              <span class="<%= physical.glucose_test == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                <%= physical.glucose_test == true ? "Yes" : "No"%>
              </span>
            </div>
          </div>
        <% end  %>

        </div>
      </div>
      <div class="w-1/3 mx-2 ">
        <div class="flex items-start px-2 mt-2 mb-2 font-bold text-gray-700 text-md">
          <span>Systemic Examinations</span>
        </div>
        <div class="mb-4">
          <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
            <div class="flex items-center py-1 text-xs font-medium text-gray-600">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
              </span>
              <span>
                Peripheral Signs 
              </span>
            </div>
            <div class="text-xs font-bold uppercase">
              <span class="<%= physical.peripheral_exam == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                <%= physical.peripheral_exam == true ? "Yes" : "No"%>
              </span>
            </div>
          </div>
          <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
            <div class="flex items-center py-1 text-xs font-medium text-gray-600">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
              </span>
              <span>
                Skin/Appendages 
              </span>
            </div>
            <div class="text-xs font-bold uppercase">
              <span class="<%= physical.skin_exam == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                <%= physical.skin_exam == true ? "Yes" : "No"%>
              </span>
            </div>
          </div>
          <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
            <div class="flex items-center py-1 text-xs font-medium text-gray-600">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
              </span>
              <span>
                CVS 
              </span>
            </div>
            <div class="text-xs font-bold uppercase">
              <span class="<%= physical.cvs_exam == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                <%= physical.cvs_exam == true ? "Yes" : "No"%>
              </span>
            </div>
          </div>
          <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
            <div class="flex items-center py-1 text-xs font-medium text-gray-600">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
              </span>
              <span>
                ENT 
              </span>
            </div>
            <div class="text-xs font-bold uppercase">
              <span class="<%= physical.ent_exam == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                <%= physical.ent_exam == true ? "Yes" : "No"%>
              </span>
            </div>
          </div>
          <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
            <div class="flex items-center py-1 text-xs font-medium text-gray-600">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
              </span>
              <span>
                Chest 
              </span>
            </div>
            <div class="text-xs font-bold uppercase">
              <span class="<%= physical.chest_exam == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                <%= physical.chest_exam == true ? "Yes" : "No"%>
              </span>
            </div>
          </div>
          <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
            <div class="flex items-center py-1 text-xs font-medium text-gray-600">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
              </span>
              <span>
                Gastro 
              </span>
            </div>
            <div class="text-xs font-bold uppercase">
              <span class="<%= physical.gastro_exam == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                <%= physical.gastro_exam == true ? "Yes" : "No"%>
              </span>
            </div>
          </div>
          <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
            <div class="flex items-center py-1 text-xs font-medium text-gray-600">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
              </span>
              <span>
                Urinary 
              </span>
            </div>
            <div class="text-xs font-bold uppercase">
              <span class="<%= physical.urinary_exam == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                <%= physical.urinary_exam == true ? "Yes" : "No"%>
              </span>
            </div>
          </div>
          <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
            <div class="flex items-center py-1 text-xs font-medium text-gray-600">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
              </span>
              <span>
                Musculo Skeletal
              </span>
            </div>
            <div class="text-xs font-bold uppercase">
              <span class="<%= physical.musculo_exam == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                <%= physical.musculo_exam == true ? "Yes" : "No"%>
              </span>
            </div>
          </div>
          <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
            <div class="flex items-center py-1 text-xs font-medium text-gray-600">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
              </span>
              <span>
                CNS 
              </span>
            </div>
            <div class="text-xs font-bold uppercase">
              <span class="<%= physical.cns_exam == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                <%= physical.cns_exam == true ? "Yes" : "No"%>
              </span>
            </div>
          </div>
          <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
            <div class="flex items-center py-1 text-xs font-medium text-gray-600">
              <span>
                <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
              </span>
              <span>
                Endocrine 
              </span>
            </div>
            <div class="text-xs font-bold uppercase">
              <span class="<%= physical.endocrine_exam == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                <%= physical.endocrine_exam == true ? "Yes" : "No"%>
              </div>
            </div>
          </div>
        </div>
        <div class="w-1/3">
          <div class="flex items-start px-2 mt-2 mb-2 font-bold text-gray-700 text-md">
            <span>Chronic Diseases detected</span>
          </div>
          <div class="mb-4">
            <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
              <div class="flex items-center py-1 text-xs font-medium text-gray-600">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
                </span>
                <span>
                  Hypertension 
                </span>
              </div>
              <div class="text-xs font-bold uppercase">
                <span class="<%= physical.hypertension_chronic == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                  <%= physical.hypertension_chronic == true ? "Yes" : "No"   %>
                </span>
              </div>
            </div>
            <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
              <div class="flex items-center py-1 text-xs font-medium text-gray-600">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
                </span>
                <span>
                  Asthma 
                </span>
              </div>
              <div class="text-xs font-bold uppercase">
                <span class="<%= physical.asthma_chronic == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                  <%= physical.asthma_chronic == true ? "Yes" : "No"  %>
                </span>
              </div>
            </div>
            <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
              <div class="flex items-center py-1 text-xs font-medium text-gray-600">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
                </span>
                <span>
                  Epilepsy 
                </span>
              </div>
              <div class="text-xs font-bold uppercase">
                <span class="<%= physical.epilepsy_chronic == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                  <%= physical.epilepsy_chronic == true ? "Yes" : "No"%>
                </span>
              </div>
            </div>
            <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
              <div class="flex items-center py-1 text-xs font-medium text-gray-600">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
                </span>
                <span>
                  Mental
                </span>
              </div>
              <div class="text-xs font-bold uppercase">
                <span class="<%= physical.mental_chronic == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                  <%= physical.mental_chronic == true ? "Yes" : "No"%>
                </span>
              </div>
            </div>
            <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
              <div class="flex items-center py-1 text-xs font-medium text-gray-600">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
                </span>
                <span>
                  Obesity
                </span>
              </div>
              <div class="text-xs font-bold uppercase">
                <span class="<%= physical.obesity_chronic == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                  <%= physical.obesity_chronic == true ? "Yes" : "No"%>
                </span>
              </div>
            </div>
            <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
              <div class="flex items-center py-1 text-xs font-medium text-gray-600">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
                </span>
                <span>
                  Diabetes 
                </span>
              </div>
              <div class="text-xs font-bold uppercase">
                <span class="<%= physical.diabetes_chronic == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                  <%= physical.diabetes_chronic == true ? "Yes" : "No"%>
                </span>
              </div>
            </div>
            <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
              <div class="flex items-center py-1 text-xs font-medium text-gray-600">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
                </span>
                <span>
                  Drug/Alcohol 
                </span>
              </div>
              <div class="text-xs font-bold uppercase">
                <span class="<%= physical.drug_chronic == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                  <%= physical.drug_chronic == true ? "Yes" : "No"%>
                </span>
              </div>
            </div>
            <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
              <div class="flex items-center py-1 text-xs font-medium text-gray-600">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
                </span>
                <span>
                  Thyroid 
                </span>
              </div>
              <div class="text-xs font-bold uppercase">
                <span class="<%= physical.thyroid_chronic == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                  <%= physical.thyroid_chronic == true ? "Yes" : "No"%>
                </span>
              </div>
            </div>
            <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
              <div class="flex items-center py-1 text-xs font-medium text-gray-600">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
                </span>
                <span>
                  COPD 
                </span>
              </div>
              <div class="text-xs font-bold uppercase">
                <span class="<%= physical.copd_chronic == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                  <%= physical.copd_chronic == true ? "Yes" : "No"%>
                </span>
              </div>
            </div>
            <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
              <div class="flex items-center py-1 text-xs font-medium text-gray-600">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
                </span>
                <span>
                  Cardiac 
                </span>
              </div>
              <div class="text-xs font-bold uppercase">
                <span class="<%= physical.cardiac_chronic == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                  <%= physical.cardiac_chronic == true ? "Yes" : "No"%>
                </span>
              </div>
            </div>
            <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
              <div class="flex items-center py-1 text-xs font-medium text-gray-600">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
                </span>
                <span>
                  Prosthesis 
                </span>
              </div>
              <div class="text-xs font-bold uppercase">
                <span class="<%= physical.prosthesis_chronic == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                  <%= physical.prosthesis_chronic == true ? "Yes" : "No"%>
                </span>
              </div>
            </div>
            <div class="flex items-center justify-between w-full px-1 bg-white odd:bg-gray-100">
              <div class="flex items-center py-1 text-xs font-medium text-gray-600">
                <span>
                  <%= inline_svg_tag "chevron-right.svg", class: "text-gray-500 w-4 mr-2 " %>
                </span>
                <span>
                  Arthritis 
                </span>
              </div>
              <div class="text-xs font-bold uppercase">
                <span class="<%= physical.arthrithis_chronic == true ? "text-red-700 bg-red-100" : "text-green-700 bg-green-100" %> rounded-full px-3 py-1">
                  <%= physical.arthrithis_chronic == true ? "Yes" : "No"%>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="flex justify-end text-gray-200 border-t border-gray-100">
        End of Report
      </div>
    </div>
  </div>
  <div data-controller='qrcode' class="opacity-50 footer right">
    <canvas data-target="qrcode.output"><%= qrcode_result %></canvas>
  </div>
  <div class="flex-col items-center justify-center w-90 h-40 pb-56 footer left"
         style="background-image: url('https://www.transparenttextures.com/patterns/triangles.png')">
    <% if signature.present? %>
      <div class="text-center ">
        <%= image_tag signature.image.url, class: "fill-current w-80 h-36 text-gray-500" %>
      </div>
    <% else %>
      <div class="mt-24 text-center">
        No signature for this user
      </div>
    <% end%>
    <% if signature.present? %>
      <div class="text-center border-t">
        <%= signature.present? ? signature.first_line : 'Not Specified'%>
      </div>
      <% if signature.second_line.present? %>
        <div class="text-center">
          <%= signature.second_line %>
        </div>
      <% end %>
      <% if signature.third_line.present? %>
        <div class="text-center">
          <%= signature.third_line %>
        </div>
      <% end %>
      <% if signature.forth_line.present? %>
        <div class="text-center">
          <%= signature.forth_line %>
        </div>
      <% end %>
    <% end %>
  </div>
</div>
