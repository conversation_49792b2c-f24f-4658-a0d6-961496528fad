# frozen_string_literal: true

class PhysicalReportComponent < ViewComponent::Base
  attr_reader :physical

  def initialize(physical:)
    @physical = physical
  end

  delegate :clinic, to: :physical

  def signoff
    physical.physical_signoff
  end

  def signature
    physical.physical_signoff.user.signatures.first
  end

  delegate :patient, to: :physical

  def qrcode_result
    result = "ID:#{patient.identification_number}"
    result << "|Height=#{physical.height}"
    result << "|Weight=#{physical.weight}"
    result << "|BP=#{physical.blood_pressure}"
    result << "|Pulse=#{physical.pulse}"
    result << "|BloodSugar=#{physical.blood_sugar}"

    result
  end
end
