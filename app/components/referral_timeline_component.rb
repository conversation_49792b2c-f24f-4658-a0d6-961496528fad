# frozen_string_literal: true

class ReferralTimelineComponent < ViewComponent::Base
  attr_accessor :referral

  def initialize(data:)
    @referral = data
  end

  def edit_link
    Rails.application.routes.url_helpers.edit_referral_path(@referral.id)
  end

  def svg_icon
    svg = <<~FOO
      <svg class="text-indigo-600 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path></svg>
    FOO
    svg.html_safe
  end
end
