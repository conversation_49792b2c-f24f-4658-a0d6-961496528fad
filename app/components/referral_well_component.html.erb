<%= component_controller do %>

<%= @debug %>

  <div class="max-w-xs rounded-md shadow-sm">

    <select name="select"
            id="jacky"
            class="block form-select w-full text-gray-600 transition
            duration-150 ease-in-out sm:text-sm sm:leading-5"
            data-controller="stimulus-reflex"
            data-action="change->stimulus-reflex#__perform"
            data-reflex="change->ReferralWellComponent#add"
            data-key=<%= key %>
          >
            <option value=>Select a referral</option>
          <% @model.each do |record|%>
            <option value="<%= record.id %>"><%= record.name %></option>
          <% end %>
    </select>

    <% if @selection.count > 0 %>
      <% @selection.each do |record|%>
        <div class="relative mt-6 bg-gray-100 sm:rounded-lg">
          <div id="record-referral-<%= record.id %>"
          class="hover:text-gray-900 cursor-pointer absolute
          top-4 right-4 text-sm leading-6 font-medium text-gray-600"
          data-record="<%= record.id %>"
          data-controller="stimulus-reflex"
          data-action="click->stimulus-reflex#__perform"
          data-reflex="click->ReferralWellComponent#remove"
          data-key=<%= key %>
          >
          <svg class="fill-current w-6 h-6"
               fill="none"
               stroke-linecap="round"
               stroke-linejoin="round"
               stroke-width="2"
               viewBox="0 0 24 24"
               stroke="currentColor">
            <path d="M6 18L18 6M6 6l12 12"></path>
          </svg>
          </div>

          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              <%= record.specialist_type %>
            </h3>
            <div class="mt-2 max-w-xl text-sm leading-5 text-gray-500">
              <p>
              <%= record.note %>
              </p>
            </div>
          </div>
        </div>

        <%= hidden_field_tag "evaluation_form[referrals][#{ SecureRandom.hex}][id]", record.id %>

      <% end %>
    <% end %>

  </div>
<% end %>
