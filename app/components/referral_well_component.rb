# frozen_string_literal: true

class ReferralWellComponent < ViewComponentReflex::Component
  attr_reader :model
  attr_reader :selection

  def initialize(model:)
    @model = model
    @selection = []
  end

  def add
    return if element.value.blank?
    record = @model.detect { |e| e.id == element.value.to_i }
    @selection << record
  end

  def remove
    @selection = @selection.select { |e| e.id != element.dataset["record"].to_i }
  end
end
