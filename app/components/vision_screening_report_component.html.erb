<div class="flex-col justify-center bg-white">
  <div class="report h-full flex flex-col ">
    <div class=" flex justify-between w-full items-start">
      <div class="w-40">
        <%= image_tag "Logo.png" %>
      </div>

      <div class="text-right pt-2">
        <div class="text-lg font-bold text-gray-600">
          <%= clinic.clinic_name %>
        </div>
        <div class="text-xs text-gray-500">
          Reg. No. <%= clinic.organisation.registration_number %>
        </div>
        <div class="text-xs text-gray-500">
          <%= clinic.physical_address %>
        </div>
        <% if clinic.physical_address_2.present?  %>
          <div class="text-xs text-gray-500">
            <%= clinic.physical_address_2 %>
          </div>
        <% end %>
        <div class="text-xs text-gray-500">
          Tel: <%= clinic.phone_number %>
        </div>
        <div class="text-xs text-gray-500 ">
          <%= clinic.organisation.email_address %>
        </div>
        <div class="text-xs text-gray-500 ">
          <%= clinic.organisation.web_address %>
        </div>
      </div>
    </div>

    <div class="text-2xl text-gray-700 font-bold
    my-6 flex justify-center">
      Vision Screening Results
    </div>

    <div
      style="background-image: url('https://www.transparenttextures.com/patterns/black-thread-light.png')"
      class="px-4 py-4  border-t border-b border-gray-200
      grid grid-flow-col grid-cols-2 grid-rows-4 col-gap-16 gap-1 ">
      <div class="flex justify-between text-left items-baseline">
        <div class="text-gray-700 text-sm">Patient</div>
        <div class="text-gray-700 font-bold text-md truncate"><%= patient.name.full %></div>
      </div>

      <div class="flex justify-between text-left items-baseline ">
        <div class="text-gray-700 text-sm">ID No</div>
        <div class="text-gray-700 font-bold text-md"><%= patient.identification_number %></div>
      </div>
      <div class="flex justify-between items-baseline">
        <div class="text-gray-700 text-sm">Date of Birth</div>
        <div class="text-gray-700 font-bold text-md"><%= patient.dob %></div>
      </div>
      <div class="flex justify-between items-baseline">
        <div class="text-gray-700 text-sm">Gender</div>
        <div class="text-gray-700 font-bold text-md"><%= patient.gender.capitalize %></div>
      </div>
      <div class="flex justify-between items-baseline">
        <div class="text-gray-700 text-sm">Employer</div>
        <div class="text-gray-700 font-bold text-md">
          <% if patient.employments.empty? %>
            Not Specified
          <% else %>
            <%= patient.employments.last.company.name %>
          <% end %>
        </div>
      </div>
      <div class="flex justify-between items-baseline">
        <div class="text-gray-700 text-sm">Department</div>
        <div class="text-gray-700 font-bold text-md">
          <% if patient.employments.empty? %>
            Not Specified
          <% else %>
            <%= patient.employments.last.department %>
          <% end %>
        </div>
      </div>
      <div class="flex justify-between items-baseline">
        <div class="text-gray-700 text-sm">Position</div>
        <div class="text-gray-700 font-bold text-md">
          <% if patient.employments.empty? %>
            Not Specified
          <% else %>
            <%= patient.employments.last.position %>
          <% end %>
        </div>
      </div>
      <div class="flex justify-between items-baseline">
        <div class="text-gray-700 text-sm">Date of Test Performed</div>
        <div class="text-gray-700 font-bold text-md"><%= screening.date_of_screening || Date.today %></div>
      </div>
    </div>

    <div class="items-center flex mt-8 mb-4 px-4 font-bold text-xl text-gray-700">
      <scan>Tests Performed</scan>
    </div>
    <div class="mb-2">

      <div class="flex px-4 py-5 sm:px-6">
        <dt class="w-1/3 text-sm leading-5 font-medium text-gray-500">
        Left Eye
        </dt>
        <dd class="w-2/3 mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
        <ul class="max-w-md border border-gray-200 rounded-md">

          <li class="border-t border-gray-200 first:border-t-0 pl-3 pr-4 py-1 flex items-center justify-between text-sm leading-5">
            <div class="flex-shrink-0 h-5 w-5 text-green-400 flex-1 flex items-center">
              <span class="text-xs text-gray-900 ml-2 flex-1 w-0 truncate">
                Actual Horizontal Total Field
              </span>
            </div>
            <div class="ml-4 flex-shrink-0">
              <div class="uppercase font-medium transition duration-150 ease-in-out">
                <%= screening.total_left %>
              </div>
            </div>
          </li>
          <li class="border-t border-gray-200 first:border-t-0 pl-3 pr-4 py-1 flex items-center justify-between text-sm leading-5">
            <div class="flex-shrink-0 h-5 w-5 text-green-400 flex-1 flex items-center">
              <span class="text-gray-900 text-xs ml-2 flex-1 w-0 truncate">
                Actual Horizontal Temporal Field
              </span>
            </div>
            <div class="ml-4 flex-shrink-0">
              <div class="uppercase font-medium transition duration-150 ease-in-out">
                <%= screening.temporal_left %>
              </div>
            </div>
          </li>
          <li class="border-t border-gray-200 first:border-t-0 pl-3 pr-4 py-1 flex items-center justify-between text-sm leading-5">
            <div class="flex-shrink-0 h-5 w-5 text-green-400 flex-1 flex items-center">
              <span class="text-xs text-gray-900 ml-2 flex-1 w-0 truncate">
                Snellen in Meters without glasses
              </span>
            </div>
            <div class="ml-4 flex-shrink-0">
              <div class="uppercase font-medium transition duration-150 ease-in-out">
                <%= screening.snellen_left_eye_without_glasses %>
              </div>
            </div>
          </li>
          <li class="border-t border-gray-200 first:border-t-0 pl-3 pr-4 py-1 flex items-center justify-between text-sm leading-5">
            <div class="flex-shrink-0 h-5 w-5 text-green-400 flex-1 flex items-center">
              <span class="text-gray-900 ml-2 text-xs flex-1 w-0 truncate">
                Snellen in Meters, with glasses
              </span>
            </div>
            <div class="ml-4 flex-shrink-0">
              <div class="uppercase font-medium transition duration-150 ease-in-out">
                <%= screening.snellen_left_eye %>
              </div>
            </div>
          </li>
        </ul>
        </dd>
      </div>

      <div class="flex px-4 pb-5 sm:px-6">
        <dt class="w-1/3 text-sm leading-5 font-medium text-gray-500">
        Right Eye
        </dt>
        <dd class="w-2/3 mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">

        <ul class="max-w-md border border-gray-200 rounded-md">

          <li class="border-t border-gray-200 first:border-t-0 pl-3 pr-4 py-1 flex items-center justify-between text-sm leading-5">
            <div class="flex-shrink-0 h-5 w-5 text-green-400 flex-1 flex items-center">
              <span class="text-gray-900 ml-2 text-xs flex-1 w-0 truncate">
                Actual Horizontal Total Field
              </span>
            </div>
            <div class="ml-4 flex-shrink-0">
              <div class="uppercase font-medium transition duration-150 ease-in-out">
                <%= screening.total_right %>
              </div>
            </div>
          </li>
          <li class="border-t border-gray-200 first:border-t-0 pl-3 pr-4 py-1 flex items-center justify-between text-sm leading-5">
            <div class="flex-shrink-0 h-5 w-5 text-green-400 flex-1 flex items-center">
              <span class="text-gray-900 ml-2 flex-1  text-xs w-0 truncate">
                Actual Horizontal Temporal Field
              </span>
            </div>
            <div class="ml-4 flex-shrink-0">
              <div class="uppercase font-medium transition duration-150 ease-in-out">
                <%= screening.temporal_right %>
              </div>
            </div>
          </li>
          <li class="border-t border-gray-200 first:border-t-0 pl-3 pr-4 py-1 flex items-center justify-between text-sm leading-5">
            <div class="flex-shrink-0 h-5 w-5 text-green-400 flex-1 flex items-center">
              <span class="text-gray-900 ml-2 flex-1 w-0 text-xs truncate">
                Snellen Rating in Meters without glasses
              </span>
            </div>
            <div class="ml-4 flex-shrink-0">
              <div class="uppercase font-medium transition duration-150 ease-in-out">
                <%= screening.snellen_right_eye_without_glasses %>
              </div>
            </div>
          </li>

          <li class="border-t border-gray-200 first:border-t-0 pl-3 pr-4 py-1 flex items-center justify-between text-sm leading-5">
            <div class="flex-shrink-0 h-5 w-5 text-green-400 flex-1 flex items-center">
              <span class="text-gray-900 ml-2 flex-1 w-0 truncate text-xs">
                Snellen Rating in Meters with glasses
              </span>
            </div>
            <div class="ml-4 flex-shrink-0">
              <div class="uppercase font-medium transition duration-150 ease-in-out">
                <%= screening.snellen_right_eye %>
              </div>
            </div>
          </li>
        </ul>
        </dd>
      </div>

      <div class="flex  px-4 py-1 sm:px-6">
        <dt class="w-1/3 text-sm leading-5 font-medium text-gray-500">
        Color Discrimination
        </dt>
        <dd class="w-2/3 ml-6 mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
        <%= screening.color_discrimination.capitalize %>
        </dd>
      </div>

    </div>
    <div class="border-t border-gray-100 flex justify-end text-gray-200">
      End of Report
    </div>
  </div>
</div>

<div data-controller='qrcode' class="opacity-50 footer right">
  <canvas data-target="qrcode.output"><%= qrcode_result %></canvas>
</div>

<div class="pb-56 flex-col items-center justify-center footer left w-90 h-40"
         style="background-image: url('https://www.transparenttextures.com/patterns/triangles.png')">
         <%= image_tag signature.image.url, class: "fill-current w-80 h-36 text-gray-500" %>

     <div class="border-t text-center">
       <%= signature.first_line %>
     </div>
     <% if signature.second_line.present? %>
       <div class="text-center">
         <%= signature.second_line %>
       </div>
     <% end %>
     <% if signature.third_line.present? %>
       <div class="text-center">
         <%= signature.third_line %>
       </div>
     <% end %>
     <% if signature.forth_line.present? %>
       <div class="text-center">
         <%= signature.forth_line %>
       </div>
     <% end %>
</div>

</div>
