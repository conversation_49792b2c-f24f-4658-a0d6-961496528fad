class VisionScreeningReportComponent < ViewComponent::Base
  attr_reader :screening

  def initialize(screening:)
    @screening = screening
  end

  delegate :patient, to: :screening
  delegate :clinic, to: :screening

  def signoff
    screening.vision_screening_signoff
  end

  def signature
    screening.vision_screening_signoff.user.signatures.first
  end

  def qrcode_result
    result = "ID:#{patient.identification_number}"

    if screening.total_left
      result << "|ActualField_L=#{screening.total_left}"
    end

    if screening.total_right
      result << "|ActualField_R=#{screening.total_right}"
    end

    if screening.temporal_left
      result << "|TemporalField_L=#{screening.temporal_left}"
    end

    if screening.temporal_right
      result << "|TemporalField_R=#{screening.temporal_right}"
    end

    if screening.color_discrimination
      result << "|ColorDiscrimination=#{screening.color_discrimination}"
    end

    result
  end
end
