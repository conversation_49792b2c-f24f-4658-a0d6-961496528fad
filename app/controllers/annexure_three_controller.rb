class AnnexureThreeController < ApplicationController
  layout "empty_shell"

  def new
  end

  def show
    #  find_screening
  end

  def edit
    #  find_screening
  end

  def update
    # find_screening

    # if @vision_screening.update!(edit_vision_params)
    #   redirect_to vision_screening_path(@vision_screening), notice: "Vision Screening was successfully updated"
    # else
    #   broadcast_errors @vision_screening, vision_params
    # end
  end

  def create
    # @vision = VisionScreeningForm.new(vision_params.merge!(
    #   patient_id: params[:patient_id]
    # ))

    # if @vision.save!
    #   redirect_to patient_path(params[:patient_id]), notice: "Vision Screening was successfully created"
    # else
    #   broadcast_errors @vision, vision_params
    # end
  end

  def destroy
    # find_screening
    # patient = @vision_screening.patient
    # @vision_screening.destroy
    # redirect_to patient_path(patient), notice: "Vision Screening was successfully deleted"
  end

  private

  def find_screening
    # @vision_screening = VisionScreening.find(params[:id])
  end

  def edit_vision_params
    # params.require(:vision_screening).permit(
    #   :snellen_right_eye,
    #   :snellen_left_eye,
    #   :temporal_right,
    #   :temporal_left,
    #   :total_right,
    #   :total_left,
    #   :color_discrimination,
    #   :comment,
    #   :name,
    #   :date_of_screening,
    #   :patient,
    #   :performed_by
    # )
  end

  def vision_params
    # params.require(:vision).permit(
    #   :snellen_right_eye,
    #   :snellen_left_eye,
    #   :temporal_right,
    #   :temporal_left,
    #   :total_right,
    #   :total_left,
    #   :color_discrimination,
    #   :comment,
    #   :name,
    #   :date_of_screening,
    #   :patient,
    #   :performed_by
    # )
  end
end
