# frozen_string_literal: true

class ApplicationController < ActionController::Base
  before_action :authenticate_user!, unless: :devise_controller?
  skip_before_action :verify_authenticity_token
  include ActiveStorage::SetCur<PERSON>

  def append_info_to_payload(payload)
    super
    payload[:user_id] = current_user.try(:id)
    payload[:host] = request.host
    payload[:source_ip] = request.remote_ip
    payload[:level] = if payload[:status] == 200
      "INFO"
    elsif payload[:status] == 302
      "WARN"
    else
      "ERROR"
    end
  end
end
