# frozen_string_literal: true

class AudiosController < ApplicationController
  layout "empty_shell"

  def new
    get_patient
    get_clinics

    @audio_form = AudioForm.new(Audio.new)
  end

  def edit
    get_audio
    @clinics = @audio.patient.organisation.clinics
    @audio_form = AudioForm.new(Audio.find(params[:id]))
  end

  def show
    @audio = Audio.find(params[:id])
  end

  def create
    get_patient
    get_clinics

    @audio = AudioForm.new(Audio.new(audio_params.merge!(patient_id: params[:patient_id])))

    respond_to do |format|
      if @audio.validate(params[:audio])
        @audio.save!
        format.html { redirect_to patient_path(@patient), notice: "Audiometry assessment was successfully created" }
      else
        format.turbo_stream { render turbo_stream: turbo_stream.replace(@audio, partial: "form", locals: {audio: @audio, clinics: @clinics, url: patient_audios_path}) }
      end
    end
  end

  def update
    get_audio
    audio_form = AudioForm.new(@audio)
    @clinics = @audio.patient.organisation.clinics

    respond_to do |format|
      if audio_form.validate(params[:audio])
        audio_form.update!
        format.html { redirect_to @audio.patient, notice: "Audiometry assessment was successfully updated." }
      else
        format.turbo_stream { render turbo_stream: turbo_stream.replace(audio_form, partial: "form", locals: {audio: audio_form, clinics: @clinics, url: audio_path(@audio)}) }
      end
    end
  end

  def destroy
    get_audio

    @audio.destroy
    redirect_to patient_path(@audio.patient), notice: "Audiometry assessment was successfully deleted"
  end

  private

  def audio_params
    params.require(:audio)
      .permit(:result,
        :note,
        :name,
        :performed_by,
        :system_used,
        :clinic_id,
        :patient_id,
        :signed_by,
        :signed,
        :signature_date,
        :status,
        attachments: {},
        attachments_attributes: {})
  end

  def get_clinics
    @clinics = @patient.organisation.clinics
  end

  def get_audio
    @audio = Audio.find params[:id]
  end

  def get_patient
    @patient = Patient.find(params[:patient_id])
  end
end
