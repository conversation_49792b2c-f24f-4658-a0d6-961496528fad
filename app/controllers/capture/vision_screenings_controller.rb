# frozen_string_literal: true

module Capture
  class VisionScreeningsController < ApplicationController
    layout "empty_shell"

    def new
      get_patient
      get_clinics

      @vision = VisionScreeningForm.new
    end

    def show
      find_screening
    end

    def edit
      find_screening
    end

    def update
      find_screening

      respond_to do |format|
        if @vision_screening.update!(edit_vision_params)
          redirect_to vision_screening_path(@vision_screening), notice: "Vision Screening was successfully updated"
        else
          format.html { render :new, status: :unprocessable_entity }
        end
      end
    end

    def create
      get_patient
      get_clinics

      @vision = VisionScreeningForm.new(vision_params.merge!(
        patient_id: params[:patient_id]
      ))
      respond_to do |format|
        if @vision.save!
          format.html { redirect_to patient_path(params[:patient_id]), notice: "Vision Screening was successfully created" }
        else
          format.html { render :new, status: :unprocessable_entity }
        end
      end
    end

    private

    def get_clinics
      @clinics = @patient.organisation.clinics
    end

    def get_patient
      @patient = Patient.find(params[:patient_id])
    end

    def find_screening
      @vision_screening = VisionScreening.find(params[:id])
    end

    def edit_vision_params
      params.require(:vision_screening).permit(
        :snellen_right_eye,
        :snellen_left_eye,
        :temporal_right,
        :temporal_left,
        :total_right,
        :total_left,
        :color_discrimination,
        :comment,
        :name,
        :date_of_screening,
        :clinic_id,
        :patient,
        :performed_by
      )
    end

    def vision_params
      params.require(:vision).permit(
        :snellen_right_eye,
        :snellen_left_eye,
        :temporal_right,
        :temporal_left,
        :total_right,
        :total_left,
        :color_discrimination,
        :comment,
        :name,
        :date_of_screening,
        :patient,
        :clinic_id,
        :performed_by
      )
    end
  end
end
