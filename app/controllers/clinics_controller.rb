class ClinicsController < ApplicationController
  layout "empty_shell"
  before_action :set_clinic, only: [:show, :edit, :update, :destroy]

  # GET /clinics
  # GET /clinics.json
  def index
    @data ||= Clinic.where(organisation_id: params[:organisation_id])
  end

  # GET /clinics/1
  # GET /clinics/1.json
  def show
  end

  # GET /clinics/new
  def new
    @clinic = Clinic.new
  end

  # GET /clinics/1/edit
  def edit
  end

  # POST /clinics
  # POST /clinics.json
  def create
    @clinic = Clinic.new(clinic_params.merge!(organisation_id: params[:organisation_id]))

    respond_to do |format|
      if @clinic.save
        format.html { redirect_to organisation_clinics_path @clinic.organisation, notice: "Clinic was successfully created." }
        format.json { render :show, status: :created, location: @clinic }
      else
        format.html { broadcast_errors @clinic, clinic_params }
        format.json { render json: @clinic.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /clinics/1
  # PATCH/PUT /clinics/1.json
  def update
    respond_to do |format|
      if @clinic.update(clinic_params)
        format.html { redirect_to clinic_path @clinic, notice: "Clinic was successfully updated." }
        format.json { render :show, status: :ok, location: @clinic }
      else
        format.html { broadcast_errors @clinic, clinic_params }
        format.json { render json: @clinic.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /clinics/1
  # DELETE /clinics/1.json
  def destroy
    @clinic.destroy
    respond_to do |format|
      format.html { redirect_to organisation_clinics_path @clinic.organisation, notice: "Clinic was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_clinic
    @clinic = Clinic.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def clinic_params
    params.require(:clinic).permit(:clinic_name,
      :phone_number,
      :physical_address_2,
      :physical_address,
      :details)
  end
end
