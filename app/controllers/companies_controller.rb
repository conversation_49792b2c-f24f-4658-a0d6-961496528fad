# frozen_string_literal: true

class CompaniesController < ApplicationController
  layout "empty_shell"

  def index
    @data ||= Company.where(organisation_id: params[:organisation_id])
  end

  def new
    @company = CompanyForm.new(Company.new)
  end

  def edit
    get_company
  end

  def update
    get_company

    if @company.update(company_params)
      redirect_to organisation_companies_path(@company.organisation), notice: "Company details was successfully updated"
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def show
    get_company
    @employees = @company.employees
  end

  def create
    company_data = company_params.merge!(organisation_id: params[:organisation_id])
    @company = CompanyForm.new(Company.new)

    if @company.validate(company_data)
      @company.save!
      flash[:notice] = "Company was successfully created"
      redirect_to root_path
    else
      render :new, status: :unprocessable_entity
    end
  end

  private

  def get_organisation
    @org = Organisation.find(params[:organisation_id])
  end

  def get_company
    @company = Company.find(params[:id])
  end

  def company_params
    params.require(:company_form).permit(
      :name,
      :street_address,
      :industry_sector,
      :about,
      :phone_number,
      :email,
      :suburb,
      :city
    )
  end
end
