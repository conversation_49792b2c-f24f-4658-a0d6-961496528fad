class EmploymentsController < ApplicationController
  layout "empty_shell"

  def edit
    @employment = get_employment
    @companies = @employment.patient.organisation.companies
  end

  def update
    @employment = get_employment
    @companies = @employment.patient.organisation.companies

    respond_to do |format|
      if @employment.update(employment_params)
        format.html { redirect_to @employment.patient, notice: "Employment was successfully updated" }
      else
        format.turbo_stream {
          render turbo_stream: turbo_stream.replace(@employment, partial: "form", locals: {employment: @employment,
                                                                                           companies: @companies,
                                                                                           url: employment_path})
        }
      end
    end
  end

  def new
    @employment = Employment.new
    @companies = get_patient.organisation.companies
  end

  def create
    @employment = Employment.new(employment_params.merge!(patient_id: params[:patient_id]))
    @companies = @employment.patient.organisation.companies

    respond_to do |format|
      if @employment.save
        format.html { redirect_to @employment.patient, notice: "Employment was successfully created" }
      else
        format.turbo_stream {
          render turbo_stream: turbo_stream.replace(@employment, partial: "form", locals: {employment: @employment,
                                                                                           companies: @companies,
                                                                                           url: patient_employments_path})
        }
      end
    end
  end

  def destroy
    @employment = get_employment
    @patient = @employment.patient

    @employment.destroy!

    redirect_to @patient, notice: "Employment successfully deleted"
  end

  private

  def get_employment
    Employment.find(params[:id])
  end

  def get_patient
    Patient.find(params[:patient_id])
  end

  def employment_params
    params.require(:employment)
      .permit(
        :department,
        :employment_type,
        :induction_date,
        :position,
        :termination_date,
        :termination_reason,
        :company_id
      )
  end
end
