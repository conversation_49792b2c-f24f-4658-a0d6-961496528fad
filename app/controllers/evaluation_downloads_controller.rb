class EvaluationDownloadsController < ApplicationController
  skip_before_action :authenticate_user!
  before_action :check_auth

  def show
    @evaluation = report
    respond_to do |format|
      format.pdf { send_report_pdf }
      format.html { render_sample_html }
    end
  end

  private

  def report
    Evaluation.find(params[:evaluation_id])
  end

  def download
    Download.new(report: report,
      url: lab_test_download_url(report),
      filename: report.report_name)
  end

  def send_report_pdf
    send_data download.to_pdf, download_attributes
  end

  def render_sample_html
    render :show, layout: "pdf"
  end

  def download_attributes
    {
      filename: download.filename,
      type: "application/pdf",
      disposition: "inline"
    }
  end

  def check_auth
    authenticate_or_request_with_http_basic do |username, password|
      username == "jack" && password == "black"
    end
    warden.custom_failure! if performed?
  end
end
