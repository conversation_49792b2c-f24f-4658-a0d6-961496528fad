# frozen_string_literal: true

class EvaluationSignOffsController < ApplicationController
  layout "empty_shell"

  def new
    find_evaluation
    @patient = find_evaluation.patient
  end

  def create
    find_evaluation

    if @evaluation.update!(status: ENUMS::ASSESSMENT_STATUS::SIGNED_OFF) && EvaluationSignoff.create!(user: current_user, evaluation: @evaluation, date_signed: Date.today)
      GenerateEvaluationReportWorker.perform_async(report_id: params[:evaluation_id], user_id: current_user.id)
      redirect_to evaluation_path(@evaluation),
        notice: "Evaluation was successfully signed off"
    else
      render :new
    end
  end

  private

  def find_evaluation
    @evaluation = Evaluation.find(params[:evaluation_id])
  end
end
