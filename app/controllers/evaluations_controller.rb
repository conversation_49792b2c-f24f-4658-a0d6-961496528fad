# frozen_string_literal: true

class EvaluationsController < ApplicationController
  layout "empty_shell"

  def new
    @patient ||= Patient.find(params[:patient_id])
    @evaluation ||= EvaluationForm.new(Evaluation.new)
    @clinics = @patient.organisation.clinics

    @spiros = @spiro || @patient.spiros
    @audios ||= @patient.audios
    @physicals ||= @patient.physicals
    @visuals ||= @patient.visuals
    @labs ||= @patient.lab_tests
    @exclusions ||= @patient.exclusions
    @referrals ||= @patient.referrals
  end

  def show
    get_evalution
    @patient = get_evalution.patient
  end

  def update
    get_evalution
    evaluation_form = EvaluationForm.new(@evaluation)

    @patient ||= @evaluation.patient
    @clinics = @patient.organisation.clinics
    @spiros = @spiro || @patient.spiros
    @audios ||= @patient.audios
    @physicals ||= @patient.physicals
    @visuals ||= @patient.visuals
    @labs ||= @patient.lab_tests
    @exclusions ||= @patient.exclusions
    @referrals ||= @patient.referrals

    respond_to do |format|
      if evaluation_form.validate(params[:evaluation_form].reject { |_, v| v.blank? })
        evaluation_form.update!
        format.html { redirect_to evaluation_path(params[:id]), notice: "Certificate was successfully updated" }
      else
        format.html { render :edit, status: :unprocessable_entity }
        format.turbo_stream { render turbo_stream: turbo_stream.replace(evaluation_form, partial: "form", locals: {evaluation: evaluation_form, url: evaluation_path(@evaluation)}) }
      end
    end
  end

  def edit
    get_evalution
    @evaluation_form = EvaluationForm.new(Evaluation.find(params[:id]))
    @patient ||= @evaluation.patient
    @clinics = @patient.organisation.clinics

    @spiros = @spiro || @patient.spiros
    @audios ||= @patient.audios
    @physicals ||= @patient.physicals
    @visuals ||= @patient.visuals
    @labs ||= @patient.lab_tests
    @exclusions ||= @patient.exclusions
    @referrals ||= @patient.referrals
  end

  def create
    @patient ||= Patient.find(params[:patient_id])
    @clinics = @patient.organisation.clinics
    @spiros = @spiro || @patient.spiros
    @audios ||= @patient.audios
    @physicals ||= @patient.physicals
    @visuals ||= @patient.visuals
    @labs ||= @patient.lab_tests
    @exclusions ||= @patient.exclusions
    @referrals ||= @patient.referrals

    @evaluation = EvaluationForm.new(Evaluation.new)

    respond_to do |format|
      if @evaluation.validate(params[:evaluation_form].merge!(patient_id: params[:patient_id]).reject { |_, v| v.blank? })
        @evaluation.save!
        format.html { redirect_to patient_path(params[:patient_id]), notice: "Certificate was successfully created" }
      else
        format.html { render :new, status: :unprocessable_entity }
        format.turbo_stream { render turbo_stream: turbo_stream.replace("new_evaluation", partial: "form", locals: {evaluation: @evaluation, url: patient_evaluations_path}) }
      end
    end
  end

  def destroy
    get_evalution
    @patient = get_evalution.patient

    if @evaluation.destroy
      redirect_to @patient, notice: "Medical successfully deleted"
    end
  end

  private

  def evaluations_params
    params
      .require(:evaluation_form)
      .permit(
        :name,
        :medical_type,
        :physical_exam,
        :physical_exam_checkbox,
        :physical_exam_unlink,
        :visual,
        :visual_checkbox,
        :visual_unlink,
        :audio,
        :audio_checkbox,
        :audio_unlink,
        :spiro,
        :spiro_checkbox,
        :spiro_unlink,
        :xray,
        :xray_checkbox,
        :xray_unlink,
        :heat,
        :heat_checkbox,
        :heat_unlink,
        :height,
        :height_checkbox,
        :height_unlink,
        :drug,
        :drug_checkbox,
        :drug_unlink,
        :ecg_checkbox,
        :ecg_unlink,
        :ecg,
        :xray_checkbox,
        :xray_unlink,
        :xray,
        :outcome,
        :outcome_comment,
        :exclusions,
        :referral,
        :medical_examination_date,
        :medical_expiry_date,
        :employment_id,
        :clinic_id,
        exclusions: [],
        referrals: []
      )
  end

  def get_patient
    @patient = Patient.find(params[:patient_id])
  end

  def get_evalution
    @evaluation = Evaluation.find(params[:id])
  end
end
