class ExclusionsController < ApplicationController
  layout "empty_shell"

  def new
    @patient = Patient.find(params[:patient_id])
    @exclusion = Exclusion.new
  end

  def create
    @patient = Patient.find(params[:patient_id])
    @exclusion = Exclusion.new(exclusion_params.merge!(patient_id: params[:patient_id]))

    respond_to do |format|
      if @exclusion.save
        format.html { redirect_to @patient, notice: "Exclusion was successfully created" }
      else
        format.html { render :new, status: :unprocessable_entity }
      end
    end
  end

  def edit
    @exclusion = Exclusion.find(params[:id])
    @patient = @exclusion.patient
  end

  def update
    @exclusion = Exclusion.find(params[:id])
    @patient = @exclusion.patient

    respond_to do |format|
      if @exclusion.update(exclusion_params)
        format.html { redirect_to @patient, notice: "Exclusion was successfully updated" }
      else
        format.html { render :edit, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @exclusion = Exclusion.find(params[:id])
    @patient = @exclusion.patient
    @exclusion.destroy

    redirect_to @patient, notice: "Exclusion was successfully deleted"
  end

  private

  def exclusion_params
    params.require(:exclusion).permit(
      :category,
      :note
    )
  end
end
