class ExpiredMedicalsController < ApplicationController
  layout "empty_shell"

  def index
    @organisation_id = params[:organisation_id]
    
    if @organisation_id.blank?
      redirect_to root_path, alert: "Organisation ID is required"
      return
    end

    @organisation = Organisation.find_by(id: @organisation_id)
    unless @organisation
      redirect_to root_path, alert: "Organisation not found"
      return
    end

    # Get all evaluations with expired medical_expiry_date
    expired_evaluations = Evaluation.joins(:patient, employment: :company)
      .where(patients: { organisation_id: @organisation_id })
      .where('medical_expiry_date < ?', Date.current)
      .includes(:patient, employment: :company)
      .order('companies.name ASC, medical_expiry_date ASC')

    # Group by company for display
    @grouped_expired_medicals = expired_evaluations.group_by { |eval| eval.employment.company.name }
    @total_expired = expired_evaluations.count
  end

  def export_csv
    @organisation_id = params[:organisation_id]
    
    if @organisation_id.blank?
      redirect_to root_path, alert: "Organisation ID is required"
      return
    end

    @organisation = Organisation.find_by(id: @organisation_id)
    unless @organisation
      redirect_to root_path, alert: "Organisation not found"
      return
    end

    # Get all evaluations with expired medical_expiry_date
    expired_evaluations = Evaluation.joins(:patient, employment: :company)
      .where(patients: { organisation_id: @organisation_id })
      .where('medical_expiry_date < ?', Date.current)
      .includes(:patient, employment: :company)
      .order('companies.name ASC, medical_expiry_date ASC')

    respond_to do |format|
      format.csv do
        csv_data = generate_csv(expired_evaluations)
        send_data csv_data, 
          filename: "expired_medicals_#{Date.current.strftime('%Y%m%d')}.csv",
          type: 'text/csv'
      end
    end
  end

  private

  def generate_csv(expired_evaluations)
    require 'csv'
    
    CSV.generate(headers: true) do |csv|
      csv << ['Full Name', 'Contact Number', 'ID Number', 'Company', 'Medical Reference', 'Medical Expired On']
      
      expired_evaluations.each do |evaluation|
        csv << [
          evaluation.patient.full_name,
          evaluation.patient.phone_number,
          evaluation.patient.identification_number,
          evaluation.employment.company.name,
          evaluation.name,
          evaluation.medical_expiry_date.strftime('%Y-%m-%d')
        ]
      end
    end
  end
end
