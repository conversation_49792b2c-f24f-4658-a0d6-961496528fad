# frozen_string_literal: true

class LabTestController < ApplicationController
  layout "empty_shell"
  def new
    @lab_test = LabTest.new
    @patient = Patient.find params[:patient_id]
  end

  def edit
    find_lab_test
    @users = @lab_test.patient.organisation.users
    @clinics = @lab_test.patient.organisation.clinics
  end

  def show
    find_lab_test
  end

  def update
    find_lab_test

    if @lab_test.update!(lab_test_params)
      redirect_to patient_path(@lab_test.patient), notice: "Drug Screening was successfully updated"
    else
      render :new
    end
  end

  def create
    @patient = Patient.find params[:patient_id]

    @lab_test = LabTest.new(
      lab_test_params.merge(patient_id: params[:patient_id])
    )

    if @lab_test.save!
      redirect_to patient_path(@patient), notice: "Drug Screening was successfully captured"
    else
      render :new
    end
  end

  def destroy
    find_lab_test
    @lab_test.destroy
    redirect_to patient_path(@lab_test.patient), notice: "Drug Screening was successfully removed"
  end

  private

  def find_patient
    @patient = Patient.find params[:id]
  end

  def find_lab_test
    @lab_test = LabTest.find params[:id]
  end

  def lab_test_params
    params.require(:lab_test).permit(
      :ast,
      :cannabis,
      :fbc,
      :gamma,
      :hiv,
      :six_panel,
      :comment,
      :date_performed,
      :performed_by,
      :clinic_id
    )
  end
end
