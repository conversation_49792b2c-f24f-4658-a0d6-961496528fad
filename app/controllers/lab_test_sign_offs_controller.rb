# frozen_string_literal: true

class LabTestSignOffsController < ApplicationController
  layout "empty_shell"
  def new
    find_lab_test
  end

  def create
    find_lab_test

    if @lab_test.update!(status: "signed off") && LabTestSignoff.create!(user: current_user, lab_test: @lab_test, date_signed: Date.today)
      GenerateLabTestReportWorker.perform_async(report_id: params[:lab_test_id], user_id: current_user.id)
      redirect_to lab_test_path(@lab_test),
        notice: "Drug Screening was successfully signed off"
    else
      render :new
    end
  end

  private

  def find_lab_test
    @lab_test = LabTest.find(params[:lab_test_id])
  end
end
