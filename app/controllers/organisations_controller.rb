class OrganisationsController < ApplicationController
  before_action :set_organisation, only: [:show, :edit, :update, :destroy]
  layout "empty_shell", only: [:index]

  # GET /organisations
  # GET /organisations.json
  def index
    @organisations = current_user.organisations
    redirect_to @organisations.first if @organisations.count == 1
  end

  # GET /organisations/1
  # GET /organisations/1.json
  def show
    @patient_count = Organisation.find(params[:id]).patients.size
    @company_count = Organisation.find(params[:id]).companies.size
    @evaluations_count = Evaluation.where(patient_id: Patient.select("id").where(organisation_id: params[:id])).count

    @latest_patients = Organisation.find(params[:id])
      .patients.where(updated_at: 2.weeks.ago..Time.zone.now)
      .order("updated_at DESC")
      .limit(10)
      .includes([:employers, :employments])
  end

  # GET /organisations/new
  def new
    @organisation = Organisation.new
  end

  # GET /organisations/1/edit
  def edit
  end

  # POST /organisations
  # POST /organisations.json
  def create
    @organisation = current_user.organisations.new(organisation_params)

    respond_to do |format|
      if @organisation.save
        format.html { redirect_to @organisation, notice: "Organisation was successfully created." }
        format.json { render :show, status: :created, location: @organisation }
      else
        format.html { render :new }
        format.json { render json: @organisation.errors, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /organisations/1
  # PATCH/PUT /organisations/1.json
  def update
    respond_to do |format|
      if @organisation.update(organisation_params)
        format.html { redirect_to settings_organisational_path, notice: "Organisation was successfully updated." }
        format.json { render :show, status: :ok, location: @organisation }
      else
        format.html { render :edit }
        format.json { render json: @organisation.errors, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /organisations/1
  # DELETE /organisations/1.json
  def destroy
    @organisation.destroy
    respond_to do |format|
      format.html { redirect_to organisations_url, notice: "Organisation was successfully destroyed." }
      format.json { head :no_content }
    end
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def set_organisation
    @organisation = Organisation.find(params[:id])
  end

  # Only allow a list of trusted parameters through.
  def organisation_params
    params.require(:organisation).permit(:organisation_name, :registration_number, :email_address, :web_address, :contact_number)
  end
end
