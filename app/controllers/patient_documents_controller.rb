class PatientDocumentsController < ApplicationController
  layout "empty_shell"

  def new
    @document = PatientDocumentForm.new
  end

  def create
    data = doc_params.merge!(
      patient_id: params[:patient_id],
      uploaded_by: current_user.name.full
    )

    @document = PatientDocumentForm.new(PatientDocument.new(data))

    respond_to do |format|
      if @document.save!
        format.html {
          redirect_to patient_path(params[:patient_id]),
            notice: "Patient Document was successfully created"
        }
      else
        format
          .turbo_stream {
            render turbo_stream: turbo_stream.replace("new_patient_document",
              partial: "form",
              locals: {document: @document})
          }
      end
    end
  end

  def edit
    get_document
    @document_form = PatientDocumentForm.new(@document.attributes)
  end

  def update
    get_document
    document_form = PatientDocumentForm.new(@document.attributes)

    respond_to do |format|
      if document_form.validate(params[:patient_document])
        document_form.update!
        format.html { redirect_to @document.patient, notice: "Patient Document was successfully updated." }
      else
        format.turbo_stream {
          render turbo_stream: turbo_stream.replace(document_form,
            partial: "form",
            locals: {document: document_form,
                     url: patient_document_path(@document)})
        }
      end
    end
  end

  def destroy
    get_document
    @document.destroy
    redirect_to patient_path(@document.patient), notice: "Patient Document was successfully deleted"
  end

  private

  def get_document
    @document = PatientDocument.find(params[:id])
  end

  def doc_params
    params.require(:patient_document).permit(
      :name,
      :description,
      :uploaded_by,
      attachments: []
    )
  end
end
