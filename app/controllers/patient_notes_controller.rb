# frozen_string_literal: true

class PatientNotesController < ApplicationController
  layout "empty_shell"

  def create
    get_patient
    @patient_note = @patient.patient_notes.build(patient_note_params)

    if @patient_note.save
      redirect_to patient_path(@patient_note.patient_id)
    else
      render action: :new
    end
  end

  def show
    get_patient_note
  end

  def new
    get_patient
    @patient_note = @patient.patient_notes.build
  end

  def edit
    get_patient_note
  end

  def update
    get_patient_note

    if @patient_note.update!(patient_note_params)
      redirect_to patient_path(@patient_note.patient_id)
    else
      render action: :edit
    end
  end

  def destroy
    get_patient_note
    @patient_note.destroy

    redirect_to patient_path(@patient_note.patient_id)
  end

  private

  def patient_note_params
    params.require(:patient_note)
      .permit(:note,
        attachments_attributes: {})
  end

  def get_patient_note
    @patient_note = PatientNote.find params[:id]
  end

  def get_patient
    @patient = Patient.find(params[:patient_id])
  end
end
