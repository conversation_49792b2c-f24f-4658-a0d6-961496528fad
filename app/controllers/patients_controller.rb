# frozen_string_literal: true

class PatientsController < ApplicationController
  layout "empty_shell"

  def index
    @data ||= Patient.where(organisation_id: params[:organisation_id]).includes([:employers, :employments])
  end

  def new
    @patient = PatientForm.new(Patient.new)
  end

  def edit
    @patient = PatientForm.new(Patient.find(params[:id]))
  end

  def update
    @patient = PatientForm.new(Patient.find(params[:id]))

    respond_to do |format|
      if @patient.validate(params[:patient])
        @patient.update!
        format.html { redirect_to patient_path(@patient), notice: "Patient details was successfully updated" }
      else
        format.html { render :edit, status: :unprocessable_entity }
      end
    end
  end

  def create
    data = patient_params.merge!(organisation_id: params[:organisation_id])

    @patient = PatientForm.new(Patient.new(data))

    respond_to do |format|
      if @patient.validate(params[:patient])
        @patient.save!
        format.html { redirect_to root_path, notice: "Patient was successfully created" }
      else
        format.html { render :new, status: :unprocessable_entity }
      end
    end
  end

  def show
    @patient = Patient.find params[:id]
    @medicals = @patient.evaluations.includes(:evaluation_signoff)
      .last(3).sort_by(&:updated_at).reverse
    @notes = (@patient.lab_tests +
              @patient.audios +
              @patient.vision_screening_uploads +
              @patient.vision_screenings +
              @patient.exclusions +
              @patient.referrals
             ).sort { |a, b| b.created_at <=> a.created_at }
  end

  def destroy
    @patient = Patient.find params[:id]

    @patient.destroy
    redirect_to root_path, notice: "Patient was successfully deleted"
  end

  private

  def patient_params
    params.require(:patient).permit(
      :first_name,
      :last_name,
      :identification_number,
      :email,
      :dob,
      :gender,
      :phone_number
    )
  end
end
