class ReferralsController < ApplicationController
  layout "empty_shell"

  def new
    @patient = Patient.find(params[:patient_id])
    @referral = Referral.new
  end

  def create
    @patient = Patient.find(params[:patient_id])
    @referral = Referral.new(referral_params.merge!(patient_id: params[:patient_id]))

    respond_to do |format|
      if @referral.save
        format.html { redirect_to @patient, notice: "Referral was successfully created" }
      else
        format.html { render :new, status: :unprocessable_entity }
      end
    end
  end

  def edit
    @referral = Referral.find(params[:id])
    @patient = @referral.patient
  end

  def update
    @referral = Referral.find(params[:id])
    @patient = @referral.patient

    respond_to do |format|
      if @referral.update(referral_params)
        format.html { redirect_to @patient, notice: "Referral was successfully updated" }
      else
        format.html { render :edit, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    @referral = Referral.find(params[:id])
    @patient = @referral.patient
    @referral.destroy

    redirect_to @patient, notice: "Referral was successfully deleted"
  end

  private

  def referral_params
    params.require(:referral).permit(
      :address,
      :attention,
      :issues,
      :medical_centre,
      :referral_date,
      :specialist_type
    )
  end
end
