class Settings::SignaturesController < ApplicationController
  layout "empty_shell"

  def index
    @signatures = current_user.signatures
  end

  def new
    @signature = Signature.new
  end

  def edit
    get_signature
  end

  def update
    get_signature
    if @signature.update!(
      first_line: signature_params[:first_line],
      second_line: signature_params[:second_line],
      third_line: signature_params[:third_line],
      forth_line: signature_params[:forth_line]
    )
      redirect_to settings_signatures_path,
        notice: "Signature was successfully updated"
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def create
    @signature = current_user.signatures.build(
      first_line: signature_params[:first_line],
      second_line: signature_params[:second_line],
      third_line: signature_params[:third_line],
      forth_line: signature_params[:forth_line]
    )

    @signature.image.attach(temp_signature_file) unless signature_params[:image_data_uri].empty?

    if @signature.save
      redirect_to settings_signatures_path,
        notice: "Signature was successfully created"
    else
      render :new, status: :unprocessable_entity
    end
  end

  def destroy
    get_signature
    @signature.destroy
    redirect_to settings_signatures_path,
      notice: "Signature was successfully deleted"
  end

  private

  def get_signature
    @signature = Signature.find(params[:id])
  end

  def signature_params
    params.require(:signature).permit(
      :first_line,
      :second_line,
      :third_line,
      :forth_line,
      :image_data_uri,
      :image
    )
  end

  def signature_decoded
    return "" if signature_params[:image_data_uri].empty?

    decoded_data = Base64.decode64(signature_params[:image_data_uri].split(",")[1])
    {
      io: StringIO.new(decoded_data),
      content_type: "image/svg+xml",
      filename: "signature-#{Time.current.to_i}.svg"
    }
  end

  def temp_signature_file
    ActionDispatch::Http::UploadedFile.new({
      filename: "signature-#{Time.current.to_i}.svg",
      content_type: "image/svg+xml",
      tempfile: make_signature_temp_file
    })
  end

  def make_signature_temp_file
    decoded_data = Base64.decode64(signature_params[:image_data_uri].split(",")[1])
    temp = Tempfile.new(["", ".svg"])
    File.binwrite(temp, decoded_data)
    temp.close
    temp
  end
end
