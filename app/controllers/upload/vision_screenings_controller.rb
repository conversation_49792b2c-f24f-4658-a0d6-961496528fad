# frozen_string_literal: true

module Upload
  class VisionScreeningsController < ApplicationController
    layout "empty_shell"

    def new
      get_patient
      get_clinics

      @vision = VisionScreeningUploadForm.new(VisionScreeningUpload.new)
    end

    def show
      get_screening
    end

    def edit
      get_screening
      @clinics = @vision_screening.patient.organisation.clinics
      @vision_form = VisionScreeningUploadForm.new(VisionScreeningUpload.find(params[:id]))
    end

    def update
      get_screening
      @clinics = @vision_screening.patient.organisation.clinics
      vision_form = VisionScreeningUploadForm.new(@vision_screening)

      respond_to do |format|
        if vision_form.validate(params[:vision])
          vision_form.update!
          format.html { redirect_to @vision_screening.patient, notice: "Vision Screening was successfully updated" }
        else
          format.turbo_stream { render turbo_stream: turbo_stream.replace(@vision, partial: "form", locals: {vision: vision_form, clinics: @clinics, url: upload_vision_screening_path(@vision_screening)}) }
        end
      end
    end

    def create
      get_patient
      get_clinics

      @vision = VisionScreeningUploadForm.new(
        VisionScreeningUpload.new(vision_params.merge!(
          patient_id: params[:patient_id]
        ))
      )

      respond_to do |format|
        if @vision.validate(params[:vision])
          @vision.save!
          format.html { redirect_to patient_path(params[:patient_id]), notice: "Vision Screening Report was successfully created" }
        else
          format.turbo_stream { render turbo_stream: turbo_stream.replace(@vision, partial: "form", locals: {vision: @vision, clinics: @clinics, url: patient_upload_vision_screenings_path}) }
        end
      end
    end

    private

    def get_clinics
      @clinics = @patient.organisation.clinics
    end

    def get_patient
      @patient = Patient.find(params[:patient_id])
    end

    def get_screening
      @vision_screening = VisionScreeningUpload.find(params[:id])
    end

    def vision_params
      params.require(:vision).permit(
        :note,
        :result,
        :name,
        :performed_by,
        :system_used,
        :clinic_id,
        :patient_id,
        :signed_by,
        :signed,
        :signature_date,
        :status,
        attachments: {},
        attachments_attributes: {}
      )
    end
  end
end
