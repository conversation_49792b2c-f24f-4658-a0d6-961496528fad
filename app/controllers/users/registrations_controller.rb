# frozen_string_literal: true

class Users::RegistrationsController < Devise::RegistrationsController
  layout "auth", only: :new
  layout "empty_shell", except: :new

  # before_action :configure_sign_up_params, only: %i[create]
  # before_action :configure_account_update_params, only: %i[update]

  # GET /resource/sign_up
  def new
    render(layout: "auth") && return
    super
  end

  # POST /resource
  def create
    super

    created_organisation =
      if sign_up_params[:organisation_name].blank?
        Organisation.create!(
          organisation_name: "#{sign_up_params[:last_name]} Organisation"
        )
      else
        Organisation.create!(
          organisation_name: sign_up_params[:organisation_name]
        )
      end

    created_organisation.clinics.create!(
      clinic_name: "#{created_organisation.organisation_name}'s clinic"
    )

    OrganisationUser.create!(user: @user, organisation: created_organisation)
  end

  # GET /resource/edit
  def edit
    super
  end

  # PUT /resource
  def update
    super
  end

  # DELETE /resource
  def destroy
    super
  end

  # GET /resource/cancel
  # Forces the session data which is usually expired after sign
  # in to be expired now. This is useful if the user wants to
  # cancel oauth signing in/up in the middle of the process,
  # removing all OAuth session data.
  def cancel
    super
  end

  protected

  def sign_up_params
    params.require(:user).permit(
      :email,
      :password,
      :password_confirmation,
      :first_name,
      :last_name,
      :organisation_name
    )
  end

  def account_update_params
    params.require(:user).permit(
      :email,
      :password,
      :password_confirmation,
      :current_password,
      :first_name,
      :last_name
    )
  end

  # The path used after sign up.
  def after_sign_up_path_for(resource)
    super(resource)
    new_user_session_url
  end

  # The path used after sign up for inactive accounts.
  def after_inactive_sign_up_path_for(resource)
    super(resource)
    new_user_session_url
  end

  def update_resource(resource, params)
    # Require current password if user is trying to change password.
    return super if params["password"]&.present?

    # Allows user to update registration information without password.
    resource.update_without_password(params.except("current_password"))
  end
end
