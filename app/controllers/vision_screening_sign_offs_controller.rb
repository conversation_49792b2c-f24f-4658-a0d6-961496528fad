# frozen_string_literal: true

class VisionScreeningSignOffsController < ApplicationController
  layout "empty_shell"

  def new
    find_screening
  end

  def create
    find_screening

    if @vision_screening.update!(status: "signed off") && VisionScreeningSignoff.create!(user: current_user, vision_screening: @vision_screening, date_signed: Date.today)
      GenerateVisionScreeningReportWorker.perform_async(report_id: params[:vision_screening_id], user_id: current_user.id)
      redirect_to vision_screening_path(@vision_screening),
        notice: "Vision Screening was successfully signed off"
    else
      render :new
    end
  end

  private

  def find_screening
    @vision_screening = VisionScreening.find(params[:vision_screening_id])
  end
end
