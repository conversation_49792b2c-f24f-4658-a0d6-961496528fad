# frozen_string_literal: true

class VisionScreeningsController < ApplicationController
  layout "empty_shell"

  def new
    get_patient
    get_clinics
  end

  def show
    find_screening
  end

  private

  def find_screening
    @vision_screening = VisionScreening.find(params[:id])
  end

  def get_clinics
    @clinics = @patient.organisation.clinics
  end

  def get_patient
    @patient = Patient.find(params[:patient_id])
  end
end
