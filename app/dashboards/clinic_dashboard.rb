require "administrate/base_dashboard"

class ClinicDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    clinic_name: Field::String,
    clinic_teams: Field::HasMany,
    details: Field::String,
    organisation: Field::BelongsTo,
    phone_number: Field::String,
    physical_address: Field::String,
    physical_address_2: Field::String,
    teams: Field::HasMany,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    clinic_name
    clinic_teams
    details
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    clinic_name
    clinic_teams
    details
    organisation
    phone_number
    physical_address
    physical_address_2
    teams
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    clinic_name
    clinic_teams
    details
    organisation
    phone_number
    physical_address
    physical_address_2
    teams
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how clinics are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(clinic)
  #   "Clinic ##{clinic.id}"
  # end
end
