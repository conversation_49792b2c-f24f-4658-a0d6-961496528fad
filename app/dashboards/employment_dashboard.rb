require "administrate/base_dashboard"

class EmploymentDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    company: Field::BelongsTo.with_options(
      searchable: true,
      searchable_fields: ["name"]
    ),
    department: Field::String,
    patient_name: Field::String.with_options(
      searchable: false
    ),
    company_name: Field::String.with_options(
      searchable: false
    ),
    employment_type: Field::String,
    induction_date: Field::Date,
    patient: Field::BelongsTo.with_options(
      searchable: true,
      searchable_fields: ["last_name", "first_name"]
    ),
    position: Field::String,
    termination_date: Field::Date,
    termination_reason: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    company
    patient_name
    company_name
    department
    employment_type
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    company
    patient_name
    company_name
    department
    employment_type
    induction_date
    patient
    position
    termination_date
    termination_reason
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    company
    department
    employment_type
    induction_date
    patient
    position
    termination_date
    termination_reason
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how employments are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(employment)
  #   "Employment ##{employment.id}"
  # end
end
