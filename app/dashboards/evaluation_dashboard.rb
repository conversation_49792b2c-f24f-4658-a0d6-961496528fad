require "administrate/base_dashboard"

class EvaluationDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    assessment_reports: Field::HasMany,
    audio: Field::BelongsTo,
    audio_performed: Field::Boolean,
    cannabis_performed: Field::Boolean,
    clinic: Field::BelongsTo,
    ecg: Field::HasOne,
    ecg_performed: Field::Boolean,
    employment: Field::BelongsTo,
    evaluation_signoff: Field::HasOne,
    exclusion: Field::Boolean,
    exclusion_comment: Field::String,
    exclusions: Field::HasMany,
    heat: Field::HasOne,
    heat_performed: Field::Boolean,
    height: Field::HasOne,
    height_performed: Field::Boolean,
    lab_test: Field::BelongsTo,
    medical_examination_date: Field::Date,
    medical_expiry_date: Field::Date,
    medical_type: Field::String,
    name: Field::String,
    outcome: Field::Text,
    outcome_comment: Field::Text,
    patient: Field::BelongsTo,
    physical_exam_performed: Field::Boolean,
    referral: Field::Boolean,
    referral_comment: Field::String,
    referrals: Field::HasMany,
    spiro: Field::HasOne,
    spiro_performed: Field::Boolean,
    status: Field::String,
    visual: Field::HasOne,
    visual_performed: Field::Boolean,
    xray: Field::HasOne,
    xray_performed: Field::Boolean,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    assessment_reports
    audio
    audio_performed
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    assessment_reports
    audio
    audio_performed
    cannabis_performed
    clinic
    ecg
    ecg_performed
    employment
    evaluation_signoff
    exclusion
    exclusion_comment
    exclusions
    heat
    heat_performed
    height
    height_performed
    lab_test
    medical_examination_date
    medical_expiry_date
    medical_type
    name
    outcome
    outcome_comment
    patient
    physical_exam_performed
    referral
    referral_comment
    referrals
    spiro
    spiro_performed
    status
    visual
    visual_performed
    xray
    xray_performed
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    assessment_reports
    audio
    audio_performed
    cannabis_performed
    clinic
    ecg
    ecg_performed
    employment
    evaluation_signoff
    exclusion
    exclusion_comment
    exclusions
    heat
    heat_performed
    height
    height_performed
    lab_test
    medical_examination_date
    medical_expiry_date
    medical_type
    name
    outcome
    outcome_comment
    patient
    physical_exam_performed
    referral
    referral_comment
    referrals
    spiro
    spiro_performed
    status
    visual
    visual_performed
    xray
    xray_performed
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how evaluations are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(evaluation)
  #   "Evaluation ##{evaluation.id}"
  # end
end
