require "administrate/base_dashboard"

class OrganisationDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    clinics: Field::Has<PERSON>any,
    companies: Field::<PERSON><PERSON><PERSON>,
    contact_number: Field::String,
    email_address: Field::String,
    initials: Field::String,
    organisation_name: Field::String,
    organisation_users: Field::Has<PERSON>any,
    patients: Field::HasMany,
    registration_number: Field::String,
    teams: Field::HasMany,
    users: Field::HasMany,
    web_address: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    clinics
    companies
    contact_number
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    clinics
    companies
    contact_number
    email_address
    initials
    organisation_name
    organisation_users
    patients
    registration_number
    teams
    users
    web_address
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    clinics
    companies
    contact_number
    email_address
    initials
    organisation_name
    organisation_users
    patients
    registration_number
    teams
    users
    web_address
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how organisations are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(organisation)
  #   "Organisation ##{organisation.id}"
  # end
end
