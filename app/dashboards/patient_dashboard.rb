require "administrate/base_dashboard"

class PatientDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    assessments: Field::Has<PERSON>any,
    audios: Field::Has<PERSON>any,
    dob: Field::Date,
    email: Field::String,
    employers: Field::Has<PERSON>any,
    employments: Field::HasMany,
    evaluations: Field::HasMany,
    exclusions: Field::<PERSON><PERSON>any,
    first_name: Field::String,
    gender: Field::String,
    identification_number: Field::String,
    lab_tests: Field::<PERSON><PERSON>any,
    last_name: Field::String,
    organisation: Field::BelongsTo,
    patient_documents: Field::HasMany,
    patient_notes: Field::HasMany,
    phone_number: Field::String,
    physicals: Field::HasMany,
    referrals: Field::HasMany,
    signatures: Field::Has<PERSON>any,
    spiros: Field::<PERSON><PERSON><PERSON>,
    vision_screening_uploads: Field::<PERSON><PERSON><PERSON>,
    vision_screenings: Field::<PERSON><PERSON>any,
    visuals: Field::<PERSON><PERSON><PERSON>,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    assessments
    audios
    dob
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    assessments
    audios
    dob
    email
    employers
    employments
    evaluations
    exclusions
    first_name
    gender
    identification_number
    lab_tests
    last_name
    organisation
    patient_documents
    patient_notes
    phone_number
    physicals
    referrals
    signatures
    spiros
    vision_screening_uploads
    vision_screenings
    visuals
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    assessments
    audios
    dob
    email
    employers
    employments
    evaluations
    exclusions
    first_name
    gender
    identification_number
    lab_tests
    last_name
    organisation
    patient_documents
    patient_notes
    phone_number
    physicals
    referrals
    signatures
    spiros
    vision_screening_uploads
    vision_screenings
    visuals
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how patients are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(patient)
  #   "Patient ##{patient.id}"
  # end
end
