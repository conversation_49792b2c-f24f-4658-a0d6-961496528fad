require "administrate/base_dashboard"

class PhysicalDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    arthrithis_chronic: Field::Boolean,
    assessment_reports: Field::HasMany,
    asthma_chronic: Field::Boolean,
    blood_pressure: Field::String,
    blood_sugar: Field::String,
    blood_test: Field::Boolean,
    cardiac_chronic: Field::Boolean,
    chest_exam: Field::Boolean,
    clinic: Field::BelongsTo,
    cns_exam: Field::Boolean,
    copd_chronic: Field::Boolean,
    cvs_exam: Field::Boolean,
    date_performed: Field::Date,
    diabetes_chronic: Field::Boolean,
    drug_chronic: Field::Boolean,
    endocrine_exam: Field::Boolean,
    ent_exam: Field::Boolean,
    epilepsy_chronic: Field::Boolean,
    gastro_exam: Field::Boolean,
    general_appearance: Field::String,
    glucose_exam: Field::Boolean,
    glucose_test: Field::Boolean,
    height: Field::String,
    hypertension_chronic: Field::Boolean,
    leucocytes_test: Field::Boolean,
    mental_chronic: Field::Boolean,
    musculo_exam: Field::Boolean,
    nad_test: Field::Boolean,
    nitrite_test: Field::Boolean,
    note: Field::Text,
    obesity_chronic: Field::Boolean,
    patient: Field::BelongsTo,
    performed_by: Field::String,
    peripheral_exam: Field::Boolean,
    physical_signoff: Field::HasOne,
    prosthesis_chronic: Field::Boolean,
    protein_test: Field::Boolean,
    pulse: Field::String,
    result: Field::String,
    skin_exam: Field::Boolean,
    status: Field::String,
    thyroid_chronic: Field::Boolean,
    urinary_exam: Field::Boolean,
    urine_test: Field::Boolean,
    weight: Field::String
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    arthrithis_chronic
    assessment_reports
    asthma_chronic
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    arthrithis_chronic
    assessment_reports
    asthma_chronic
    blood_pressure
    blood_sugar
    blood_test
    cardiac_chronic
    chest_exam
    clinic
    cns_exam
    copd_chronic
    cvs_exam
    date_performed
    diabetes_chronic
    drug_chronic
    endocrine_exam
    ent_exam
    epilepsy_chronic
    gastro_exam
    general_appearance
    glucose_exam
    glucose_test
    height
    hypertension_chronic
    leucocytes_test
    mental_chronic
    musculo_exam
    nad_test
    nitrite_test
    note
    obesity_chronic
    patient
    performed_by
    peripheral_exam
    physical_signoff
    prosthesis_chronic
    protein_test
    pulse
    result
    skin_exam
    status
    thyroid_chronic
    urinary_exam
    urine_test
    weight
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    arthrithis_chronic
    assessment_reports
    asthma_chronic
    blood_pressure
    blood_sugar
    blood_test
    cardiac_chronic
    chest_exam
    clinic
    cns_exam
    copd_chronic
    cvs_exam
    date_performed
    diabetes_chronic
    drug_chronic
    endocrine_exam
    ent_exam
    epilepsy_chronic
    gastro_exam
    general_appearance
    glucose_exam
    glucose_test
    height
    hypertension_chronic
    leucocytes_test
    mental_chronic
    musculo_exam
    nad_test
    nitrite_test
    note
    obesity_chronic
    patient
    performed_by
    peripheral_exam
    physical_signoff
    prosthesis_chronic
    protein_test
    pulse
    result
    skin_exam
    status
    thyroid_chronic
    urinary_exam
    urine_test
    weight
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how physicals are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(physical)
  #   "Physical ##{physical.id}"
  # end
end
