require "administrate/base_dashboard"

class UserDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    confirmation_sent_at: Field::DateTime,
    confirmation_token: Field::String,
    confirmed_at: Field::DateTime,
    current_sign_in_at: Field::DateTime,
    current_sign_in_ip: Field::String.with_options(searchable: false),
    default_organisation_id: Field::Number,
    email: Field::String,
    encrypted_password: Field::String,
    first_name: Field::String,
    invitation_accepted_at: Field::DateTime,
    invitation_created_at: Field::DateTime,
    invitation_limit: Field::Number,
    invitation_sent_at: Field::DateTime,
    invitation_token: Field::String,
    invitations_count: Field::Number,
    invited_by: Field::Polymorphic,
    is_active: Field::Boolean,
    last_name: Field::String,
    last_sign_in_at: Field::DateTime,
    last_sign_in_ip: Field::String.with_options(searchable: false),
    organisation_users: Field::HasMany,
    organisations: Field::HasMany,
    remember_created_at: Field::DateTime,
    reset_password_sent_at: Field::DateTime,
    reset_password_token: Field::String,
    sign_in_count: Field::Number,
    signatures: Field::HasMany,
    team_users: Field::HasMany,
    teams: Field::HasMany,
    unconfirmed_email: Field::String,
    user_mobile: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    confirmation_sent_at
    confirmation_token
    confirmed_at
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    confirmation_sent_at
    confirmation_token
    confirmed_at
    current_sign_in_at
    current_sign_in_ip
    default_organisation_id
    email
    encrypted_password
    first_name
    invitation_accepted_at
    invitation_created_at
    invitation_limit
    invitation_sent_at
    invitation_token
    invitations_count
    invited_by
    is_active
    last_name
    last_sign_in_at
    last_sign_in_ip
    organisation_users
    organisations
    remember_created_at
    reset_password_sent_at
    reset_password_token
    sign_in_count
    signatures
    team_users
    teams
    unconfirmed_email
    user_mobile
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    confirmation_sent_at
    confirmation_token
    confirmed_at
    current_sign_in_at
    current_sign_in_ip
    default_organisation_id
    email
    encrypted_password
    first_name
    invitation_accepted_at
    invitation_created_at
    invitation_limit
    invitation_sent_at
    invitation_token
    invitations_count
    invited_by
    is_active
    last_name
    last_sign_in_at
    last_sign_in_ip
    organisation_users
    organisations
    remember_created_at
    reset_password_sent_at
    reset_password_token
    sign_in_count
    signatures
    team_users
    teams
    unconfirmed_email
    user_mobile
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how users are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(user)
  #   "User ##{user.id}"
  # end
end
