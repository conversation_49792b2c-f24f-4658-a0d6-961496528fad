require "administrate/base_dashboard"

class VisionScreeningDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    assessment_reports: Field::HasMany,
    attachments: Field::Has<PERSON>any,
    clinic: Field::BelongsTo,
    color_discrimination: Field::String,
    comment: Field::Text,
    date_of_screening: Field::Date,
    date_signed: Field::Date,
    name: Field::String,
    patient: Field::BelongsTo,
    performed_by: Field::String,
    signed_by: Field::String,
    snellen_left_eye: Field::String,
    snellen_left_eye_without_glasses: Field::String,
    snellen_right_eye: Field::String,
    snellen_right_eye_without_glasses: Field::String,
    status: Field::String,
    temporal_left: Field::String,
    temporal_right: Field::String,
    total_left: Field::String,
    total_right: Field::String,
    vision_screening_signoff: Field::HasOne,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    assessment_reports
    attachments
    clinic
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    assessment_reports
    attachments
    clinic
    color_discrimination
    comment
    date_of_screening
    date_signed
    name
    patient
    performed_by
    signed_by
    snellen_left_eye
    snellen_left_eye_without_glasses
    snellen_right_eye
    snellen_right_eye_without_glasses
    status
    temporal_left
    temporal_right
    total_left
    total_right
    vision_screening_signoff
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    assessment_reports
    attachments
    clinic
    color_discrimination
    comment
    date_of_screening
    date_signed
    name
    patient
    performed_by
    signed_by
    snellen_left_eye
    snellen_left_eye_without_glasses
    snellen_right_eye
    snellen_right_eye_without_glasses
    status
    temporal_left
    temporal_right
    total_left
    total_right
    vision_screening_signoff
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how vision screenings are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(vision_screening)
  #   "VisionScreening ##{vision_screening.id}"
  # end
end
