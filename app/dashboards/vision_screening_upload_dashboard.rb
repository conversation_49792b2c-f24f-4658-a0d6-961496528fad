require "administrate/base_dashboard"

class VisionScreeningUploadDashboard < Administrate::BaseDashboard
  # ATTRIBUTE_TYPES
  # a hash that describes the type of each of the model's fields.
  #
  # Each different type represents an Administrate::Field object,
  # which determines how the attribute is displayed
  # on pages throughout the dashboard.
  ATTRIBUTE_TYPES = {
    id: Field::Number,
    attachments: Field::Has<PERSON>any,
    clinic: Field::BelongsTo,
    date_performed: Field::Date,
    name: Field::String,
    note: Field::Text,
    patient: Field::BelongsTo,
    performed_by: Field::String,
    result: Field::String,
    signature_date: Field::Date,
    signed_by: Field::String,
    status: Field::String,
    system_used: Field::String,
    created_at: Field::DateTime,
    updated_at: Field::DateTime
  }.freeze

  # COLLECTION_ATTRIBUTES
  # an array of attributes that will be displayed on the model's index page.
  #
  # By default, it's limited to four items to reduce clutter on index pages.
  # Feel free to add, remove, or rearrange items.
  COLLECTION_ATTRIBUTES = %i[
    id
    attachments
    clinic
    date_performed
  ].freeze

  # SHOW_PAGE_ATTRIBUTES
  # an array of attributes that will be displayed on the model's show page.
  SHOW_PAGE_ATTRIBUTES = %i[
    id
    attachments
    clinic
    date_performed
    name
    note
    patient
    performed_by
    result
    signature_date
    signed_by
    status
    system_used
    created_at
    updated_at
  ].freeze

  # FORM_ATTRIBUTES
  # an array of attributes that will be displayed
  # on the model's form (`new` and `edit`) pages.
  FORM_ATTRIBUTES = %i[
    attachments
    clinic
    date_performed
    name
    note
    patient
    performed_by
    result
    signature_date
    signed_by
    status
    system_used
  ].freeze

  # COLLECTION_FILTERS
  # a hash that defines filters that can be used while searching via the search
  # field of the dashboard.
  #
  # For example to add an option to search for open resources by typing "open:"
  # in the search field:
  #
  #   COLLECTION_FILTERS = {
  #     open: ->(resources) { resources.where(open: true) }
  #   }.freeze
  COLLECTION_FILTERS = {}.freeze

  # Overwrite this method to customize how vision screening uploads are displayed
  # across all pages of the admin dashboard.
  #
  # def display_resource(vision_screening_upload)
  #   "VisionScreeningUpload ##{vision_screening_upload.id}"
  # end
end
