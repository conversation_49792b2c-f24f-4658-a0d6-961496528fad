class AudioForm < Reform::Form
  property :note
  property :result
  property :name
  property :performed_by
  property :system_used
  property :clinic_id
  property :patient_id
  property :signed_by
  property :signature_date
  property :signed
  property :status
  property :date_performed

  collection :attachments, prepopulator: :build_attachment,
    populate_if_empty: Attachment do
    property :name
    property :description
    property :content
    property :remove
  end

  validates :clinic_id,
    :patient_id,
    :name,
    :attachments,
    :result, presence: true

  def save!
    sync
    create_audio
    @model
  end

  def update!
    sync
    @model.save!
    @model
  end

  private

  def build_attachment(*)
    attachments << Attachment.new if attachment.blank?
  end

  def create_audio
    @model.status = ENUMS::ASSESSMENT_STATUS::CAPTURED

    if signed == "1"
      @model[:signed_by] = signed_by
      @model[:signature_date] = signature_date
      @model[:status] = ENUMS::ASSESSMENT_STATUS::SIGNED_OFF
    end

    @model.save!
  end
end
