# frozen_string_literal: true

class CaptureMedicalForm
  include ActiveModel::Model

  attr_accessor(
    :evaluation,
    :patient_id,
    :employment_id,
    :clinic_id,
    :medical_type,
    :name,
    :physical_exam_performed,
    :vision_screening_performed,
    :physical_exam_performed,
    :spiro_performed,
    :audio_performed,
    :visual_performed,
    :xray_performed,
    :ecg_performed,
    :heat_performed,
    :height_performed,
    :cannabis_performed,
    :referral,
    :referral_comment,
    :exclusion,
    :exclusion_comment,
    :medical_examination_date,
    :medical_expiry_date,
    :outcome,
    :outcome_comment,
    :outcome_comment_text
  )

  validates :patient_id,
    :clinic_id,
    :employment_id, presence: true

  validate :medical_name_is_unique

  validates :referral_comment, presence: true, if: -> { referral == true }
  validates :exclusion_comment, presence: true, if: -> { exclusion == true }

  def save
    data = {
      success: false,
      evaluation: nil
    }

    if valid?
      @evaluation = Evaluation.create(
        audio_performed: audio_performed,
        cannabis_performed: cannabis_performed,
        ecg_performed: ecg_performed,
        heat_performed: heat_performed,
        height_performed: height_performed,
        physical_exam_performed: physical_exam_performed,
        spiro_performed: spiro_performed,
        visual_performed: vision_screening_performed,
        xray_performed: xray_performed,

        exclusion_comment: exclusion_comment,
        exclusion: exclusion,

        patient_id: patient_id,
        employment_id: employment_id,
        clinic_id: clinic_id,

        medical_type: medical_type,
        name: name,

        referral: referral,
        referral_comment: referral_comment,

        medical_examination_date: medical_examination_date,
        medical_expiry_date: medical_expiry_date,

        outcome: outcome,
        outcome_comment: outcome_comment_text,

        status: "draft"
      )
      data = {
        success: true,
        evaluation: @evaluation
      }
    end
    data
  end

  private

  def medical_name_is_unique
    if Evaluation.where(name: name).exists?
      errors.add(:name, "is taken")
    end
  end
end
