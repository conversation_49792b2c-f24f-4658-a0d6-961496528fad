# frozen_string_literal: true

class CapturePatientForm
  include ActiveModel::Model

  attr_accessor(
    :organisation_id,
    :first_name,
    :last_name,
    :identification_number,
    :email,
    :gender,
    :dob,
    :phone_number,
    :employment_department,
    :employment_start_date,
    :employment_position,
    :company_search,
    :company_input,
    :company_capture,
    :company,
    :patient,
    :employment
  )

  validates :first_name,
    :last_name,
    :dob,
    :gender,
    :identification_number, presence: true
  # validates :identification_number, uniqueness: true

  validates :employment_department,
    :employment_start_date,
    :employment_position, presence: true

  validates :company_capture, presence: true
  validates :company_input, presence: true, if: :capture_a_company?
  validates :company_search, presence: true, if: :use_an_existing_company?

  def save
    data = {
      success: false,
      patient: nil,
      company: nil
    }

    if valid?
      ActiveRecord::Base.transaction do
        @patient = create_patient
        @company = create_company
        @employment = create_employment(company_id: @company.id, patient_id: @patient.id)
      end
      data = {
        success: true,
        patient: @patient,
        company: @company,
        employment: @employment
      }
    end
    data
  end

  private

  def use_an_existing_company?
    company_capture == "existing"
  end

  def capture_a_company?
    company_capture == "add_company"
  end

  def create_company
    if @company_capture == "existing"
      @company = Company.find(@company_search)
    end
    if @company_capture == "add_company"
      @company = Company.create!(name: @company_input, organisation_id: @organisation_id)
    end
    @company
  end

  def create_patient
    Patient.create!(
      first_name: first_name,
      last_name: last_name,
      identification_number: identification_number,
      email: email,
      gender: gender,
      dob: dob,
      phone_number: phone_number,
      organisation_id: organisation_id
    )
  end

  def create_employment(company_id:, patient_id:)
    Employment.create!(
      department: employment_department,
      position: employment_position,
      employment_type: "employee",
      induction_date: employment_start_date,
      patient_id: patient_id,
      company_id: company_id
    )
  end
end
