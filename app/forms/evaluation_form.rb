class EvaluationForm < Reform::Form
  property :name
  property :medical_type
  property :physical_exam, virtual: true
  property :physical_exam_checkbox, virtual: true
  property :physical_exam_unlink, virtual: true
  property :visual, virtual: true
  property :visual_checkbox, virtual: true
  property :visual_unlink, virtual: true
  property :audio
  property :audio_checkbox, virtual: true
  property :audio_unlink, virtual: true
  property :spiro, virtual: true
  property :spiro_checkbox, virtual: true
  property :spiro_unlink, virtual: true
  property :xray, virtual: true
  property :xray_checkbox, virtual: true
  property :xray_unlink, virtual: true
  property :heat, virtual: true
  property :heat_checkbox, virtual: true
  property :heat_unlink, virtual: true
  property :height, virtual: true
  property :height_checkbox, virtual: true
  property :height_unlink, virtual: true
  property :drug, virtual: true
  property :drug_checkbox, virtual: true
  property :drug_unlink, virtual: true
  property :ecg_checkbox, virtual: true
  property :ecg_unlink, virtual: true
  property :ecg, virtual: true
  property :xray_checkbox, virtual: true
  property :xray_unlink, virtual: true
  property :xray, virtual: true
  property :outcome
  property :outcome_comment
  property :exclusion_ids
  property :referral_ids
  property :medical_examination_date
  property :medical_expiry_date
  property :patient_id
  property :employment_id
  property :clinic_id

  validates :drug, presence: true, if: -> { drug_checkbox == "on" && drug_unlink != "1" }
  validates :audio, presence: true, if: -> { audio_checkbox == "on" && audio_unlink != "1" }
  validates :physical_exam, presence: true, if: -> { physical_exam_checkbox == "on" && physical_exam_unlink != "1" }
  validates :spiro, presence: true, if: -> { spiro_checkbox == "on" && spiro_unlink != "1" }
  validates :visual, presence: true, if: -> { visual_checkbox == "on" && visual_unlink != "1" }
  validates :xray, presence: true, if: -> { xray_checkbox == "on" && xray_unlink != "1" }
  validates :ecg, presence: true, if: -> { ecg_checkbox == "on" && ecg_unlink != "1" }
  validates :heat, presence: true, if: -> { heat_checkbox == "on" && heat_unlink != "1" }
  validates :height, presence: true, if: -> { height_checkbox == "on" && height_unlink != "1" }

  validate :medical_name_is_unique

  validates :name, :outcome, :medical_type,
    :employment_id, :medical_examination_date,
    :medical_expiry_date, :clinic_id, presence: true

  def save!
    sync
    create_evaluation
    @model
  end

  def update!
    save!
  end

  private

  def create_evaluation
    @model.audio_performed = true if audio_checkbox == "on"
    @model.cannabis_performed = true if drug_checkbox == "on"
    @model.ecg_performed = true if ecg_checkbox == "on"
    @model.heat_performed = true if heat_checkbox == "on"
    @model.height_performed = true if height_checkbox == "on"
    @model.physical_exam_performed = true if physical_exam_checkbox == "on"
    @model.spiro_performed = true if spiro_checkbox == "on"
    @model.visual_performed = true if visual_checkbox == "on"
    @model.xray_performed = true if xray_checkbox == "on"

    if drug_checkbox == "on"
      @model.lab_test = drug
    end

    if drug_unlink == "1"
      @model.lab_test = nil
    end

    if audio_unlink == "1"
      @model.audio = nil
    end

    @model.save!
  end

  def medical_name_is_unique
    if Evaluation.where(name: name).exists? && @model.new_record?
      errors.add(:name, "is taken")
    end
  end
end
