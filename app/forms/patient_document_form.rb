class PatientDocumentForm
  include ActiveModel::Model

  attr_accessor(
    :name,
    :description,
    :patient_id,
    :uploaded_by,
    :attachments,
    :id,
    :created_at,
    :updated_at
  )

  validates :patient_id, :name, presence: true

  def save
    doc = {}

    if valid?
      doc = PatientDocument.new(
        name: name,
        description: description,
        patient_id: patient_id,
        uploaded_by: uploaded_by
      )
      if attachments.present?
        attachments.each do |att|
          attachment = ActiveStorage::Blob.create_and_upload!(io: att, filename: att.original_filename, content_type: att.content_type)
          doc.attachments.attach(attachment)
        end
      end
      doc.save!
    end

    doc
  end

  def update
    doc = PatientDocument.find_by(id: id)
    return false if doc.nil?

    doc.update(
      name: name,
      description: description,
      uploaded_by: uploaded_by,
      patient_id: patient_id
    )

    if attachments.present?
      attachments.each do |att|
        attachment = ActiveStorage::Blob.create_and_upload!(io: att, filename: att.original_filename, content_type: att.content_type)
        doc.attachments.attach(attachment)
      end
    end

    doc
  end
end
