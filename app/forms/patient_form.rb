# frozen_string_literal: true

class PatientForm < Reform::Form
  property :first_name
  property :last_name
  property :identification_number
  property :email
  property :gender
  property :dob
  property :phone_number
  property :organisation_id

  validates :first_name, presence: true
  validates :last_name, presence: true
  validates :dob, presence: true
  validates :gender, presence: true
  validates_uniqueness_of :identification_number
  validates :identification_number, presence: true

  def save!
    sync
    @model.save!
    @model
  end

  def update!
    save!
  end
end
