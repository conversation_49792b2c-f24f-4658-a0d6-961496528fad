class PhysicalAssessmentForm
  include ActiveModel::Model

  attr_accessor(
    :height,
    :weight,
    :blood_sugar,
    :blood_pressure,
    :pulse,
    :body_mass_index,
    :appearance_and_exam_comment,
    :urine,
    :nad,
    :leucocytes,
    :nitrite,
    :blood,
    :protein,
    :glucose,
    :peripheral_signs,
    :skin_or_appendages,
    :ent,
    :cvs,
    :chest,
    :gastro_intestinal_system,
    :urinary_system,
    :musculo_skeletal_system,
    :cns,
    :endocrine_system,
    :eye_acuity_l,
    :eye_acuity_r,
    :eye_acuity_both,
    :eye_field_l,
    :eye_field_r,
    :color_vision_normal,
    :night_vision_normal,
    :wearing_glasses_or_contacts,
    :fvc,
    :fev1,
    :fev1_fvc,
    :plh,
    :nihl,
    :chest_x_ray_comment,
    :abnormalities,
    :occupational_disorders,
    :chronic_hypertension,
    :chronic_drug_alcohol,
    :chronic_asthma,
    :chronic_thyroid,
    :chronic_epilepsy,
    :chronic_copd,
    :chronic_mental,
    :chronic_cardiac,
    :chronic_obesity,
    :chronic_prosthesis,
    :chronic_diabetes,
    :chronic_arthritis,
    :assessment_comment,
    :examination_date
  )
end
