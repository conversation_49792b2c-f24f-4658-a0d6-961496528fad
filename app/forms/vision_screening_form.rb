class VisionScreeningForm
  include ActiveModel::Model

  attr_accessor(
    :snellen_right_eye,
    :snellen_left_eye,
    :temporal_right,
    :temporal_left,
    :total_right,
    :total_left,
    :color_discrimination,
    :comment,
    :name,
    :date_of_screening,
    :patient_id,
    :performed_by,
    :clinic_id,
    :attachments_attributes
  )

  validates :snellen_right_eye, :snellen_left_eye, :temporal_right,
    :temporal_left, :total_right, :total_left,
    :color_discrimination, :date_of_screening,
    :patient_id, :performed_by, presence: true

  def save!
    create_vision_screening_assessment if valid?
  end

  def self.model_name
    ActiveModel::Name.new(self, nil, "Vision")
  end

  private

  def create_vision_screening_assessment
    data = {
      snellen_left_eye: snellen_left_eye,
      snellen_right_eye: snellen_right_eye,
      temporal_right: temporal_right,
      temporal_left: temporal_left,
      total_right: total_right,
      total_left: total_left,
      comment: comment,
      performed_by: performed_by,
      date_of_screening: date_of_screening,
      patient_id: patient_id,
      clinic_id: clinic_id
    }

    data[:color_discrimination] = if color_discrimination == "1"
      "yes"
    else
      "no"
    end

    vision = VisionScreening.new(data)
    vision.save!

    vision
  end
end
