module Mutations
  class CreateAudio < BaseMutation
    description "Create a Audiometry Assessment for a patient"

    field :audio, Types::AudioType, null: true
    field :errors, [Types::UserErrorType], null: false
    field :success, Bo<PERSON>an, null: false

    argument :input, Types::AudioInputType, required: true

    def resolve(input:)
      verify_requester!
      record = Patient.find_by(id: input.patient_id)

      if record.nil?
        return {
          success: false,
          audio: nil,
          errors: [{
            path: "id",
            message: "Patient not found"
          }]
        }
      end

      audio = Audio.new(
        name: input.name,
        result: input.result,
        performed_by: input.performed_by,
        patient_id: input.patient_id,
        clinic_id: input.clinic_id,
        note: input.note,
        system_used: input.system_used
      )

      if input.attachments.present?
        input.attachments.each do |att|
          attachment = ActiveStorage::Blob.create_and_upload!(io: att, filename: att.original_filename, content_type: att.content_type)
          audio.attachments.attach(attachment)
        end
      end

      if audio.save!
        {
          success: true,
          audio: audio,
          errors: []
        }
      else
        errors = audio.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          audio: nil,
          errors: errors
        }
      end
    end
  end
end
