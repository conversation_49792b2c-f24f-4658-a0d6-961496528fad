# frozen_string_literal: true

module Mutations
  class CreateClinic < BaseMutation
    description "Create a company for an organisation with an org id"

    field :clinic, Types::ClinicType, null: true
    field :errors, [Types::UserErrorType], null: false
    field :success, Boolean, null: false

    argument :input, Types::ClinicInputType, required: true
    argument :organisation_id, ID, required: true

    def resolve(organisation_id:, input:)
      verify_requester!
      record = Organisation.find_by(id: organisation_id)

      if record.nil?
        return {
          success: false,
          clinic: nil,
          errors: [{
            path: "id",
            message: "Organisation not found"
          }]
        }
      end

      data = input.to_h.merge!(organisation_id: organisation_id)
      clinic = Clinic.new(data)

      if clinic.save!
        {
          success: true,
          clinic: clinic,
          errors: []
        }
      else
        errors = clinic.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          clinic: nil,
          errors: errors
        }
      end
    end
  end
end
