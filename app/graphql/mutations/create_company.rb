# frozen_string_literal: true

module Mutations
  class CreateCompany < BaseMutation
    description "Create a company for an organisation with an org id"

    field :company, Types::CompanyType, null: true
    field :errors, [Types::UserErrorType], null: false
    field :success, Boolean, null: false

    argument :input, Types::CompanyInputType, required: true
    argument :organisation_id, ID, required: true

    def resolve(organisation_id:, input:)
      verify_requester!
      record = Organisation.find_by(id: organisation_id)

      if record.nil?
        return {
          success: false,
          company: nil,
          errors: [{
            path: "id",
            message: "Organisation not found"
          }]
        }
      end

      data = input.to_h.merge!(organisation_id: organisation_id)
      company = CompanyForm.new(Company.new)

      if company.validate(data)
        company.save!
        {
          success: true,
          company: company.model,
          errors: []
        }
      else
        errors = company.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          company: nil,
          errors: errors
        }
      end
    end
  end
end
