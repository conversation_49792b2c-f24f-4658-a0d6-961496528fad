module Mutations
  class CreateEmployment < BaseMutation
    description "Create an employment record for a patient "

    field :errors, [Types::UserErrorType], null: false
    field :employment, Types::EmploymentType, null: true
    field :success, Boolean, null: false

    argument :input, Types::EmploymentInputType, required: true

    def resolve(input:)
      verify_requester!

      company_record = Company.find_by(id: input.company_id)
      patient_record = Patient.find_by(id: input.patient_id)

      input_data = input.to_h

      if company_record.nil? || patient_record.nil?
        return {
          success: false,
          employment: nil,
          errors: [{
            path: "input",
            message: "Company or Patient not found"
          }]
        }
      end

      employment = Employment.new(input_data)

      if employment.save!
        {
          success: true,
          employment: employment,
          errors: []
        }
      else
        errors = employment.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          employment: nil,
          errors: errors
        }
      end
    end
  end
end
