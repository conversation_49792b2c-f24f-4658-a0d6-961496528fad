module Mutations
  class CreateLabTest < BaseMutation
    description "Create a Lab Test for a patient"

    field :lab_test, Types::LabTestType, null: true
    field :errors, [Types::UserErrorType], null: false
    field :success, <PERSON><PERSON>an, null: false

    argument :input, Types::LabTestInputType, required: true

    def resolve(input:)
      verify_requester!
      record = Patient.find_by(id: input.patient_id)

      if record.nil?
        return {
          success: false,
          lab_test: nil,
          errors: [{
            path: "id",
            message: "Patient not found"
          }]
        }
      end

      lab_test = LabTest.new(input.to_h)

      if lab_test.save!
        {
          success: true,
          lab_test: lab_test,
          errors: []
        }
      else
        errors = lab_test.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          lab_test: nil,
          errors: errors
        }
      end
    end
  end
end
