module Mutations
  class CreateLabTestReport < BaseMutation
    description "Create a lab test report for a patient"

    field :errors, [Types::UserErrorType], null: false
    field :labTest, Types::LabTestType, null: true
    field :success, Bo<PERSON>an, null: false

    argument :lab_test_id, ID, required: true

    def resolve(lab_test_id:)
      verify_requester!
      labTest = LabTest.find_by(id: lab_test_id)

      if labTest.nil?
        return {
          success: false,
          labTest: nil,
          errors: [{
            path: "id",
            message: "Drug Screening not found"
          }]
        }
      end

      if labTest.update!(status: "signed off") && LabTestSignoff.create!(user: requester, lab_test: labTest, date_signed: Date.today)

        GenerateLabTestReportWorker.perform_async(report_id: labTest.id, user_id: requester.id)

        {
          success: true,
          labTest: labTest,
          errors: []
        }
      else
        errors = labTest.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          labTest: nil,
          errors: errors
        }
      end
    end
  end
end
