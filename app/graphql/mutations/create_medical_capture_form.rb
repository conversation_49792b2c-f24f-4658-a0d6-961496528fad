module Mutations
  class CreateMedicalCaptureForm < BaseMutation
    description "Capture the details of a new medical form"

    field :errors, [Types::UserErrorType], null: false
    field :medical, Types::EvaluationType, null: true
    field :success, <PERSON><PERSON>an, null: false

    argument :input, Types::MedicalCaptureFormInputType, required: true

    def resolve(input:)
      verify_requester!
      clinic_record = Clinic.find_by(id: input.clinic_id)
      patient_record = Patient.find_by(id: input.patient_id)
      employment_record = Employment.find_by(id: input.employment_id)

      if clinic_record.nil?
        return missing_record(path: "clinic_id", message: "Clinic not found")
      end

      if patient_record.nil?
        return missing_record(path: "patient_id", message: "Patient not found")
      end

      if patient_record.nil?
        return missing_record(path: "patient_id", message: "Patient not found")
      end

      form = CaptureMedicalForm.new(input.to_h)

      if form.valid?
        record = form.save
        {
          success: true,
          evaluation: record[:evaluation],
          errors: []
        }
      else
        errors = form.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          evaluation: nil,
          errors: errors
        }
      end
    end

    private

    def missing_record(path:, message:)
      {
        success: false,
        patient: nil,
        errors: [{
          path: path,
          message: message
        }]
      }
    end
  end
end
