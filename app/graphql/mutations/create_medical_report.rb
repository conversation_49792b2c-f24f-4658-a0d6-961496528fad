module Mutations
  class CreateMedicalReport < BaseMutation
    description "Create a medical report for a patient"

    field :errors, [Types::UserErrorType], null: false
    field :medical, Types::EvaluationType, null: true
    field :success, Bo<PERSON>an, null: false

    argument :medical_id, ID, required: true

    def resolve(medical_id:)
      verify_requester!
      medical = Evaluation.find_by(id: medical_id)

      if medical.nil?
        return {
          success: false,
          medical: nil,
          errors: [{
            path: "id",
            message: "Medical not found"
          }]
        }
      end

      if medical.update!(status: ENUMS::ASSESSMENT_STATUS::SIGNED_OFF) && EvaluationSignoff.create!(user: requester, evaluation: medical, date_signed: Date.today)
        GenerateEvaluationReportWorker.perform_async(report_id: medical.id, user_id: requester.id)

        {
          success: true,
          medical: medical,
          errors: []
        }
      else
        errors = medical.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          patient: nil,
          errors: errors
        }
      end
    end
  end
end
