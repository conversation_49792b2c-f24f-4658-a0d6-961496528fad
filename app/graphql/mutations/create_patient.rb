# frozen_string_literal: true

module Mutations
  class CreatePatient < BaseMutation
    description "Create a patient for an organisation with an org id"

    field :errors, [Types::UserErrorType], null: false
    field :patient, Types::PatientType, null: true
    field :success, Boolean, null: false

    argument :input, Types::PatientInputType, required: true
    argument :organisation_id, ID, required: true

    def resolve(organisation_id:, input:)
      verify_requester!
      record = Organisation.find_by(id: organisation_id)

      if record.nil?
        return {
          success: false,
          patient: nil,
          errors: [{
            path: "id",
            message: "Organisation not found"
          }]
        }
      end

      data = input.to_h.merge!(organisation_id: organisation_id)
      patient = PatientForm.new(Patient.new)

      if patient.validate(data)
        patient.save!
        {
          success: true,
          patient: patient.model,
          errors: []
        }
      else
        errors = patient.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          patient: nil,
          errors: errors
        }
      end
    end
  end
end
