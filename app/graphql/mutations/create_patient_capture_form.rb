module Mutations
  class CreatePatientCaptureForm < BaseMutation
    description "Capture the details of a new patient form"

    field :errors, [Types::UserErrorType], null: false
    field :patient, Types::PatientType, null: true
    field :success, Bo<PERSON>an, null: false

    argument :input, Types::PatientCaptureFormInputType, required: true
    argument :organisation_id, ID, required: true

    def resolve(organisation_id:, input:)
      verify_requester!
      record = Organisation.find_by(id: organisation_id)

      if record.nil?
        return {
          success: false,
          patient: nil,
          errors: [{
            path: "id",
            message: "Organisation not found"
          }]
        }
      end

      data = input.to_h.merge(organisation_id: organisation_id)
      form = CapturePatientForm.new(data)

      if form.valid?
        record = form.save
        {
          success: true,
          patient: record[:patient],
          errors: []
        }
      else
        errors = form.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          patient: nil,
          errors: errors
        }
      end
    end
  end
end
