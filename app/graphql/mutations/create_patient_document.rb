module Mutations
  class CreatePatientDocument < BaseMutation
    description "Create a patient document for a patient"

    field :errors, [Types::UserErrorType], null: false
    field :patient_document, Types::PatientDocumentType, null: true
    field :success, Boolean, null: false

    argument :input, Types::PatientDocumentInputType, required: true
    argument :patient_id, ID, required: true

    def resolve(patient_id:, input:)
      verify_requester!
      record = Patient.find_by(id: patient_id)

      if record.nil?
        return {
          success: false,
          patient_document: nil,
          errors: [{
            path: "id",
            message: "Patient not found"
          }]
        }
      end

      data = input.to_h.merge!(patient_id: patient_id)
      doc = PatientDocumentForm.new(data)

      if doc.valid?
        record_created = doc.save
        {
          success: true,
          patient_document: record_created,
          errors: []
        }
      else
        errors = doc.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          patient_document: nil,
          errors: errors
        }
      end
    end
  end
end
