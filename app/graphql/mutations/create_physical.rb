module Mutations
  class CreatePhysical < BaseMutation
    description "Create a Physical Assessment for a patient"

    field :physical, Types::PhysicalType, null: true
    field :errors, [Types::UserErrorType], null: false
    field :success, Bo<PERSON>an, null: false

    argument :input, Types::PhysicalInputType, required: true

    def resolve(input:)
      verify_requester!
      record = Patient.find_by(id: input.patient_id)

      if record.nil?
        return {
          success: false,
          physical: nil,
          errors: [{
            path: "id",
            message: "Patient not found"
          }]
        }
      end

      physical = Physical.new(input.to_h)

      if physical.save!
        {
          success: true,
          physical: physical,
          errors: []
        }
      else
        errors = physical.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          physical: nil,
          errors: errors
        }
      end
    end
  end
end
