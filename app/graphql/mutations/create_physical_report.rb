module Mutations
  class CreatePhysicalReport < BaseMutation
    description "Create a physical report for a patient"

    field :errors, [Types::UserErrorType], null: false
    field :physical, Types::PhysicalType, null: true
    field :success, Bo<PERSON>an, null: false

    argument :physical_id, ID, required: true

    def resolve(physical_id:)
      verify_requester!
      physical = Physical.find_by(id: physical_id)

      if physical.nil?
        return {
          success: false,
          physical: nil,
          errors: [{
            path: "id",
            message: "Physical not found"
          }]
        }
      end

      if physical.update!(status: ENUMS::ASSESSMENT_STATUS::SIGNED_OFF) && PhysicalSignoff.create!(user: requester, physical: physical, date_signed: Date.today)
        GeneratePhysicalReportWorker.perform_async(report_id: physical.id, user_id: requester.id)

        {
          success: true,
          physical: physical,
          errors: []
        }
      else
        errors = physical.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          physical: nil,
          errors: errors
        }
      end
    end
  end
end
