module Mutations
  class CreateSpiro < BaseMutation
    description "Create a Spirometry Assessment for a patient"

    field :spiro, Types::SpiroType, null: true
    field :errors, [Types::UserErrorType], null: false
    field :success, Bo<PERSON>an, null: false

    argument :input, Types::SpiroInputType, required: true

    def resolve(input:)
      verify_requester!
      record = Patient.find_by(id: input.patient_id)

      if record.nil?
        return {
          success: false,
          spiro: nil,
          errors: [{
            path: "id",
            message: "Patient not found"
          }]
        }
      end

      spiro = Spiro.new(
        name: input.name,
        result: input.result,
        performed_by: input.performed_by,
        patient_id: input.patient_id,
        clinic_id: input.clinic_id,
        note: input.note,
        system_used: input.system_used
      )

      if input.attachments.present?
        input.attachments.each do |att|
          attachment = ActiveStorage::Blob.create_and_upload!(io: att, filename: att.original_filename, content_type: att.content_type)
          spiro.attachments.attach(attachment)
        end
      end

      if spiro.save!
        {
          success: true,
          spiro: spiro,
          errors: []
        }
      else
        errors = spiro.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          spiro: nil,
          errors: errors
        }
      end
    end
  end
end
