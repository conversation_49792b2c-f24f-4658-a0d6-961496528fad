module Mutations
  class CreateVision < BaseMutation
    description "Create a Vision Screening Assessment for a patient"

    field :vision, Types::VisionScreeningType, null: true
    field :errors, [Types::UserErrorType], null: false
    field :success, <PERSON><PERSON><PERSON>, null: false

    argument :input, Types::VisionScreeningInputType, required: true

    def resolve(input:)
      verify_requester!
      record = Patient.find_by(id: input.patient_id)

      if record.nil?
        return {
          success: false,
          vision: nil,
          errors: [{
            path: "id",
            message: "Patient not found"
          }]
        }
      end

      vision = VisionScreening.new(input.to_h)

      if vision.save!
        {
          success: true,
          vision: vision,
          errors: []
        }
      else
        errors = vision.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          vision: nil,
          errors: errors
        }
      end
    end
  end
end
