module Mutations
  class CreateVisionScreeningReport < BaseMutation
    description "Create a vision screening report for a patient"

    field :errors, [Types::UserErrorType], null: false
    field :visionScreening, Types::VisionScreeningType, null: true
    field :success, <PERSON><PERSON>an, null: false

    argument :vision_screening_id, ID, required: true

    def resolve(vision_screening_id:)
      verify_requester!
      vision = VisionScreening.find_by(id: vision_screening_id)

      if vision.nil?
        return {
          success: false,
          visionScreening: nil,
          errors: [{
            path: "id",
            message: "Vision Screening not found"
          }]
        }
      end

      if vision.update!(status: "signed off") && VisionScreeningSignoff.create!(user: requester, vision_screening: vision, date_signed: Date.today)
        GenerateVisionScreeningReportWorker.perform_async(report_id: vision.id, user_id: requester.id)

        {
          success: true,
          visionScreening: vision,
          errors: []
        }
      else
        errors = vision.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          visionScreening: nil,
          errors: errors
        }
      end
    end
  end
end
