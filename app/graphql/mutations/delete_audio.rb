module Mutations
  class DeleteAudio < BaseMutation
    description "delete an audio by id"

    field :audio, Types::AudioType, null: true
    field :errors, [Types::UserErrorType], null: false
    field :success, Bo<PERSON>an, null: false

    argument :id, ID, required: true

    def resolve(id:)
      verify_requester!
      audio = Audio.find_by(id: id)

      if audio.nil?
        return {
          success: false,
          audio: nil,
          errors: [{
            path: "id",
            message: "Audio not found"
          }]
        }
      end

      if audio.destroy
        {
          success: true,
          audio: audio,
          errors: []
        }
      else
        errors = audio.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          audio: nil,
          errors: errors
        }
      end
    end
  end
end
