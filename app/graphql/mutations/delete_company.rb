# frozen_string_literal: true

module Mutations
  class DeleteCompany < BaseMutation
    description "deletes a company by id"

    field :company, Types::CompanyType, null: true
    field :errors, [Types::UserErrorType], null: false
    field :success, Bo<PERSON>an, null: false

    argument :id, ID, required: true

    def resolve(id:)
      verify_requester!
      company = Company.find_by(id: id)

      if company.nil?
        return {
          success: false,
          company: nil,
          errors: [{
            path: "id",
            message: "Company not found"
          }]
        }
      end

      if company.destroy
        {
          success: true,
          company: company,
          errors: []
        }
      else
        errors = company.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          company: nil,
          errors: errors
        }
      end
    end
  end
end
