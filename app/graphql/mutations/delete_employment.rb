module Mutations
  class DeleteEmployment < BaseMutation
    description "deletes a patient by id"

    field :errors, [Types::UserErrorType], null: false
    field :employment, Types::EmploymentType, null: true
    field :success, Bo<PERSON>an, null: false

    argument :id, ID, required: true

    def resolve(id:)
      verify_requester!
      employment = Employment.find_by(id: id)

      if employment.nil?
        return {
          success: false,
          employment: nil,
          errors: [{
            path: "id",
            message: "Employment record not found"
          }]
        }
      end

      if employment.destroy
        {
          success: true,
          employment: employment,
          errors: []
        }
      else
        errors = employment.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end
        {
          success: false,
          employment: nil,
          errors: errors
        }
      end
    end
  end
end
