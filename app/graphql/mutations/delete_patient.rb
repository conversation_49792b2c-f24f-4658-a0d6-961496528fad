# frozen_string_literal: true

module Mutations
  class DeletePatient < BaseMutation
    description "deletes a patient by id"

    field :errors, [Types::UserErrorType], null: false
    field :patient, Types::PatientType, null: true
    field :success, <PERSON><PERSON>an, null: false

    argument :id, ID, required: true

    def resolve(id:)
      verify_requester!
      patient = Patient.find_by(id: id)

      if patient.nil?
        return {
          success: false,
          patient: nil,
          errors: [{
            path: "id",
            message: "Patient not found"
          }]
        }
      end

      if patient.destroy
        {
          success: true,
          patient: patient,
          errors: []
        }
      else
        errors = patient.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end
        {
          success: false,
          patient: nil,
          errors: errors
        }
      end
    end
  end
end
