# frozen_string_literal: true

module Mutations
  class DeletePatientDocument < BaseMutation
    description "Delete a patient documet by id"

    field :patient_document, Types::PatientDocumentType, null: true
    field :errors, [Types::UserErrorType], null: false
    field :success, <PERSON><PERSON>an, null: false

    argument :id, ID, required: true

    def resolve(id:)
      # verify_requester!
      doc = PatientDocument.find_by(id: id)

      if doc.nil?
        return {
          success: false,
          patient_document: nil,
          errors: [{
            path: "id",
            message: "Patient Document not found"
          }]
        }
      end

      if doc.destroy
        {
          success: true,
          patient_document: doc,
          errors: []
        }
      else
        errors = doc.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          patient_document: nil,
          errors: errors
        }
      end
    end
  end
end
