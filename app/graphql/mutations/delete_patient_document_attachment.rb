# frozen_string_literal: true

module Mutations
  class DeletePatientDocumentAttachment < BaseMutation
    description "Delete a patient documet attachment by id"

    field :errors, [Types::UserErrorType], null: false
    field :success, Bo<PERSON><PERSON>, null: false

    argument :id, ID, required: true

    def resolve(id:)
      # verify_requester!
      doc = ActiveStorage::Attachment.find_by(id: id)

      if doc.nil?
        return {
          success: false,
          errors: [{
            path: "id",
            message: "Attachment not found"
          }]
        }
      end

      doc.purge

      {
        success: true,
        errors: []
      }
    end
  end
end
