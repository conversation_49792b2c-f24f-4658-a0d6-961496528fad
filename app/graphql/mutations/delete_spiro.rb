module Mutations
  class DeleteSpiro < BaseMutation
    description "delete an Spiro by id"

    field :spiro, Types::SpiroType, null: true
    field :errors, [Types::UserErrorType], null: false
    field :success, <PERSON><PERSON><PERSON>, null: false

    argument :id, ID, required: true

    def resolve(id:)
      verify_requester!
      spiro = Spiro.find_by(id: id)

      if spiro.nil?
        return {
          success: false,
          spiro: nil,
          errors: [{
            path: "id",
            message: "Spiro not found"
          }]
        }
      end

      if spiro.destroy
        {
          success: true,
          spiro: spiro,
          errors: []
        }
      else
        errors = spiro.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          spiro: nil,
          errors: errors
        }
      end
    end
  end
end
