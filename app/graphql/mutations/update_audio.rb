module Mutations
  class UpdateAudio < BaseMutation
    description "Update a Audiometry Assessment for a patient"

    field :audio, Types::AudioType, null: true
    field :errors, [Types::UserErrorType], null: false
    field :success, Boolean, null: false

    argument :input, Types::AudioInputType, required: true

    def resolve(input:)
      verify_requester!
      audio = Audio.find_by(id: input.patient_id)

      if audio.nil?
        return {
          success: false,
          audio: nil,
          errors: [{
            path: "id",
            message: "Audio not found"
          }]
        }
      end

      if input.attachments.present?
        attachments.each do |att|
          attachment = ActiveStorage::Blob.create_and_upload!(io: att, filename: att.original_filename, content_type: att.content_type)
          audio.attachments.attach(attachment)
        end
      end

      if audio.update!(input.to_h)
        {
          success: true,
          audio: audio,
          errors: []
        }
      else
        errors = audio.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          audio: nil,
          errors: errors
        }
      end
    end
  end
end
