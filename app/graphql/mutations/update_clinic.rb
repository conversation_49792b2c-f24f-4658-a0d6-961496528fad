# frozen_string_literal: true

module Mutations
  class UpdateClinic < BaseMutation
    description "Updates a clinic by id"

    field :clinic, Types::ClinicType, null: true
    field :errors, [Types::UserErrorType], null: false
    field :success, <PERSON><PERSON>an, null: false

    argument :id, ID, required: true
    argument :input, Types::ClinicInputType, required: true

    def resolve(id:, input:)
      verify_requester!
      clinic = Clinic.find_by(id: id)

      if clinic.nil?
        return {
          success: false,
          clinic: nil,
          errors: [{
            path: "id",
            message: "Clinic not found"
          }]
        }
      end

      if clinic.update!(input.to_h)
        {
          success: true,
          clinic: clinic,
          errors: []
        }
      else
        errors = clinic.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          clinic: nil,
          errors: errors
        }
      end
    end
  end
end
