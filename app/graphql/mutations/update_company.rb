# frozen_string_literal: true

module Mutations
  class UpdateCompany < BaseMutation
    description "Updates a company by id"

    field :company, Types::CompanyType, null: true
    field :errors, [Types::UserErrorType], null: false
    field :success, Bo<PERSON>an, null: false

    argument :id, ID, required: true
    argument :input, Types::CompanyInputType, required: true

    def resolve(id:, input:)
      verify_requester!
      record = Company.find_by(id: id)

      if record.nil?
        return {
          success: false,
          company: nil,
          errors: [{
            path: "id",
            message: "Company not found"
          }]
        }
      end

      company = CompanyForm.new(record)

      if company.validate(input.to_h)
        company.update!
        {
          success: true,
          company: company.model,
          errors: []
        }
      else
        errors = company.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          company: nil,
          errors: errors
        }
      end
    end
  end
end
