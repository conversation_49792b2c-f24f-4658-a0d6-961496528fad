# frozen_string_literal: true

module Mutations
  class UpdatePatient < BaseMutation
    description "Updates a patient by id"

    field :errors, [Types::UserErrorType], null: false
    field :patient, Types::PatientType, null: true
    field :success, <PERSON><PERSON>an, null: false

    argument :id, ID, required: true
    argument :input, Types::PatientInputType, required: true

    def resolve(id:, input:)
      verify_requester!
      record = Patient.find_by(id: id)

      if record.nil?
        return {
          success: false,
          patient: nil,
          errors: [{
            path: "id",
            message: "Patient not found"
          }]
        }
      end

      patient = PatientForm.new(record)

      if patient.validate(input.to_h)
        patient.update!
        {
          success: true,
          patient: patient.model,
          errors: []
        }
      else
        errors = patient.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          patient: nil,
          errors: errors
        }
      end
    end
  end
end
