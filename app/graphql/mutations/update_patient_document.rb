module Mutations
  class UpdatePatientDocument < BaseMutation
    description "Update a patient document by ID"

    field :errors, [Types::UserErrorType], null: false
    field :patient_document, Types::PatientDocumentType, null: true
    field :success, Boolean, null: false

    argument :input, Types::PatientDocumentInputType, required: true
    argument :id, ID, required: true

    def resolve(id:, input:)
      verify_requester!
      record = PatientDocument.find_by(id: id)

      if record.nil?
        return {
          success: false,
          patient_document: nil,
          errors: [{
            path: "id",
            message: "Patient Document not found"
          }]
        }
      end

      data = input.to_h.merge!(id: id)
      doc = PatientDocumentForm.new(data)

      if doc.valid?
        record_updated = doc.update
        {
          success: true,
          patient_document: record_updated,
          errors: []
        }
      else
        errors = doc.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          patient_document: nil,
          errors: errors
        }
      end
    end
  end
end
