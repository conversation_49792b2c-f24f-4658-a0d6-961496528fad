module Mutations
  class UpdateSpiro < BaseMutation
    description "Update a Spirometry Assessment for a patient"

    field :spiro, Types::SpiroType, null: true
    field :errors, [Types::UserErrorType], null: false
    field :success, Bo<PERSON>an, null: false

    argument :input, Types::SpiroInputType, required: true

    def resolve(input:)
      verify_requester!
      spiro = Spiro.find_by(id: input.patient_id)

      if spiro.nil?
        return {
          success: false,
          spiro: nil,
          errors: [{
            path: "id",
            message: "Spiro not found"
          }]
        }
      end

      if input.attachments.present?
        attachments.each do |att|
          attachment = ActiveStorage::Blob.create_and_upload!(io: att, filename: att.original_filename, content_type: att.content_type)
          spiro.attachments.attach(attachment)
        end
      end

      if spiro.update!(input.to_h)
        {
          success: true,
          spiro: spiro,
          errors: []
        }
      else
        errors = spiro.errors.map do |error|
          {
            path: error.attribute.to_s.camelize(:lower),
            message: error.full_message
          }
        end

        {
          success: false,
          spiro: nil,
          errors: errors
        }
      end
    end
  end
end
