module Queries
  class ExpiredMedicals < Queries::BaseQuery
    description "Find all expired medicals by organisation, grouped by company and ordered by expiry date"

    argument :organisation_id, ID, required: true

    type [Types::ExpiredMedicalType], null: false

    def resolve(organisation_id:)
      verify_requester!
      
      # Get all evaluations with expired medical_expiry_date
      expired_evaluations = ::Evaluation.joins(:patient, employment: :company)
        .where(patients: { organisation_id: organisation_id })
        .where('medical_expiry_date < ?', Date.current)
        .includes(:patient, employment: :company)
        .order('companies.name ASC, medical_expiry_date ASC')

      # Transform the data to include company grouping
      expired_evaluations.map do |evaluation|
        {
          id: evaluation.id,
          full_name: evaluation.patient.full_name,
          contact_number: evaluation.patient.phone_number,
          id_number: evaluation.patient.identification_number,
          company: evaluation.employment.company.name,
          medical_ref: evaluation.name,
          medical_expiry_date: evaluation.medical_expiry_date,
          patient: evaluation.patient,
          employment: evaluation.employment,
          evaluation: evaluation
        }
      end
    end
  end
end
