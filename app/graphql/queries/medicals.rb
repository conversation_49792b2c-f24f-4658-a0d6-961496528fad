module Queries
  class Medicals < Queries::BaseQuery
    description "Find all medicals by organisation"

    argument :organisation_id, ID, required: true

    type [Types::EvaluationType], null: false

    def resolve(organisation_id:)
      ::Evaluation.joins(:patient)
        .where(patients: {organisation_id: organisation_id})
        .preload(:employment, :patient)
        .includes(employment: :company)
        .order("created_at DESC")
    end
  end
end
