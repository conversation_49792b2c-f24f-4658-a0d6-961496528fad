module Queries
  class RecentPatients < Queries::BaseQuery
    description "Find all patients within the last 3 weeks"

    argument :organisation_id, ID, required: true

    type [Types::PatientType], null: false

    def resolve(organisation_id:)
      verify_requester!
      ::Patient.where(organisation_id: organisation_id).where("updated_at >= ?", 3.weeks.ago).order("created_at DESC")
    end
  end
end
