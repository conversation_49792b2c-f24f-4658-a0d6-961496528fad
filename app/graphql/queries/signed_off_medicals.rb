module Queries
  class SignedOffMedicals < Queries::BaseQuery
    description "Find all draft medicals by organisation"

    argument :organisation_id, ID, required: true

    type [Types::EvaluationType], null: false

    def resolve(organisation_id:)
      verify_requester!

      ::Evaluation.joins(:patient)
        .where("patient.organisation_id": organisation_id, status: "signed_off")
        .order("created_at DESC")
    end
  end
end
