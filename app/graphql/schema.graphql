"""
An ISO 8601-encoded date
"""
scalar ISO8601Date

"""
An ISO 8601-encoded datetime
"""
scalar ISO8601DateTime

type Mutation {
  """
  Updates a patient by id
  """
  patientUpdate(id: ID!, input: PatientInput!): PatientUpdatePayload

  """
  An example field added by the generator
  """
  testField: String!
}

type Patient {
  createdAt: ISO8601DateTime!
  dob: ISO8601Date
  email: String
  firstName: String
  gender: String
  id: ID!
  identificationNumber: String
  lastName: String
  organisationId: Int!
  phoneNumber: String
  updatedAt: ISO8601DateTime!
}

input PatientInput {
  dob: ISO8601Date
  email: String
  firstName: String
  gender: String
  identificationNumber: String
  lastName: String
  organisationId: Int
  phoneNumber: String
}

"""
Autogenerated return type of PatientUpdate
"""
type PatientUpdatePayload {
  errors: [UserError!]!
  patient: Patient
}

type Query {
  """
  Find an patient by ID
  """
  patient(id: ID!): Patient!

  """
  Find all patient
  """
  patients: [Patient!]!
}

"""
A user-readable error
"""
type UserError {
  """
  A description of the error
  """
  message: String!

  """
  Which input value this error came from
  """
  path: [String!]
}
