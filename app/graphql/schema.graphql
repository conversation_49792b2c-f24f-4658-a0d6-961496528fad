type AssessmentReport {
  assessableId: Int!
  assessableType: String!
  createdAt: ISO8601DateTime!
  dateGenerated: ISO8601Date
  fileName: String
  generatedBy: String
  id: ID!
  reportData: String
  updatedAt: ISO8601DateTime!
  url: String
}

type Audio {
  attachments: [UploadActiveStorage!]
  clinic: Clinic
  clinicId: Int
  createdAt: ISO8601DateTime!
  datePerformed: ISO8601Date
  id: ID!
  name: String
  note: String
  patient: Patient
  patientId: Int
  performedBy: String
  result: String
  systemUsed: String
  updatedAt: ISO8601DateTime!
}

input AudioInput {
  attachments: [Upload!]
  clinicId: Int!
  createdAt: ISO8601DateTime
  datePerformed: ISO8601Date
  id: ID
  name: String!
  note: String
  patientId: Int!
  performedBy: String
  result: String
  systemUsed: String
  updatedAt: ISO8601DateTime
}

input ChangePasswordInput {
  confirmPassword: String!
  currentPassword: String!
  newPassword: String!
}

type ChangePasswordResponse {
  errors: [Error!]!
  message: String
  success: Boolean!
}

type Clinic {
  clinicName: String
  createdAt: ISO8601DateTime!
  details: String
  id: ID!
  organisationId: Int!
  phoneNumber: String
  physicalAddress: String
  physicalAddress2: String
  updatedAt: ISO8601DateTime!
}

input ClinicInput {
  clinicName: String
  organisationId: Int
  phoneNumber: String
  physicalAddress: String
  physicalAddress2: String
}

type Company {
  about: String
  city: String
  createdAt: ISO8601DateTime!
  email: String
  employees: [Patient!]
  employments: [Employment!]
  id: ID!
  industrySector: String
  name: String
  organisationId: Int!
  phoneNumber: String
  streetAddress: String
  suburb: String
  updatedAt: ISO8601DateTime!
}

"""
The connection type for Company.
"""
type CompanyConnection {
  """
  A list of edges.
  """
  edges: [CompanyEdge]

  """
  A list of nodes.
  """
  nodes: [Company]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
}

"""
An edge in a connection.
"""
type CompanyEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: Company
}

input CompanyInput {
  about: String
  city: String
  email: String
  industrySector: String
  name: String
  organisationId: Int
  phoneNumber: String
  streetAddress: String
  suburb: String
}

"""
Autogenerated return type of CreateAudio.
"""
type CreateAudioPayload {
  audio: Audio
  errors: [UserError!]!
  success: Boolean!
}

"""
Autogenerated return type of CreateClinic.
"""
type CreateClinicPayload {
  clinic: Clinic
  errors: [UserError!]!
  success: Boolean!
}

"""
Autogenerated return type of CreateCompany.
"""
type CreateCompanyPayload {
  company: Company
  errors: [UserError!]!
  success: Boolean!
}

"""
Autogenerated return type of CreateEmployment.
"""
type CreateEmploymentPayload {
  employment: Employment
  errors: [UserError!]!
  success: Boolean!
}

"""
Autogenerated return type of CreateLabTest.
"""
type CreateLabTestPayload {
  errors: [UserError!]!
  labTest: LabTest
  success: Boolean!
}

"""
Autogenerated return type of CreateLabTestReport.
"""
type CreateLabTestReportPayload {
  errors: [UserError!]!
  labTest: LabTest
  success: Boolean!
}

"""
Autogenerated return type of CreateMedicalCaptureForm.
"""
type CreateMedicalCaptureFormPayload {
  errors: [UserError!]!
  medical: Evaluation
  success: Boolean!
}

"""
Autogenerated return type of CreateMedicalReport.
"""
type CreateMedicalReportPayload {
  errors: [UserError!]!
  medical: Evaluation
  success: Boolean!
}

"""
Autogenerated return type of CreatePatientCaptureForm.
"""
type CreatePatientCaptureFormPayload {
  errors: [UserError!]!
  patient: Patient
  success: Boolean!
}

"""
Autogenerated return type of CreatePatientDocument.
"""
type CreatePatientDocumentPayload {
  errors: [UserError!]!
  patientDocument: PatientDocument
  success: Boolean!
}

"""
Autogenerated return type of CreatePatient.
"""
type CreatePatientPayload {
  errors: [UserError!]!
  patient: Patient
  success: Boolean!
}

"""
Autogenerated return type of CreatePhysical.
"""
type CreatePhysicalPayload {
  errors: [UserError!]!
  physical: Physical
  success: Boolean!
}

"""
Autogenerated return type of CreatePhysicalReport.
"""
type CreatePhysicalReportPayload {
  errors: [UserError!]!
  physical: Physical
  success: Boolean!
}

"""
Autogenerated return type of CreateSpiro.
"""
type CreateSpiroPayload {
  errors: [UserError!]!
  spiro: Spiro
  success: Boolean!
}

"""
Autogenerated return type of CreateVision.
"""
type CreateVisionPayload {
  errors: [UserError!]!
  success: Boolean!
  vision: VisionScreening
}

"""
Autogenerated return type of CreateVisionScreeningReport.
"""
type CreateVisionScreeningReportPayload {
  errors: [UserError!]!
  success: Boolean!
  visionScreening: VisionScreening
}

"""
Autogenerated return type of DeleteAudio.
"""
type DeleteAudioPayload {
  audio: Audio
  errors: [UserError!]!
  success: Boolean!
}

"""
Autogenerated return type of DeleteCompany.
"""
type DeleteCompanyPayload {
  company: Company
  errors: [UserError!]!
  success: Boolean!
}

"""
Autogenerated return type of DeleteEmployment.
"""
type DeleteEmploymentPayload {
  employment: Employment
  errors: [UserError!]!
  success: Boolean!
}

"""
Autogenerated return type of DeletePatientDocumentAttachment.
"""
type DeletePatientDocumentAttachmentPayload {
  errors: [UserError!]!
  success: Boolean!
}

"""
Autogenerated return type of DeletePatientDocument.
"""
type DeletePatientDocumentPayload {
  errors: [UserError!]!
  patientDocument: PatientDocument
  success: Boolean!
}

"""
Autogenerated return type of DeletePatient.
"""
type DeletePatientPayload {
  errors: [UserError!]!
  patient: Patient
  success: Boolean!
}

"""
Autogenerated return type of DeleteSpiro.
"""
type DeleteSpiroPayload {
  errors: [UserError!]!
  spiro: Spiro
  success: Boolean!
}

type Employment {
  company: Company
  companyId: Int!
  companyName: String
  createdAt: ISO8601DateTime!
  department: String
  employmentType: String
  id: ID!
  inductionDate: ISO8601Date
  patient: Patient
  patientId: Int!
  position: String
  terminationDate: ISO8601Date
  terminationReason: String
  updatedAt: ISO8601DateTime!
}

input EmploymentInput {
  companyId: Int!
  department: String!
  employmentType: String!
  id: ID
  inductionDate: ISO8601Date!
  patientId: Int!
  position: String!
  terminationDate: ISO8601Date
  terminationReason: String
}

type Error {
  message: String!
  path: [String!]!
}

type Evaluation {
  assessmentReports: [AssessmentReport!]
  audioId: Int
  audioPerformed: Boolean!
  cannabisPerformed: Boolean!
  clinic: Clinic
  clinicId: Int
  createdAt: ISO8601DateTime!
  ecgPerformed: Boolean!
  employment: Employment
  employmentId: Int
  exclusion: Boolean!
  exclusionComment: String
  heatPerformed: Boolean!
  heightPerformed: Boolean!
  id: ID!
  labTestId: Int
  medicalExaminationDate: ISO8601Date
  medicalExpiryDate: ISO8601Date
  medicalType: String
  name: String
  outcome: String
  outcomeComment: String
  patient: Patient
  patientId: Int!
  physicalExamPerformed: Boolean!
  referral: Boolean
  referralComment: String
  spiroPerformed: Boolean!
  status: String
  updatedAt: ISO8601DateTime!
  visualPerformed: Boolean!
  xrayPerformed: Boolean!
}

"""
An ISO 8601-encoded date
"""
scalar ISO8601Date @specifiedBy(url: "https://tools.ietf.org/html/rfc3339")

"""
An ISO 8601-encoded datetime
"""
scalar ISO8601DateTime @specifiedBy(url: "https://tools.ietf.org/html/rfc3339")

type LabTest {
  assessmentReports: [AssessmentReport!]
  ast: String
  cannabis: String
  clinic: Clinic
  clinicId: Int!
  comment: String
  createdAt: ISO8601DateTime
  datePerformed: ISO8601Date
  dateSigned: ISO8601Date
  fbc: String
  gamma: String
  hiv: String
  id: ID!
  patient: Patient
  patientId: Int
  performedBy: String
  reportName: String
  signedBy: String
  sixPanel: String
  status: String
  updatedAt: ISO8601DateTime
}

input LabTestInput {
  ast: String
  cannabis: String
  clinicId: Int!
  comment: String
  datePerformed: ISO8601Date!
  fbc: String
  gamma: String
  hiv: String
  id: ID
  patientId: Int!
  performedBy: String!
  sixPanel: String
}

input MedicalCaptureFormInput {
  audioPerformed: Boolean
  cannabisPerformed: Boolean
  clinicId: String!
  ecgPerformed: Boolean
  employmentId: String!
  exclusion: Boolean
  exclusionComment: String
  heatPerformed: Boolean
  heightPerformed: Boolean
  medicalExaminationDate: ISO8601Date!
  medicalExpiryDate: ISO8601Date!
  medicalType: String
  name: String
  outcome: String!
  outcomeComment: Boolean!
  outcomeCommentText: String!
  patientId: String!
  physicalExamPerformed: Boolean
  referral: Boolean
  referralComment: String
  spiroPerformed: Boolean
  visionScreeningPerformed: Boolean
  visualPerformed: Boolean
  xrayPerformed: Boolean
}

type Mutation {
  changePassword(input: ChangePasswordInput!): ChangePasswordResponse

  """
  Create a Audiometry Assessment for a patient
  """
  createAudio(input: AudioInput!): CreateAudioPayload

  """
  Create a company for an organisation with an org id
  """
  createClinic(input: ClinicInput!, organisationId: ID!): CreateClinicPayload

  """
  Create a company for an organisation with an org id
  """
  createCompany(input: CompanyInput!, organisationId: ID!): CreateCompanyPayload

  """
  Create an employment record for a patient 
  """
  createEmployment(input: EmploymentInput!): CreateEmploymentPayload

  """
  Create a Lab Test for a patient
  """
  createLabTest(input: LabTestInput!): CreateLabTestPayload

  """
  Create a lab test report for a patient
  """
  createLabTestReport(labTestId: ID!): CreateLabTestReportPayload

  """
  Capture the details of a new medical form
  """
  createMedicalCaptureForm(input: MedicalCaptureFormInput!): CreateMedicalCaptureFormPayload

  """
  Create a medical report for a patient
  """
  createMedicalReport(medicalId: ID!): CreateMedicalReportPayload

  """
  Create a patient for an organisation with an org id
  """
  createPatient(input: PatientInput!, organisationId: ID!): CreatePatientPayload

  """
  Capture the details of a new patient form
  """
  createPatientCaptureForm(input: PatientCaptureFormInput!, organisationId: ID!): CreatePatientCaptureFormPayload

  """
  Create a patient document for a patient
  """
  createPatientDocument(input: PatientDocumentInput!, patientId: ID!): CreatePatientDocumentPayload

  """
  Create a Physical Assessment for a patient
  """
  createPhysical(input: PhysicalInput!): CreatePhysicalPayload

  """
  Create a physical report for a patient
  """
  createPhysicalReport(physicalId: ID!): CreatePhysicalReportPayload

  """
  Create a Spirometry Assessment for a patient
  """
  createSpiro(input: SpiroInput!): CreateSpiroPayload

  """
  Create a Vision Screening Assessment for a patient
  """
  createVision(input: VisionScreeningInput!): CreateVisionPayload

  """
  Create a vision screening report for a patient
  """
  createVisionScreeningReport(visionScreeningId: ID!): CreateVisionScreeningReportPayload

  """
  delete an audio by id
  """
  deleteAudio(id: ID!): DeleteAudioPayload

  """
  deletes a company by id
  """
  deleteCompany(id: ID!): DeleteCompanyPayload

  """
  deletes a patient by id
  """
  deleteEmployment(id: ID!): DeleteEmploymentPayload

  """
  deletes a patient by id
  """
  deletePatient(id: ID!): DeletePatientPayload

  """
  Delete a patient documet by id
  """
  deletePatientDocument(id: ID!): DeletePatientDocumentPayload

  """
  Delete a patient documet attachment by id
  """
  deletePatientDocumentAttachment(id: ID!): DeletePatientDocumentAttachmentPayload

  """
  delete an Spiro by id
  """
  deleteSpiro(id: ID!): DeleteSpiroPayload
  login(email: String!, password: String!): User!

  """
  Update a Audiometry Assessment for a patient
  """
  updateAudio(input: AudioInput!): UpdateAudioPayload

  """
  Updates a clinic by id
  """
  updateClinic(id: ID!, input: ClinicInput!): UpdateClinicPayload

  """
  Updates a company by id
  """
  updateCompany(id: ID!, input: CompanyInput!): UpdateCompanyPayload

  """
  Update an employment record for a patient 
  """
  updateEmployment(input: EmploymentInput!): UpdateEmploymentPayload

  """
  Updates a patient by id
  """
  updatePatient(id: ID!, input: PatientInput!): UpdatePatientPayload

  """
  Update a patient document by ID
  """
  updatePatientDocument(id: ID!, input: PatientDocumentInput!): UpdatePatientDocumentPayload

  """
  Update a Spirometry Assessment for a patient
  """
  updateSpiro(input: SpiroInput!): UpdateSpiroPayload
  updateUserProfile(input: UserProfileInput!): UserProfileResponse
}

type Organisation {
  companies(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): CompanyConnection!
  contactNumber: String
  createdAt: ISO8601DateTime!
  emailAddress: String
  id: ID!
  initials: String
  organisationName: String
  patients(
    """
    Returns the elements in the list that come after the specified cursor.
    """
    after: String

    """
    Returns the elements in the list that come before the specified cursor.
    """
    before: String

    """
    Returns the first _n_ elements from the list.
    """
    first: Int

    """
    Returns the last _n_ elements from the list.
    """
    last: Int
  ): PatientConnection!
  registrationNumber: String
  updatedAt: ISO8601DateTime!
  webAddress: String
}

"""
Information about pagination in a connection.
"""
type PageInfo {
  """
  When paginating forwards, the cursor to continue.
  """
  endCursor: String

  """
  When paginating forwards, are there more items?
  """
  hasNextPage: Boolean!

  """
  When paginating backwards, are there more items?
  """
  hasPreviousPage: Boolean!

  """
  When paginating backwards, the cursor to continue.
  """
  startCursor: String
}

type Patient {
  createdAt: ISO8601DateTime!
  dob: ISO8601Date
  email: String
  employers: [Company!]
  employments: [Employment!]
  firstName: String
  fullName: String
  gender: String
  id: ID!
  identificationNumber: String
  lastName: String
  medicals: [Evaluation!]
  organisationId: Int!
  phoneNumber: String
  updatedAt: ISO8601DateTime!
}

input PatientCaptureFormInput {
  companyCapture: String
  companyInput: String
  companySearch: String
  dob: ISO8601Date
  email: String
  employmentDepartment: String
  employmentPosition: String
  employmentStartDate: ISO8601Date
  firstName: String
  gender: String
  identificationNumber: String
  lastName: String
  organisationId: Int
  phoneNumber: String
}

"""
The connection type for Patient.
"""
type PatientConnection {
  """
  A list of edges.
  """
  edges: [PatientEdge]

  """
  A list of nodes.
  """
  nodes: [Patient]

  """
  Information to aid in pagination.
  """
  pageInfo: PageInfo!
}

type PatientDocument {
  attachments: [UploadActiveStorage!]
  createdAt: ISO8601DateTime!
  description: String
  id: ID!
  name: String
  patientId: Int!
  updatedAt: ISO8601DateTime!
  uploadedBy: String
}

input PatientDocumentInput {
  attachments: [Upload!]
  description: String
  name: String
  patientId: Int
  uploadedBy: String
}

"""
An edge in a connection.
"""
type PatientEdge {
  """
  A cursor for use in pagination.
  """
  cursor: String!

  """
  The item at the end of the edge.
  """
  node: Patient
}

input PatientInput {
  dob: ISO8601Date
  email: String
  firstName: String
  gender: String
  identificationNumber: String
  lastName: String
  organisationId: Int
  phoneNumber: String
}

type Physical {
  arthrithisChronic: Boolean
  assessmentReports: [AssessmentReport!]
  asthmaChronic: Boolean
  bloodPressure: String
  bloodSugar: String
  bloodTest: Boolean
  cardiacChronic: Boolean
  chestExam: Boolean
  clinic: Clinic
  clinicId: Int!
  cnsExam: Boolean
  copdChronic: Boolean
  cvsExam: Boolean
  datePerformed: ISO8601Date
  diabetesChronic: Boolean
  drugChronic: Boolean
  endocrineExam: Boolean
  entExam: Boolean
  epilepsyChronic: Boolean
  gastroExam: Boolean
  generalAppearance: String
  glucoseExam: Boolean
  glucoseTest: Boolean
  height: String
  hypertensionChronic: Boolean
  id: ID!
  leucocytesTest: Boolean
  mentalChronic: Boolean
  musculoExam: Boolean
  nadTest: Boolean
  nitriteTest: Boolean
  note: String
  obesityChronic: Boolean
  patient: Patient
  patientId: Int!
  performedBy: String
  peripheralExam: Boolean
  prosthesisChronic: Boolean
  proteinTest: Boolean
  pulse: String
  result: String
  skinExam: Boolean
  status: String
  thyroidChronic: Boolean
  urinaryExam: Boolean
  urineTest: Boolean
  weight: String
}

input PhysicalInput {
  arthrithisChronic: Boolean
  asthmaChronic: Boolean
  bloodPressure: String
  bloodSugar: String
  bloodTest: Boolean
  cardiacChronic: Boolean
  chestExam: Boolean
  clinicId: Int
  cnsExam: Boolean
  copdChronic: Boolean
  cvsExam: Boolean
  datePerformed: ISO8601Date
  diabetesChronic: Boolean
  drugChronic: Boolean
  endocrineExam: Boolean
  entExam: Boolean
  epilepsyChronic: Boolean
  gastroExam: Boolean
  generalAppearance: String
  glucoseExam: Boolean
  glucoseTest: Boolean
  height: String
  hypertensionChronic: Boolean
  leucocytesTest: Boolean
  mentalChronic: Boolean
  musculoExam: Boolean
  nadTest: Boolean
  nitriteTest: Boolean
  note: String
  obesityChronic: Boolean
  patientId: Int
  performedBy: String
  peripheralExam: Boolean
  prosthesisChronic: Boolean
  proteinTest: Boolean
  pulse: String
  result: String
  skinExam: Boolean
  status: String
  thyroidChronic: Boolean
  urinaryExam: Boolean
  urineTest: Boolean
  weight: String
}

type Query {
  """
  Find an audio by id
  """
  audio(id: ID!): Audio

  """
  Find all audios for a patient
  """
  audios(patientId: ID!): [Audio!]

  """
  Find an clinic by ID
  """
  clinic(id: ID!): Clinic

  """
  Find all companies for an organisation
  """
  clinics(organisationId: ID!): [Clinic!]!

  """
  Find all companies for an organisation
  """
  companies(organisationId: ID!): [Company!]!

  """
  Find an company by ID
  """
  company(id: ID!): Company

  """
  Get the currently authenticated user
  """
  currentUser: User

  """
  Find all draft medicals by organisation
  """
  draftMedicals(organisationId: ID!): [Evaluation!]!

  """
  Find employment record
  """
  employment(id: ID!): Employment!

  """
  Find all employment records by company
  """
  employmentsByCompany(id: ID!): [Employment!]!

  """
  Find all employment records by patient
  """
  employmentsByPatient(id: ID!): [Employment!]!

  """
  Find an lab test by id
  """
  labTest(id: ID!): LabTest

  """
  Find all lab tests for a patient
  """
  labTests(patientId: ID!): [LabTest!]
  me: User!

  """
  Find a medical by ID
  """
  medical(id: ID!): Evaluation

  """
  Find all medicals by organisation
  """
  medicals(organisationId: ID!): [Evaluation!]!

  """
  Find an org by ID
  """
  organisation(id: ID!): Organisation

  """
  Find an patient by ID
  """
  patient(id: ID!): Patient

  """
  Find patients documents by ID
  """
  patientDocument(id: ID!): PatientDocument

  """
  Find all patients documents for a patient
  """
  patientDocuments(patientId: ID!): [PatientDocument!]!

  """
  Find all patients
  """
  patients(organisationId: ID!): [Patient!]!

  """
  Find a physical by id
  """
  physical(id: ID!): Physical

  """
  Find all physicals for a patient
  """
  physicals(patientId: ID!): [Physical!]

  """
  Find all patients within the last 3 weeks
  """
  recentPatients(organisationId: ID!): [Patient!]!

  """
  Find all draft medicals by organisation
  """
  signedOffMedicals(organisationId: ID!): [Evaluation!]!

  """
  Find an spiro by id
  """
  spiro(id: ID!): Spiro

  """
  Find all Spiros for a patient
  """
  spiros(patientId: ID!): [Spiro!]
  user(id: ID!): User

  """
  Find a vision screening by id
  """
  visionScreening(id: ID!): VisionScreening

  """
  Find all Vision Screenings for a patient
  """
  visionScreenings(patientId: ID!): [VisionScreening!]
}

type Spiro {
  attachments: [UploadActiveStorage!]
  clinic: Clinic
  clinicId: Int!
  datePerformed: ISO8601Date
  id: ID!
  name: String
  note: String
  patient: Patient
  patientId: Int!
  performedBy: String
  result: String
  status: String
  systemUsed: String
}

input SpiroInput {
  attachments: [Upload!]
  clinicId: Int
  datePerformed: ISO8601Date
  id: ID
  name: String
  note: String
  patientId: Int
  performedBy: String
  result: String
  status: String
  systemUsed: String
}

"""
Autogenerated return type of UpdateAudio.
"""
type UpdateAudioPayload {
  audio: Audio
  errors: [UserError!]!
  success: Boolean!
}

"""
Autogenerated return type of UpdateClinic.
"""
type UpdateClinicPayload {
  clinic: Clinic
  errors: [UserError!]!
  success: Boolean!
}

"""
Autogenerated return type of UpdateCompany.
"""
type UpdateCompanyPayload {
  company: Company
  errors: [UserError!]!
  success: Boolean!
}

"""
Autogenerated return type of UpdateEmployment.
"""
type UpdateEmploymentPayload {
  employment: Employment
  errors: [UserError!]!
  success: Boolean!
}

"""
Autogenerated return type of UpdatePatientDocument.
"""
type UpdatePatientDocumentPayload {
  errors: [UserError!]!
  patientDocument: PatientDocument
  success: Boolean!
}

"""
Autogenerated return type of UpdatePatient.
"""
type UpdatePatientPayload {
  errors: [UserError!]!
  patient: Patient
  success: Boolean!
}

"""
Autogenerated return type of UpdateSpiro.
"""
type UpdateSpiroPayload {
  errors: [UserError!]!
  spiro: Spiro
  success: Boolean!
}

scalar Upload

type UploadActiveStorage {
  contentType: String
  fileName: String
  id: ID
  url: String
}

type User {
  createdAt: ISO8601DateTime!
  defaultOrganisationId: Int
  email: String!
  firstName: String
  fullName: String
  id: ID!
  lastName: String
  organisationId: Int
  profilePicture: String
  token: String
  updatedAt: ISO8601DateTime!
  userMobile: String
}

"""
A user-readable error
"""
type UserError {
  """
  A description of the error
  """
  message: String

  """
  Which input value this error came from
  """
  path: String
}

input UserProfileInput {
  email: String
  firstName: String
  lastName: String
  profilePicture: String
}

type UserProfileResponse {
  errors: [Error!]!
  success: Boolean!
  user: User
}

type VisionScreening {
  assessmentReports: [AssessmentReport!]
  clinic: Clinic
  clinicId: Int
  colorDiscrimination: String
  comment: String
  createdAt: ISO8601DateTime!
  dateOfScreening: ISO8601Date
  dateSigned: ISO8601Date
  id: ID!
  name: String
  patient: Patient
  patientId: Int!
  performedBy: String
  reportName: String
  signedBy: String
  snellenLeftEye: String
  snellenLeftEyeWithoutGlasses: String
  snellenRightEye: String
  snellenRightEyeWithoutGlasses: String
  status: String
  temporalLeft: String
  temporalRight: String
  totalLeft: String
  totalRight: String
  updatedAt: ISO8601DateTime!
}

input VisionScreeningInput {
  clinicId: Int!
  colorDiscrimination: String!
  comment: String
  dateOfScreening: ISO8601Date!
  id: ID
  name: String
  patientId: Int!
  performedBy: String
  snellenLeftEye: String
  snellenLeftEyeWithoutGlasses: String
  snellenRightEye: String
  snellenRightEyeWithoutGlasses: String
  status: String
  temporalLeft: String
  temporalRight: String
  totalLeft: String
  totalRight: String
}
