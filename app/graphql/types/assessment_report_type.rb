# frozen_string_literal: true

module Types
  class AssessmentReportType < Types::BaseObject
    field :id, ID, null: false
    field :report_data, String
    field :date_generated, GraphQL::Types::ISO8601Date
    field :generated_by, String
    field :assessable_type, String, null: false
    field :assessable_id, Integer, null: false
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
    field :url, String
    field :file_name, String

    def url
      object.report.url
    end

    def file_name
      object.report.filename.to_s
    end
  end
end
