# frozen_string_literal: true

module Types
  class AudioInputType < Types::BaseInputObject
    argument :id, ID, required: false
    argument :name, String, required: true
    argument :result, String, required: false
    argument :performed_by, String, required: false
    argument :date_performed, GraphQL::Types::ISO8601Date, required: false
    argument :created_at, GraphQL::Types::ISO8601DateTime, required: false
    argument :updated_at, GraphQL::Types::ISO8601DateTime, required: false
    argument :patient_id, Integer, required: true
    argument :clinic_id, Integer, required: true
    argument :note, String, required: false
    argument :system_used, String, required: false
    argument :attachments, [ApolloUploadServer::Upload], required: false
  end
end
