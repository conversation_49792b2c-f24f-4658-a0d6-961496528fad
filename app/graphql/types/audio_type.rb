# frozen_string_literal: true

module Types
  class AudioType < Types::BaseObject
    field :id, ID, null: false
    field :name, String
    field :result, String
    field :performed_by, String
    field :date_performed, GraphQL::Types::ISO8601Date
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
    field :patient_id, Integer
    field :clinic_id, Integer
    field :note, String
    field :system_used, String
    field :attachments, [Types::UploadActiveStorageType]
    field :clinic, Types::ClinicType
    field :patient, Types::PatientType
  end
end
