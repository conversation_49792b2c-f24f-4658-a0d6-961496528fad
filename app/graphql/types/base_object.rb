module Types
  class BaseObject < GraphQL::Schema::Object
    edge_type_class(Types::BaseEdge)
    connection_type_class(Types::BaseConnection)
    field_class Types::BaseField

    private

    def requester
      context[:requester]
    end

    def verify_requester!
      if requester.blank?
        raise GraphQL::ExecutionError, "Authentication failed, you must be signed in!"
      end
    end
  end
end
