# frozen_string_literal: true

module Types
  class ClinicType < Types::BaseObject
    field :clinic_name, String
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :details, String
    field :id, ID, null: false
    field :organisation_id, Integer, null: false
    field :phone_number, String
    field :physical_address, String
    field :physical_address_2, String
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
  end
end
