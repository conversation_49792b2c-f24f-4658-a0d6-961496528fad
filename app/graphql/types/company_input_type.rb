# frozen_string_literal: true

module Types
  class CompanyInputType < Types::BaseInputObject
    argument :about, String, required: false
    argument :city, String, required: false
    argument :email, String, required: false
    argument :industry_sector, String, required: false
    argument :name, String, required: false
    argument :phone_number, String, required: false
    argument :street_address, String, required: false
    argument :suburb, String, required: false

    argument :organisation_id, Integer, required: false
  end
end
