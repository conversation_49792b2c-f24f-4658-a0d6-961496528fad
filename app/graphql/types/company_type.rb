# frozen_string_literal: true

module Types
  class CompanyType < Types::BaseObject
    field :about, String
    field :city, String
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :email, String
    field :employees, [Types::PatientType]
    field :employments, [Types::EmploymentType]
    field :id, ID, null: false
    field :industry_sector, String
    field :name, String
    field :organisation_id, Integer, null: false
    field :phone_number, String
    field :street_address, String
    field :suburb, String
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
  end
end
