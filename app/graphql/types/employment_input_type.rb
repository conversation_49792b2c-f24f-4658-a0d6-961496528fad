# frozen_string_literal: true

module Types
  class EmploymentInputType < Types::BaseInputObject
    argument :id, ID, required: false
    argument :patient_id, Integer, required: true
    argument :company_id, Integer, required: true
    argument :induction_date, GraphQL::Types::ISO8601Date, required: true
    argument :termination_date, GraphQL::Types::ISO8601Date, required: false
    argument :position, String, required: true
    argument :department, String, required: true
    argument :employment_type, String, required: true
    argument :termination_reason, String, required: false
  end
end
