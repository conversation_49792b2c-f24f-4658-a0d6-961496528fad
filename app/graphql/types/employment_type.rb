# frozen_string_literal: true

module Types
  class EmploymentType < Types::BaseObject
    field :company, Types::CompanyType, null: true
    field :company_id, Integer, null: false
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :department, String
    field :employment_type, String
    field :id, ID, null: false
    field :induction_date, GraphQL::Types::ISO8601Date
    field :patient, Types::PatientType
    field :patient_id, Integer, null: false
    field :position, String
    field :termination_date, GraphQL::Types::ISO8601Date
    field :termination_reason, String
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
    field :company_name, String
  end
end
