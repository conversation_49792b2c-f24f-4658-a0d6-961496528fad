# frozen_string_literal: true

module Types
  class EvaluationType < Types::BaseObject
    field :audio_id, Integer
    field :audio_performed, <PERSON><PERSON><PERSON>, null: false
    field :cannabis_performed, <PERSON><PERSON><PERSON>, null: false
    field :clinic_id, Integer
    field :clinic, Types::ClinicType
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :ecg_performed, <PERSON><PERSON>an, null: false
    field :employment_id, Integer
    field :exclusion, Boolean, null: false
    field :exclusion_comment, String
    field :heat_performed, <PERSON><PERSON><PERSON>, null: false
    field :height_performed, <PERSON><PERSON>an, null: false
    field :id, ID, null: false
    field :lab_test_id, Integer
    field :medical_examination_date, GraphQL::Types::ISO8601Date
    field :medical_expiry_date, GraphQL::Types::ISO8601Date
    field :medical_type, String
    field :name, String
    field :outcome, String
    field :outcome_comment, String
    field :patient_id, Integer, null: false
    field :physical_exam_performed, Boolean, null: false
    field :referral, Boolean
    field :referral_comment, String
    field :spiro_performed, <PERSON><PERSON><PERSON>, null: false
    field :status, String
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
    field :visual_performed, <PERSON><PERSON><PERSON>, null: false
    field :xray_performed, <PERSON><PERSON><PERSON>, null: false
    field :patient, Types::PatientType
    field :employment, Types::EmploymentType
    field :assessment_reports, [Types::AssessmentReportType]
  end
end
