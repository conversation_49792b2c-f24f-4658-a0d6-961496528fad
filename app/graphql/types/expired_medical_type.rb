# frozen_string_literal: true

module Types
  class ExpiredMedicalType < Types::BaseObject
    field :id, ID, null: false
    field :full_name, String, null: false
    field :contact_number, String, null: true
    field :id_number, String, null: false
    field :company, String, null: false
    field :medical_ref, String, null: false
    field :medical_expiry_date, GraphQL::Types::ISO8601Date, null: false
    field :patient, Types::PatientType, null: false
    field :employment, Types::EmploymentType, null: false
    field :evaluation, Types::EvaluationType, null: false
  end
end
