# frozen_string_literal: true

module Types
  class LabTestInputType < Types::BaseInputObject
    argument :id, ID, required: false
    argument :cannabis, String, required: false
    argument :six_panel, String, required: false
    argument :gamma, String, required: false
    argument :ast, String, required: false
    argument :fbc, String, required: false
    argument :hiv, String, required: false
    argument :comment, String, required: false
    argument :clinic_id, Integer, required: true
    argument :date_performed, GraphQL::Types::ISO8601Date, required: true
    argument :performed_by, String, required: true
    argument :patient_id, Integer, required: true
  end
end
