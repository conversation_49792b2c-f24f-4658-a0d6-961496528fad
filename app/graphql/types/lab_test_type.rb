# frozen_string_literal: true

module Types
  class LabTestType < Types::BaseObject
    field :id, ID, null: false
    field :cannabis, String
    field :six_panel, String
    field :gamma, String
    field :ast, String
    field :fbc, String
    field :hiv, String
    field :comment, String
    field :clinic_id, Integer, null: false
    field :status, String
    field :signed_by, String
    field :date_signed, GraphQL::Types::ISO8601Date
    field :date_performed, GraphQL::Types::ISO8601Date
    field :report_name, String
    field :performed_by, String
    field :patient_id, Integer
    field :created_at, GraphQL::Types::ISO8601DateTime
    field :updated_at, GraphQL::Types::ISO8601DateTime
    field :assessment_reports, [Types::AssessmentReportType]
    field :clinic, Types::ClinicType
    field :patient, Types::PatientType
  end
end
