# frozen_string_literal: true

module Types
  class MedicalCaptureFormInputType < Types::BaseInputObject
    argument :patient_id, String, required: true
    argument :employment_id, String, required: true
    argument :clinic_id, String, required: true

    argument :medical_type, String, required: false
    argument :name, String, required: false

    argument :audio_performed, <PERSON><PERSON><PERSON>, required: false
    argument :cannabis_performed, <PERSON><PERSON><PERSON>, required: false
    argument :ecg_performed, <PERSON><PERSON><PERSON>, required: false
    argument :heat_performed, <PERSON><PERSON><PERSON>, required: false
    argument :height_performed, <PERSON><PERSON><PERSON>, required: false
    argument :physical_exam_performed, <PERSON><PERSON><PERSON>, required: false
    argument :spiro_performed, <PERSON><PERSON><PERSON>, required: false
    argument :vision_screening_performed, <PERSON><PERSON><PERSON>, required: false
    argument :visual_performed, <PERSON><PERSON><PERSON>, required: false
    argument :xray_performed, <PERSON><PERSON><PERSON>, required: false

    argument :referral, <PERSON><PERSON><PERSON>, required: false
    argument :referral_comment, String, required: false

    argument :exclusion, <PERSON><PERSON>an, required: false
    argument :exclusion_comment, String, required: false

    argument :medical_examination_date, GraphQL::Types::ISO8601Date, required: true
    argument :medical_expiry_date, GraphQL::Types::ISO8601Date, required: true

    argument :outcome, String, required: true
    argument :outcome_comment, <PERSON><PERSON><PERSON>, required: true
    argument :outcome_comment_text, String, required: true
  end
end
