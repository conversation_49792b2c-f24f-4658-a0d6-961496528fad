module Types
  class MutationType < Types::BaseObject
    field :create_patient, mutation: Mutations::CreatePatient
    field :delete_patient, mutation: Mutations::DeletePatient
    field :update_patient, mutation: Mutations::UpdatePatient

    field :create_company, mutation: Mutations::CreateCompany
    field :delete_company, mutation: Mutations::DeleteCompany
    field :update_company, mutation: Mutations::UpdateCompany

    field :create_employment, mutation: Mutations::CreateEmployment
    field :delete_employment, mutation: Mutations::DeleteEmployment
    field :update_employment, mutation: Mutations::UpdateEmployment

    field :create_patient_capture_form, mutation: Mutations::CreatePatientCaptureForm

    field :create_patient_document, mutation: Mutations::CreatePatientDocument
    field :update_patient_document, mutation: Mutations::UpdatePatientDocument
    field :delete_patient_document, mutation: Mutations::DeletePatientDocument
    field :delete_patient_document_attachment, mutation: Mutations::DeletePatientDocumentAttachment

    field :create_medical_capture_form, mutation: Mutations::CreateMedicalCaptureForm

    field :create_clinic, mutation: Mutations::CreateClinic
    field :update_clinic, mutation: Mutations::UpdateClinic

    field :create_medical_report, mutation: Mutations::CreateMedicalReport
    field :create_physical_report, mutation: Mutations::CreatePhysicalReport
    field :create_lab_test_report, mutation: Mutations::CreateLabTestReport
    field :create_vision_screening_report, mutation: Mutations::CreateVisionScreeningReport

    field :create_lab_test, mutation: Mutations::CreateLabTest

    field :create_vision, mutation: Mutations::CreateVision

    field :create_audio, mutation: Mutations::CreateAudio
    field :delete_audio, mutation: Mutations::DeleteAudio
    field :update_audio, mutation: Mutations::UpdateAudio

    field :create_spiro, mutation: Mutations::CreateSpiro
    field :delete_spiro, mutation: Mutations::DeleteSpiro
    field :update_spiro, mutation: Mutations::UpdateSpiro

    field :create_physical, mutation: Mutations::CreatePhysical

    field :login, Types::UserType, null: false do
      argument :email, String, required: true
      argument :password, String, required: true
    end

    def login(email:, password:)
      account = User.find_by(email: email)
      if account&.valid_password?(password)
        account.token = account.to_sgid(expires_in: 36.hours, for: "graphql")
        account
      else
        raise GraphQL::ExecutionError.new("Invalid email or password")
      end
    end
  end
end
