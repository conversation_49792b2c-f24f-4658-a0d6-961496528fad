# frozen_string_literal: true

module Types
  class OrganisationType < Types::BaseObject
    field :id, ID, null: false
    field :organisation_name, String
    field :registration_number, String
    field :email_address, String
    field :web_address, String
    field :contact_number, String
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
    field :initials, String
    field :patients, Types::PatientType.connection_type, null: false
    field :companies, Types::CompanyType.connection_type, null: false
  end
end
