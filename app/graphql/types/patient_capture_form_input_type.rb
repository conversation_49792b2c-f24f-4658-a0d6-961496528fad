# frozen_string_literal: true

module Types
  class PatientCaptureFormInputType < Types::BaseInputObject
    argument :organisation_id, Integer, required: false

    argument :dob, GraphQL::Types::ISO8601Date, required: false
    argument :email, String, required: false
    argument :first_name, String, required: false
    argument :gender, String, required: false
    argument :identification_number, String, required: false
    argument :last_name, String, required: false
    argument :phone_number, String, required: false

    argument :employment_department, String, required: false
    argument :employment_position, String, required: false
    argument :employment_start_date, GraphQL::Types::ISO8601Date, required: false

    argument :company_capture, String, required: false
    argument :company_input, String, required: false
    argument :company_search, String, required: false
  end
end
