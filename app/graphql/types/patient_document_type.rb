# frozen_string_literal: true

module Types
  class PatientDocumentType < Types::BaseObject
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :description, String
    field :id, ID, null: false
    field :name, String
    field :patient_id, Integer, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
    field :uploaded_by, String
    field :attachments, [Types::UploadActiveStorageType]
  end
end
