# frozen_string_literal: true

module Types
  class PatientInputType < Types::BaseInputObject
    argument :dob, GraphQL::Types::ISO8601Date, required: false
    argument :email, String, required: false
    argument :first_name, String, required: false
    argument :gender, String, required: false
    argument :identification_number, String, required: false
    argument :last_name, String, required: false
    argument :organisation_id, Integer, required: false
    argument :phone_number, String, required: false
  end
end
