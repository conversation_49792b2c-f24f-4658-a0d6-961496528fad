# frozen_string_literal: true

module Types
  class PatientType < Types::BaseObject
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :dob, GraphQL::Types::ISO8601Date
    field :email, String
    field :employers, [Types::CompanyType], null: true
    field :employments, [Types::EmploymentType], null: true
    field :first_name, String
    field :full_name, String
    field :gender, String
    field :id, ID, null: false
    field :identification_number, String
    field :last_name, String
    field :organisation_id, Integer, null: false
    field :phone_number, String
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
    field :medicals, [Types::EvaluationType], null: true
  end
end
