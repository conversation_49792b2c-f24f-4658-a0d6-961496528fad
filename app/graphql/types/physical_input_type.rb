# frozen_string_literal: true

module Types
  class PhysicalInputType < Types::BaseInputObject
    argument :result, String, required: false
    argument :status, String, required: false
    argument :performed_by, String, required: false
    argument :height, String, required: false
    argument :weight, String, required: false
    argument :blood_pressure, String, required: false
    argument :pulse, String, required: false
    argument :blood_sugar, String, required: false
    argument :date_performed, GraphQL::Types::ISO8601Date, required: false
    argument :note, String, required: false
    argument :clinic_id, Integer, required: false
    argument :patient_id, Integer, required: false
    argument :urine_test, Boolean, required: false
    argument :nad_test, Boolean, required: false
    argument :leucocytes_test, Boolean, required: false
    argument :nitrite_test, Boolean, required: false
    argument :blood_test, Boolean, required: false
    argument :protein_test, Boolean, required: false
    argument :glucose_test, Boolean, required: false
    argument :general_appearance, String, required: false
    argument :peripheral_exam, Boolean, required: false
    argument :skin_exam, Boolean, required: false
    argument :ent_exam, Boolean, required: false
    argument :cvs_exam, Boolean, required: false
    argument :chest_exam, Boolean, required: false
    argument :gastro_exam, Boolean, required: false
    argument :urinary_exam, Boolean, required: false
    argument :musculo_exam, Boolean, required: false
    argument :cns_exam, Boolean, required: false
    argument :endocrine_exam, Boolean, required: false
    argument :glucose_exam, Boolean, required: false
    argument :hypertension_chronic, Boolean, required: false
    argument :asthma_chronic, Boolean, required: false
    argument :epilepsy_chronic, Boolean, required: false
    argument :mental_chronic, Boolean, required: false
    argument :obesity_chronic, Boolean, required: false
    argument :diabetes_chronic, Boolean, required: false
    argument :drug_chronic, Boolean, required: false
    argument :thyroid_chronic, Boolean, required: false
    argument :copd_chronic, Boolean, required: false
    argument :cardiac_chronic, Boolean, required: false
    argument :prosthesis_chronic, Boolean, required: false
    argument :arthrithis_chronic, Boolean, required: false
  end
end
