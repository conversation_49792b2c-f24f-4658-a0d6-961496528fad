# frozen_string_literal: true

module Types
  class PhysicalType < Types::BaseObject
    field :id, ID, null: false
    field :result, String
    field :performed_by, String
    field :height, String
    field :weight, String
    field :blood_pressure, String
    field :pulse, String
    field :blood_sugar, String
    field :date_performed, GraphQL::Types::ISO8601Date
    field :note, String
    field :clinic_id, Integer, null: false
    field :patient_id, Integer, null: false
    field :urine_test, Boolean
    field :nad_test, Boolean
    field :leucocytes_test, Boolean
    field :nitrite_test, Boolean
    field :blood_test, Boolean
    field :protein_test, Boolean
    field :glucose_test, Boolean
    field :general_appearance, String
    field :peripheral_exam, Boolean
    field :skin_exam, Boolean
    field :ent_exam, Boolean
    field :cvs_exam, Boolean
    field :chest_exam, Boolean
    field :gastro_exam, Boolean
    field :urinary_exam, Boolean
    field :musculo_exam, Boolean
    field :cns_exam, Boolean
    field :endocrine_exam, Boolean
    field :glucose_exam, Boolean
    field :hypertension_chronic, Boolean
    field :asthma_chronic, Boolean
    field :epilepsy_chronic, Boolean
    field :mental_chronic, Boolean
    field :obesity_chronic, Boolean
    field :diabetes_chronic, Boolean
    field :drug_chronic, Boolean
    field :thyroid_chronic, Boolean
    field :copd_chronic, Boolean
    field :cardiac_chronic, Boolean
    field :prosthesis_chronic, Boolean
    field :arthrithis_chronic, Boolean
    field :assessment_reports, [Types::AssessmentReportType]
    field :clinic, Types::ClinicType
    field :patient, Types::PatientType
    field :status, String
  end
end
