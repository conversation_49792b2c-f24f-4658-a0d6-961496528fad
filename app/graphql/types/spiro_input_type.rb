# frozen_string_literal: true

module Types
  class SpiroInputType < Types::BaseInputObject
    argument :id, ID, required: false
    argument :result, String, required: false
    argument :performed_by, String, required: false
    argument :patient_id, Integer, required: false
    argument :clinic_id, Integer, required: false
    argument :date_performed, GraphQL::Types::ISO8601Date, required: false
    argument :name, String, required: false
    argument :note, String, required: false
    argument :status, String, required: false
    argument :system_used, String, required: false
    argument :attachments, [ApolloUploadServer::Upload], required: false
  end
end
