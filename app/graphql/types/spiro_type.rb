# frozen_string_literal: true

module Types
  class SpiroType < Types::BaseObject
    field :id, ID, null: false
    field :result, String
    field :performed_by, String
    field :patient_id, Integer, null: false
    field :clinic_id, Integer, null: false
    field :date_performed, GraphQL::Types::ISO8601Date
    field :name, String
    field :note, String
    field :status, String
    field :system_used, String
    field :attachments, [Types::UploadActiveStorageType]
    field :clinic, Types::ClinicType
    field :patient, Types::PatientType
  end
end
