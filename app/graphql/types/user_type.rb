# frozen_string_literal: true

module Types
  class UserType < Types::BaseObject
    field :id, ID, null: false
    field :first_name, String
    field :last_name, String
    field :user_mobile, String
    field :token, String
    # field :is_active, Boolean
    field :email, String, null: false
    # field :encrypted_password, String, null: false
    # field :reset_password_token, String
    # field :reset_password_sent_at, GraphQL::Types::ISO8601DateTime
    # field :remember_created_at, GraphQL::Types::ISO8601DateTime
    # field :sign_in_count, Integer, null: false
    # field :current_sign_in_at, GraphQL::Types::ISO8601DateTime
    # field :last_sign_in_at, GraphQL::Types::ISO8601DateTime
    # field :current_sign_in_ip, Types::InetType
    # field :last_sign_in_ip, Types::InetType
    # field :confirmation_token, String
    # field :confirmed_at, GraphQL::Types::ISO8601DateTime
    # field :confirmation_sent_at, GraphQL::Types::ISO8601DateTime
    # field :unconfirmed_email, String
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
    # field :invitation_token, String
    # field :invitation_created_at, GraphQL::Types::ISO8601DateTime
    # field :invitation_sent_at, GraphQL::Types::ISO8601DateTime
    # field :invitation_accepted_at, GraphQL::Types::ISO8601DateTime
    # field :invitation_limit, Integer
    # field :invited_by_type, String
    # field :invited_by_id, Integer
    # field :invitations_count, Integer
    field :default_organisation_id, Integer
  end
end
