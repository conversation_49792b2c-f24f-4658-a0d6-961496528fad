# frozen_string_literal: true

module Types
  class VisionScreeningInputType < Types::BaseInputObject
    argument :id, ID, required: false
    argument :name, String, required: false
    argument :snellen_right_eye, String, required: false
    argument :snellen_left_eye, String, required: false
    argument :temporal_right, String, required: false
    argument :temporal_left, String, required: false
    argument :total_right, String, required: false
    argument :total_left, String, required: false
    argument :color_discrimination, String, required: true
    argument :comment, String, required: false
    argument :date_of_screening, GraphQL::Types::ISO8601Date, required: true
    argument :performed_by, String, required: false
    argument :patient_id, Integer, required: true
    argument :status, String, required: false
    argument :clinic_id, Integer, required: true
    argument :snellen_right_eye_without_glasses, String, required: false
    argument :snellen_left_eye_without_glasses, String, required: false
  end
end
