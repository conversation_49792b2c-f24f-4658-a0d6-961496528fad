module Types
  class VisionScreeningType < Types::BaseObject
    field :id, ID, null: false
    field :name, String
    field :snellen_right_eye, String
    field :snellen_left_eye, String
    field :temporal_right, String
    field :temporal_left, String
    field :total_right, String
    field :total_left, String
    field :color_discrimination, String
    field :comment, String
    field :date_of_screening, GraphQL::Types::ISO8601Date
    field :performed_by, String
    field :patient_id, Integer, null: false
    field :created_at, GraphQL::Types::ISO8601DateTime, null: false
    field :updated_at, GraphQL::Types::ISO8601DateTime, null: false
    field :status, String
    field :signed_by, String
    field :date_signed, GraphQL::Types::ISO8601Date
    field :clinic_id, Integer
    field :snellen_right_eye_without_glasses, String
    field :snellen_left_eye_without_glasses, String
    field :report_name, String
    field :clinic, Types::ClinicType
    field :patient, Types::PatientType
    field :assessment_reports, [Types::AssessmentReportType]
  end
end
