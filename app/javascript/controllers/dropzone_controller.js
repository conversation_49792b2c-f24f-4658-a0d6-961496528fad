// import cryptoRandomString from 'crypto-random-string'
import Dropzone from 'dropzone'
import { Controller } from '@hotwired/stimulus'
import { insertAfter, removeElement } from '../helpers'
import { nanoid } from 'nanoid'

export default class extends Controller {
  static targets = ['input', 'output']
  static values = { field: String, scope: String }

  connect () {
    Dropzone.autoDiscover = false // necessary quirk for Dropzone error in console
    this.hideFileInput()
    this.dropZone = this.createDropZone(this)
    this.bindEvents()
  }

  createDropZone (controller) {
    return new Dropzone(controller.element, {
      url: '/',
      addRemoveLinks: true,
      autoProcessQueue: false,
      maxFiles: 10,
      uploadMultiple: false,
      acceptedFiles: 'image/*,application/pdf',
      method: 'POST',
      headers: {
        'Cache-Control': null,
        'X-Requested-With': null
      }
    })
  }

  hideFileInput () {
    this.inputTarget.disabled = true
    this.inputTarget.style.display = 'none'
  }

  getPreSignInfo (file) {
    let url = `/s3/params?filename=${file.name}&type=${file.type}`
    let self = this

    fetch(url, {
      method: 'get',
      headers: {
        accept: 'application/json',
        'content-type': 'application/json'
      }
    })
      .then(response => {
        return response.json()
      })
      .then(result => {
        self.dropZone.options.url = result['url']
        file.additionalData = result['fields']
        self.dropZone.processFile(file)
      })
  }

  bindEvents () {
    let self = this

    this.dropZone.on('sending', function (file, _, formData) {
      for (var field in file.additionalData) {
        formData.append(field, file.additionalData[field])
      }
    })

    this.dropZone.on('addedfile', file => {
      self.getPreSignInfo(file)
    })

    this.dropZone.on('removedfile', file => {
      const html_id = file.additionalData['key'].match(
        /^cache\/(.+)(.*)\.[^.]+$/
      )[1]
      const uploadedFile = document.querySelector(`[id="${html_id}"]`)
      removeElement(uploadedFile)
    })

    this.dropZone.on('success', function (file) {
      const shrine_id = file.additionalData['key'].match(/^cache\/(.+)/)[1]
      const html_id = file.additionalData['key'].match(
        /^cache\/(.+)(.*)\.[^.]+$/
      )[1]

      let hiddenInput = self.createHiddenInput(
        self.scopeValue,
        html_id,
        self.fieldValue
      )

      hiddenInput.value = self.uploadedFileData(file, shrine_id)
      insertAfter(hiddenInput, self.inputTarget.parentNode)
    })

    this.dropZone.on('error', (file, err, xhr) => {
      console.log('error:', file)
      console.log('error:', err)
      console.log('error:', xhr)
    })
  }

  createHiddenInput (scope, id, field) {
    const hiddenField = document.createElement('input')

    hiddenField.id = id
    hiddenField.type = 'hidden'
    hiddenField.name = `${scope}[${nanoid()}][${field}]`
    // hiddenField.name = `${scope}['123123123'][${field}]`
    return hiddenField
  }

  uploadedFileData (file, id) {
    let fileData = {
      id: id,
      storage: 'cache',
      metadata: {
        size: file.size,
        filename: file.name,
        mime_type: file.type
      }
    }

    return JSON.stringify(fileData)
  }
}
