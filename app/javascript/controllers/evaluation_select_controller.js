import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['output']

  connect () {}

  addToOutput (e) {
    e.preventDefault()
    // e.srcElement.value = ""
    console.log('event from select', e)
  }

  removeOutput () {}
}

const html = `
        <div class="relative mt-6 bg-gray-100 sm:rounded-lg">

          <div class="hover:text-gray-900 cursor-pointer absolute top-4 right-4 text-sm leading-6 font-medium text-gray-600">
            <svg class="fill-current w-6 h-6" fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" stroke="currentColor"><path d="M6 18L18 6M6 6l12 12"></path></svg>
          </div>

          <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
            </h3>
            <div class="mt-2 max-w-xl text-sm leading-5 text-gray-500">
              <p>
              </p>
            </div>
          </div>
        </div>
    `
