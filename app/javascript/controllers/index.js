// This file is auto-generated by ./bin/rails stimulus:manifest:update
// Run that command whenever you add a new controller or create them with
// ./bin/rails generate stimulus controllerName

import { application } from './application'

import ApplicationController from './application_controller'
application.register('application', ApplicationController)

import ClinicSearchController from './clinic_search_controller'
application.register('clinic-search', ClinicSearchController)

import CompanySearchController from './company_search_controller'
application.register('company-search', CompanySearchController)

import DropzoneController from './dropzone_controller'
application.register('dropzone', DropzoneController)

import EvaluationSearchController from './evaluation_search_controller'
application.register('evaluation-search', EvaluationSearchController)

import EvaluationSelectController from './evaluation_select_controller'
application.register('evaluation-select', EvaluationSelectController)

import ExampleController from './example_controller'
application.register('example', ExampleController)

import FlatpickrController from './flatpickr_controller'
application.register('flatpickr', FlatpickrController)

import <PERSON><PERSON><PERSON><PERSON><PERSON> from './hello_controller'
application.register('hello', HelloController)

import <PERSON>ient<PERSON>earch<PERSON>ontroller from './patient_search_controller'
application.register('patient-search', PatientSearchController)

import QrcodeController from './qrcode_controller'
application.register('qrcode', QrcodeController)

import RemoveController from './remove_controller'
application.register('remove', RemoveController)

import SignatureController from './signature_controller'
application.register('signature', SignatureController)

import ToggleController from './toggle_controller'
application.register('toggle', ToggleController)

import ToggleIfController from './toggle_if_controller'
application.register('toggle-if', ToggleIfController)
