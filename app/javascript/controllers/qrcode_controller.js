import { Controller } from '@hotwired/stimulus'
import QrCode<PERSON>ithLogo from 'qrcode-with-logos'
import logo from '../images/icons8-heart-with-pulse-ios-100.png'

export default class extends Controller {
  static targets = ['output']

  connect () {
    console.log("connected")
    this.generateQRCode()
  }

  generateQRCode () {
    let qrcode = new QrCodeWithLogo({
      content: this.outputTarget.textContent,
      width: 200,
      canvas: this.outputTarget,
      logo: {
        src: logo
      }
    })

    qrcode.toCanvas()
  }
}
