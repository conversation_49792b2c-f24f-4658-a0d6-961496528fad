import { Controller } from "@hotwired/stimulus";
import SignaturePad from "signature_pad";

let signature_pad;
let signature_data_uri;

export default class extends Controller {
  static targets = ["canvas", "output", "previewContainer", "previewImage"];

  connect() {
    signature_pad = new SignaturePad(this.canvasTarget, {
      backgroundColor: "rgba(255, 255, 255, 0)",
      penColor: "rgb(0, 0, 0)",
    });
  }

  clear(e) {
    e.preventDefault();
    signature_pad.clear();
  }

  save(e) {
    e.preventDefault();

    signature_data_uri = signature_pad.toDataURL();

    this.update_saved_signature(signature_data_uri);
    this.update_previewed_signature(signature_data_uri);
  }

  update_saved_signature(data) {
    this.outputTarget.value = data;
  }

  update_previewed_signature(data) {
    this.previewImageTarget.setAttribute("src", data);
  }
}
