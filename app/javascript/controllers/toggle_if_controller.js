import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['input', 'toggleable', 'option']
  static values = { input: String }

  connect () {
    this.inputValue = this.inputTarget.value
  }

  update (event) {
    this.toggleableTargets.forEach((el, _) => {
      // el.classList.toggle("bg-indigo-50", event.target == el )
      // el.classList.toggle("border-indigo-200", event.target == el )
      // el.classList.toggle("z-10", event.target == el )
    })

    this.inputTarget.value = event.target.dataset.option

    this.optionTargets.forEach((el, _) => {
      el.classList.toggle(
        'hidden',
        event.target.dataset.option != el.dataset.option
      )
    })
  }
}
