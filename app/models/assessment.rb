# == Schema Information
#
# Table name: assessments
#
#  id             :bigint           not null, primary key
#  actable_type   :string
#  date_performed :date
#  description    :string
#  name           :string
#  performed_by   :string
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  actable_id     :integer
#  patient_id     :bigint           not null
#
# Indexes
#
#  index_assessments_on_patient_id  (patient_id)
#
# Foreign Keys
#
#  fk_rails_...  (patient_id => patients.id)
#
class Assessment < ApplicationRecord
  actable

  belongs_to :patient

  validates :performed_by, :date_performed, presence: true
end
