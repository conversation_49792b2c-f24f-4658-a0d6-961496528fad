# == Schema Information
#
# Table name: assessment_reports
#
#  id              :bigint           not null, primary key
#  assessable_type :string           not null
#  date_generated  :date
#  generated_by    :string
#  report_data     :text
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  assessable_id   :bigint           not null
#
# Indexes
#
#  index_assessment_reports_on_assessable_type_and_assessable_id  (assessable_type,assessable_id)
#
class AssessmentReport < ApplicationRecord
  has_one_attached :report, dependent: :destroy

  belongs_to :assessable, polymorphic: true

  validates :date_generated, :generated_by, :report, presence: true
end
