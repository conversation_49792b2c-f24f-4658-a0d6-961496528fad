# == Schema Information
#
# Table name: attachments
#
#  id              :bigint           not null, primary key
#  attachable_type :string           not null
#  content_data    :text
#  description     :text
#  name            :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  attachable_id   :bigint           not null
#
# Indexes
#
#  index_attachments_on_attachable_type_and_attachable_id  (attachable_type,attachable_id)
#
class Attachment < ApplicationRecord
  attribute :remove, :boolean

  belongs_to :attachable, polymorphic: true

  after_update do |attachment|
    attachment.destroy if attachment.remove == true
  end
end
