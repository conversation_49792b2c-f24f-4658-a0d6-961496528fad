# == Schema Information
#
# Table name: clinics
#
#  id                 :bigint           not null, primary key
#  clinic_name        :string
#  details            :string
#  phone_number       :string
#  physical_address   :string
#  physical_address_2 :string
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  organisation_id    :bigint           not null
#
# Indexes
#
#  index_clinics_on_organisation_id  (organisation_id)
#
# Foreign Keys
#
#  fk_rails_...  (organisation_id => organisations.id)
#
class Clinic < ApplicationRecord
  belongs_to :organisation

  has_many :clinic_teams
  has_many :teams, through: :clinic_teams

  validates :physical_address, :clinic_name, presence: true

  include PgSearch::Model

  pg_search_scope :general_search,
    against: %i[clinic_name],
    using: {trigram: {
      threshold: 0.1
    }}

  def full_address
    "#{physical_address}, #{physical_address_2}"
  end
end
