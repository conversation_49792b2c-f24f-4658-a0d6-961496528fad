# == Schema Information
#
# Table name: clinic_teams
#
#  id         :bigint           not null, primary key
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  clinic_id  :bigint           not null
#  team_id    :bigint           not null
#
# Indexes
#
#  index_clinic_teams_on_clinic_id  (clinic_id)
#  index_clinic_teams_on_team_id    (team_id)
#
# Foreign Keys
#
#  fk_rails_...  (clinic_id => clinics.id)
#  fk_rails_...  (team_id => teams.id)
#
class ClinicTeam < ApplicationRecord
  belongs_to :clinic
  belongs_to :team
end
