# == Schema Information
#
# Table name: companies
#
#  id              :bigint           not null, primary key
#  about           :string
#  city            :string
#  email           :string
#  industry_sector :string
#  name            :string
#  phone_number    :string
#  street_address  :string
#  suburb          :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  organisation_id :bigint           default(1), not null
#
# Indexes
#
#  index_companies_on_organisation_id  (organisation_id)
#
# Foreign Keys
#
#  fk_rails_...  (organisation_id => organisations.id)
#
class Company < ApplicationRecord
  belongs_to :organisation

  has_many :employments
  has_many :employees, through: :employments, source: :patient

  validates :name, presence: true

  include PgSearch::Model

  pg_search_scope :general_search,
    against: %i[name city industry_sector],
    using: {trigram: {
      threshold: 0.1
    }}

  def address
    "#{street_address} #{city}"
  end
end
