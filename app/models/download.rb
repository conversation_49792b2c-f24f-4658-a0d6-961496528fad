# frozen_string_literal: true

class Download
  def initialize(report:, url:, filename:)
    @report = report
    @url = url
    @_filename = filename
  end

  def to_pdf
    Grover.new(url,
      format: "A4",
      wait_until: "networkidle0",
      username: "jack",
      password: "black",
      print_background: true).to_pdf
  end

  def filename
    "#{_filename}.pdf"
  end

  private

  attr_reader :report
  attr_reader :url
  attr_reader :_filename
end
