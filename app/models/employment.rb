# == Schema Information
#
# Table name: employments
#
#  id                 :bigint           not null, primary key
#  department         :string
#  employment_type    :string
#  induction_date     :date
#  position           :string
#  termination_date   :date
#  termination_reason :string
#  created_at         :datetime         not null
#  updated_at         :datetime         not null
#  company_id         :bigint           not null
#  patient_id         :bigint           not null
#
# Indexes
#
#  index_employments_on_company_id  (company_id)
#  index_employments_on_patient_id  (patient_id)
#
# Foreign Keys
#
#  fk_rails_...  (company_id => companies.id)
#  fk_rails_...  (patient_id => patients.id)
#
class Employment < ApplicationRecord
  belongs_to :patient
  belongs_to :company

  def active?
    termination_date.blank? && induction_date.present?
  end

  def company_name
    company.name
  end

  def patient_name
    patient.name.full
  end
end
