# == Schema Information
#
# Table name: evaluations
#
#  id                       :bigint           not null, primary key
#  audio_performed          :boolean          default(FALSE), not null
#  cannabis_performed       :boolean          default(FALSE), not null
#  ecg_performed            :boolean          default(FALSE), not null
#  exclusion                :boolean          default(FALSE), not null
#  exclusion_comment        :string
#  heat_performed           :boolean          default(FALSE), not null
#  height_performed         :boolean          default(FALSE), not null
#  medical_examination_date :date
#  medical_expiry_date      :date
#  medical_type             :string
#  name                     :string
#  outcome                  :text
#  outcome_comment          :text
#  physical_exam_performed  :boolean          default(FALSE), not null
#  referral                 :boolean
#  referral_comment         :string
#  spiro_performed          :boolean          default(FALSE), not null
#  status                   :string
#  visual_performed         :boolean          default(FALSE), not null
#  xray_performed           :boolean          default(FALSE), not null
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  audio_id                 :bigint
#  clinic_id                :bigint
#  employment_id            :bigint
#  lab_test_id              :bigint
#  patient_id               :bigint           not null
#
# Indexes
#
#  index_evaluations_on_audio_id       (audio_id)
#  index_evaluations_on_clinic_id      (clinic_id)
#  index_evaluations_on_employment_id  (employment_id)
#  index_evaluations_on_lab_test_id    (lab_test_id)
#  index_evaluations_on_patient_id     (patient_id)
#
# Foreign Keys
#
#  fk_rails_...  (clinic_id => clinics.id)
#  fk_rails_...  (employment_id => employments.id)
#  fk_rails_...  (lab_test_id => lab_tests.id)
#  fk_rails_...  (patient_id => patients.id)
#
class Evaluation < ApplicationRecord
  belongs_to :patient
  belongs_to :lab_test, optional: true
  belongs_to :employment
  belongs_to :clinic
  belongs_to :audio, optional: true

  has_many :assessment_reports, as: :assessable, dependent: :destroy
  has_one :evaluation_signoff, dependent: :destroy

  has_and_belongs_to_many :exclusions
  has_and_belongs_to_many :referrals

  # has_one :physical
  has_one :visual

  has_one :spiro
  has_one :xray
  has_one :ecg

  has_one :heat
  has_one :height

  include PgSearch::Model

  validates :name, uniqueness: true

  validates :name, :outcome, :medical_type,
    :employment, :medical_examination_date,
    :medical_expiry_date, presence: true

  pg_search_scope :general_search,
    against: %i[name],
    associated_against: {patient: [:first_name, :last_name, :identification_number]},
    using: {trigram: {
      threshold: 0.1
    }}

  def report_name
    " Evaluation - #{report_patient_name} - #{DateTime.now.strftime("%Y%m%d - %k:%M")}"
  end

  def report_patient_name
    patient.name.full
  end
end
