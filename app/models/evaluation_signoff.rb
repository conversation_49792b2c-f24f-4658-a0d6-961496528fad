# == Schema Information
#
# Table name: evaluation_signoffs
#
#  id            :bigint           not null, primary key
#  date_signed   :date
#  notes         :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  evaluation_id :bigint           not null
#  user_id       :bigint           not null
#
# Indexes
#
#  index_evaluation_signoffs_on_evaluation_id  (evaluation_id)
#  index_evaluation_signoffs_on_user_id        (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (evaluation_id => evaluations.id) ON DELETE => cascade
#  fk_rails_...  (user_id => users.id)
#
class EvaluationSignoff < ApplicationRecord
  belongs_to :user
  belongs_to :evaluation

  def signature
    user.signature.first
  end
end
