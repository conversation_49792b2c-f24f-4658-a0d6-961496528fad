# == Schema Information
#
# Table name: exclusions
#
#  id         :bigint           not null, primary key
#  category   :string
#  note       :text
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  patient_id :bigint           not null
#
# Indexes
#
#  index_exclusions_on_patient_id  (patient_id)
#
# Foreign Keys
#
#  fk_rails_...  (patient_id => patients.id)
#
class Exclusion < ApplicationRecord
  belongs_to :patient
  has_and_belongs_to_many :evaluations

  validates :note, presence: true

  def name
    "#{category} - #{updated_at.strftime("%Y%m%d - %k:%M")}"
  end
end
