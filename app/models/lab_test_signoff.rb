# frozen_string_literal: true

# == Schema Information
#
# Table name: lab_test_signoffs
#
#  id          :bigint           not null, primary key
#  date_signed :date
#  notes       :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  lab_test_id :bigint           not null
#  user_id     :bigint           not null
#
# Indexes
#
#  index_lab_test_signoffs_on_lab_test_id  (lab_test_id)
#  index_lab_test_signoffs_on_user_id      (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (lab_test_id => lab_tests.id)
#  fk_rails_...  (user_id => users.id)
#
class LabTestSignoff < ApplicationRecord
  belongs_to :user
  belongs_to :lab_test

  def signature
    user.signatures.first
  end
end
