# == Schema Information
#
# Table name: organisations
#
#  id                  :bigint           not null, primary key
#  contact_number      :string
#  email_address       :string
#  initials            :string
#  organisation_name   :string
#  registration_number :string
#  web_address         :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#
class Organisation < ApplicationRecord
  has_many :patients
  has_many :companies

  has_many :clinics, dependent: :destroy
  has_many :teams, dependent: :destroy

  has_many :organisation_users
  has_many :users, through: :organisation_users
end
