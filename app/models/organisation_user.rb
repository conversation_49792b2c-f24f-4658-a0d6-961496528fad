# == Schema Information
#
# Table name: organisation_users
#
#  id              :bigint           not null, primary key
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  organisation_id :bigint           not null
#  user_id         :bigint           not null
#
# Indexes
#
#  index_organisation_users_on_organisation_id  (organisation_id)
#  index_organisation_users_on_user_id          (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (organisation_id => organisations.id)
#  fk_rails_...  (user_id => users.id)
#
class OrganisationUser < ApplicationRecord
  belongs_to :user, inverse_of: :organisation_users
  belongs_to :organisation, inverse_of: :organisation_users
end
