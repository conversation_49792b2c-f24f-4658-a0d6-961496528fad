# frozen_string_literal: true

# == Schema Information
#
# Table name: patients
#
#  id                    :bigint           not null, primary key
#  dob                   :date
#  email                 :string
#  first_name            :string
#  gender                :string
#  identification_number :string
#  last_name             :string
#  phone_number          :string
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#  organisation_id       :bigint           default(1), not null
#
# Indexes
#
#  index_patients_on_organisation_id  (organisation_id)
#
# Foreign Keys
#
#  fk_rails_...  (organisation_id => organisations.id)
#
class Patient < ApplicationRecord
  belongs_to :organisation

  has_many :signatures, as: :signable
  has_many :evaluations, dependent: :destroy
  has_many :patient_notes, dependent: :destroy
  has_many :lab_tests, dependent: :destroy
  has_many :audios, dependent: :destroy
  has_many :spiros, dependent: :destroy
  has_many :visuals, dependent: :destroy
  has_many :physicals, dependent: :destroy
  has_many :exclusions, dependent: :destroy
  has_many :referrals, dependent: :destroy
  has_many :assessments, dependent: :destroy
  has_many :vision_screening_uploads, dependent: :destroy
  has_many :vision_screenings, dependent: :destroy
  has_many :patient_documents, dependent: :destroy

  has_many :employments
  has_many :employers, through: :employments, source: :company

  validates :first_name, :last_name, :gender, :dob, presence: true
  validates :identification_number, presence: true,
    uniqueness: {case_sensitive: false}

  has_person_name

  include PgSearch::Model

  pg_search_scope :general_search,
    against: %i[last_name first_name
      identification_number],
    associated_against: {employers: :name},
    using: {trigram: {
      threshold: 0.1
    }}

  def full_name
    name.full
  end

  def medicals
    evaluations
  end
end
