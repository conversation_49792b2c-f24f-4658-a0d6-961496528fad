# == Schema Information
#
# Table name: patient_documents
#
#  id          :bigint           not null, primary key
#  description :text
#  name        :string
#  uploaded_by :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  patient_id  :bigint           not null
#
# Indexes
#
#  index_patient_documents_on_patient_id  (patient_id)
#
# Foreign Keys
#
#  fk_rails_...  (patient_id => patients.id)
#
class PatientDocument < ApplicationRecord
  belongs_to :patient
  has_many_attached :attachments, dependent: :destroy

  validates :name, presence: true
end
