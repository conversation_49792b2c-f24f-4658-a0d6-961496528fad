# == Schema Information
#
# Table name: patient_notes
#
#  id             :bigint           not null, primary key
#  last_edited_by :string
#  note           :text
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  patient_id     :bigint           not null
#
# Indexes
#
#  index_patient_notes_on_patient_id  (patient_id)
#
# Foreign Keys
#
#  fk_rails_...  (patient_id => patients.id)
#
class PatientNote < ApplicationRecord
  belongs_to :patient
  has_many :attachments, as: :attachable

  accepts_nested_attributes_for :attachments

  def edit_link(note)
    Rails.application.routes.url_helpers.edit_patient_note_path(note.id)
  end

  def svg_icon
    svg = <<-FOO
      <svg class='text-indigo-600 w-6' fill="none" stroke-linecap="round" 
        stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" 
        stroke="currentColor">
        <path d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
        </path>
      </svg>
    FOO
    svg.html_safe
  end
end
