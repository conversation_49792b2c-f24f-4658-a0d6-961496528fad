# == Schema Information
#
# Table name: physicals
#
#  id                   :bigint           not null, primary key
#  arthrithis_chronic   :boolean
#  asthma_chronic       :boolean
#  blood_pressure       :string
#  blood_sugar          :string
#  blood_test           :boolean
#  cardiac_chronic      :boolean
#  chest_exam           :boolean
#  cns_exam             :boolean
#  copd_chronic         :boolean
#  cvs_exam             :boolean
#  date_performed       :date
#  diabetes_chronic     :boolean
#  drug_chronic         :boolean
#  endocrine_exam       :boolean
#  ent_exam             :boolean
#  epilepsy_chronic     :boolean
#  gastro_exam          :boolean
#  general_appearance   :string
#  glucose_exam         :boolean
#  glucose_test         :boolean
#  height               :string
#  hypertension_chronic :boolean
#  leucocytes_test      :boolean
#  mental_chronic       :boolean
#  musculo_exam         :boolean
#  nad_test             :boolean
#  nitrite_test         :boolean
#  note                 :text
#  obesity_chronic      :boolean
#  performed_by         :string
#  peripheral_exam      :boolean
#  prosthesis_chronic   :boolean
#  protein_test         :boolean
#  pulse                :string
#  result               :string
#  skin_exam            :boolean
#  status               :string
#  thyroid_chronic      :boolean
#  urinary_exam         :boolean
#  urine_test           :boolean
#  weight               :string
#  clinic_id            :bigint           not null
#  patient_id           :bigint           not null
#
# Indexes
#
#  index_physicals_on_clinic_id   (clinic_id)
#  index_physicals_on_patient_id  (patient_id)
#
# Foreign Keys
#
#  fk_rails_...  (clinic_id => clinics.id)
#  fk_rails_...  (patient_id => patients.id)
#
class Physical < ApplicationRecord
  belongs_to :patient
  belongs_to :clinic

  has_many :assessment_reports, as: :assessable, dependent: :destroy

  has_one :physical_signoff

  validates :clinic_id, :patient_id, presence: true
  validates :performed_by, :date_performed, presence: true

  def report_name
    " Physical Ass. Report - #{report_patient_name} - #{Date.today.strftime("%Y%m%d")}"
  end

  def report_patient_name
    patient.name.full
  end
end
