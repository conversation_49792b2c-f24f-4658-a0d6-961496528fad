# == Schema Information
#
# Table name: physical_signoffs
#
#  id          :bigint           not null, primary key
#  date_signed :date
#  notes       :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  physical_id :bigint           not null
#  user_id     :bigint           not null
#
# Indexes
#
#  index_physical_signoffs_on_physical_id  (physical_id)
#  index_physical_signoffs_on_user_id      (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (physical_id => physicals.id)
#  fk_rails_...  (user_id => users.id)
#
class PhysicalSignoff < ApplicationRecord
  belongs_to :physical
  belongs_to :user

  def signature
    user.signatures.first
  end
end
