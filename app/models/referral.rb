# == Schema Information
#
# Table name: referrals
#
#  id              :bigint           not null, primary key
#  address         :text
#  attention       :string
#  issues          :text
#  medical_centre  :string
#  note            :text
#  referral_date   :date
#  specialist_type :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  patient_id      :bigint           not null
#
# Indexes
#
#  index_referrals_on_patient_id  (patient_id)
#
# Foreign Keys
#
#  fk_rails_...  (patient_id => patients.id)
#
class Referral < ApplicationRecord
  belongs_to :patient
  has_and_belongs_to_many :evaluations

  validates :issues, :attention, :specialist_type, presence: true

  def name
    "#{specialist_type} - #{updated_at.strftime("%Y%m%d - %k:%M")}"
  end
end
