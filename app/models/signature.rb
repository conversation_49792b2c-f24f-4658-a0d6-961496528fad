# == Schema Information
#
# Table name: signatures
#
#  id            :bigint           not null, primary key
#  first_line    :string
#  forth_line    :string
#  image_data    :text
#  second_line   :string
#  signable_type :string           not null
#  third_line    :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  signable_id   :bigint           not null
#
# Indexes
#
#  index_signatures_on_signable_type_and_signable_id  (signable_type,signable_id)
#
class Signature < ApplicationRecord
  has_one_attached :image

  belongs_to :signable, polymorphic: true

  validates :first_line, :image, presence: true
end
