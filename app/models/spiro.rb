# == Schema Information
#
# Table name: spiros
#
#  id             :bigint           not null, primary key
#  date_performed :date
#  name           :string
#  note           :text
#  performed_by   :string
#  result         :string
#  status         :string
#  system_used    :string
#  clinic_id      :bigint           not null
#  patient_id     :bigint           not null
#
# Indexes
#
#  index_spiros_on_clinic_id   (clinic_id)
#  index_spiros_on_patient_id  (patient_id)
#
# Foreign Keys
#
#  fk_rails_...  (clinic_id => clinics.id)
#  fk_rails_...  (patient_id => patients.id)
#
class Spiro < ApplicationRecord
  belongs_to :patient
  belongs_to :clinic

  has_many_attached :attachments, dependent: :destroy

  validates :clinic_id, :patient_id, :name, presence: true
end
