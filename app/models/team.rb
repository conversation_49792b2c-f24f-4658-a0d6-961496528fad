# == Schema Information
#
# Table name: teams
#
#  id              :bigint           not null, primary key
#  team_name       :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  organisation_id :bigint           not null
#
# Indexes
#
#  index_teams_on_organisation_id  (organisation_id)
#
# Foreign Keys
#
#  fk_rails_...  (organisation_id => organisations.id)
#
class Team < ApplicationRecord
  belongs_to :organisation

  has_many :team_users
  has_many :users, through: :team_users

  has_many :clinic_teams
  has_many :clinics, through: :clinic_teams
end
