# == Schema Information
#
# Table name: vision_screenings
#
#  id                                :bigint           not null, primary key
#  color_discrimination              :string
#  comment                           :text
#  date_of_screening                 :date
#  date_signed                       :date
#  name                              :string
#  performed_by                      :string
#  signed_by                         :string
#  snellen_left_eye                  :string
#  snellen_left_eye_without_glasses  :string
#  snellen_right_eye                 :string
#  snellen_right_eye_without_glasses :string
#  status                            :string
#  temporal_left                     :string
#  temporal_right                    :string
#  total_left                        :string
#  total_right                       :string
#  created_at                        :datetime         not null
#  updated_at                        :datetime         not null
#  clinic_id                         :bigint
#  patient_id                        :bigint           not null
#
# Indexes
#
#  index_vision_screenings_on_clinic_id   (clinic_id)
#  index_vision_screenings_on_patient_id  (patient_id)
#
# Foreign Keys
#
#  fk_rails_...  (clinic_id => clinics.id)
#  fk_rails_...  (patient_id => patients.id)
#
class VisionScreening < ApplicationRecord
  belongs_to :patient
  belongs_to :clinic

  has_many :assessment_reports, as: :assessable, dependent: :destroy
  has_many :attachments, as: :attachable

  has_one :vision_screening_signoff

  validates :date_of_screening, presence: true

  def result
    date_of_screening
  end

  def report_name
    " Vision Screening Report - #{report_patient_name} - #{Date.today.strftime("%Y%m%d")}"
  end

  def report_patient_name
    patient.name.full
  end
end
