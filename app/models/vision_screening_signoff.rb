# == Schema Information
#
# Table name: vision_screening_signoffs
#
#  id                  :bigint           not null, primary key
#  date_signed         :date
#  notes               :string
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#  user_id             :bigint           not null
#  vision_screening_id :bigint           not null
#
# Indexes
#
#  index_vision_screening_signoffs_on_user_id              (user_id)
#  index_vision_screening_signoffs_on_vision_screening_id  (vision_screening_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#  fk_rails_...  (vision_screening_id => vision_screenings.id)
#
class VisionScreeningSignoff < ApplicationRecord
  belongs_to :user
  belongs_to :vision_screening

  def signature
    user.signatures.first
  end
end
