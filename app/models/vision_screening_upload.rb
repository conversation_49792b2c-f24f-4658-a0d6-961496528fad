# == Schema Information
#
# Table name: vision_screening_uploads
#
#  id             :bigint           not null, primary key
#  date_performed :date
#  name           :string
#  note           :text
#  performed_by   :string
#  result         :string
#  signature_date :date
#  signed_by      :string
#  status         :string
#  system_used    :string
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  clinic_id      :bigint           not null
#  patient_id     :bigint           not null
#
# Indexes
#
#  index_vision_screening_uploads_on_clinic_id   (clinic_id)
#  index_vision_screening_uploads_on_patient_id  (patient_id)
#
# Foreign Keys
#
#  fk_rails_...  (clinic_id => clinics.id)
#  fk_rails_...  (patient_id => patients.id)
#
class VisionScreeningUpload < ApplicationRecord
  belongs_to :clinic
  belongs_to :patient

  has_many :attachments, as: :attachable, dependent: :destroy
  accepts_nested_attributes_for :attachments

  attribute :signed, :boolean
end
