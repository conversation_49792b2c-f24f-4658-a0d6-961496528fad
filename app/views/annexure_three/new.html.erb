<% content_for :head do %>
  <header class="bg-gray-800 shadow-sm">
    <div class="max-w-7xl mx-auto py-4 sm:px-6 lg:px-8">
      <h1 class="leading-6 font-semibold text-white text-sm">
        Add a new Annexure 3 form
      </h1>
    </div>
  </header>
<% end %>

<%= form_with scope: :annex, url: new_patient_annexure_three_path do |f| %>

  <div class="pl-2 flex-1 flex flex-col">
    <main class="flex-1 overflow-y-auto focus:outline-none" tabindex="0">
      <div class="relative max-w-4xl mx-auto md:px-8 xl:px-0">
        <div class="mt-10 space-y-6 divide-y divide-gray-200">
          <div class="space-y-1">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              General
            </h3>
            <p class="max-w-2xl text-sm leading-5 text-gray-500">
            </p>
          </div>
          <div>
            <dl class="divide-y divide-gray-200">
              <div class="py-4 space-y-1 sm:py-5 items-center sm:grid sm:grid-cols-3 sm:gap-6">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Occupation
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-1">
                  <%= f.text_field :occupation, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                </dd>
              </div>
            </dl>
          </div>
        </div>

        <fieldset class="border-b pb-4">
          <legend class="text-base font-medium text-gray-900">
            Possible Exposures
          </legend>
          <div class="mt-4 space-y-4">
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <%= f.check_box :noise, class: "focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" %>
              </div>
              <div class="ml-3 text-sm">
                <%= f.label :noise, class: "font-medium text-gray-600" %>
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <%= f.check_box :dust, class: "focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" %>
              </div>
              <div class="ml-3 text-sm">
                <%= f.label :dust, class: "font-medium text-gray-600" %>
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <%= f.check_box :fall_risk, class: "focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" %>
              </div>
              <div class="ml-3 text-sm">
                <%= f.label :fall_risk, class: "font-medium text-gray-600" %>
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <%= f.check_box :adverse_weather_conditions, class: "focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" %>
              </div>
              <div class="ml-3 text-sm">
                <%= f.label :adverse_weather_conditions, class: "font-medium text-gray-600" %>
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <%= f.check_box :vibration, class: "focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" %>
              </div>
              <div class="ml-3 text-sm">
                <%= f.label :vibration, class: "font-medium text-gray-600" %>
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <%= f.check_box :electricution, class: "focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" %>
              </div>
              <div class="ml-3 text-sm">
                <%= f.label :electricution, class: "font-medium text-gray-600" %>
              </div>
            </div>

          </div>
        </fieldset>


        <fieldset class="mt-8 border-b pb-4">
          <legend class="text-base font-medium text-gray-900">
            Job Specific Requirements
          </legend>
          <div class="mt-4 space-y-4">
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <%= f.check_box :driving_truck, class: "focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" %>
              </div>
              <div class="ml-3 text-sm">
                <%= f.label :driving_truck, class: "font-medium text-gray-600" %>
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <%= f.check_box :operating_earthmoving_machine, class: "focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" %>
              </div>
              <div class="ml-3 text-sm">
                <%= f.label :operating_earthmoving_machine, class: "font-medium text-gray-600" %>
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <%= f.check_box :general_labour, class: "focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" %>
              </div>
              <div class="ml-3 text-sm">
                <%= f.label :general_labour, class: "font-medium text-gray-600" %>
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <%= f.check_box :health_and_safety, class: "focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" %>
              </div>
              <div class="ml-3 text-sm">
                <%= f.label :health_and_safety, class: "font-medium text-gray-600" %>
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <%= f.check_box :admin_and_supervision, class: "focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" %>
              </div>
              <div class="ml-3 text-sm">
                <%= f.label :admin_and_supervision, class: "font-medium text-gray-600" %>
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <%= f.check_box :repair_and_installation, class: "focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded" %>
              </div>
              <div class="ml-3 text-sm">
                <%= f.label :repair_and_installation, class: "font-medium text-gray-600" %>
              </div>
            </div>

          </div>
        </fieldset>



        <div class="mt-8 border-gray-200 pt-5">
          <div class="flex justify-end">
            <span class="inline-flex rounded-md shadow-sm">
              <%= link_to "Cancel", :back,
                class: "py-2 px-4 border border-gray-300 rounded-md text-sm leading-5 font-medium
                         text-gray-700 hover:text-gray-500 focus:outline-none focus:border-blue-300
                         focus:shadow-outline-blue active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out" %>
            </span>
            <span class="ml-3 inline-flex rounded-md shadow-sm">
              <%= f.submit  "Save", class:"inline-flex justify-center py-2 px-4 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-500 focus:outline-none focus:border-indigo-700 focus:shadow-outline-indigo active:bg-indigo-700 transition duration-150 ease-in-out" %>
            </span>
          </div>
        </div>
      </div>
    </main>
  </div>

<% end %>
