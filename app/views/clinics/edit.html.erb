<% content_for :head do %>
  <header class="bg-gray-800 shadow-sm">
    <div class="max-w-7xl mx-auto py-4 sm:px-6 lg:px-8">
      <h1 class="leading-6 font-semibold text-white text-sm">
        Edit clinic details
      </h1>
    </div>
  </header>
<% end %>

<%= form_with model: @clinic, url: clinic_path, method: "patch" do |f| %>
  <div class="">
    <div class="mt-6 sm:mt-5">

      <div class="sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:pt-5">
        <%= f.label :clinic_name, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-sm rounded-md shadow-sm">
            <%= f.text_field :clinic_name, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" %>
          </div>
        </div>
      </div>

      <div class="mt-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <%= f.label :physical_address, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-xs rounded-md shadow-sm">
            <%= f.text_field :physical_address, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" %>
          </div>
        </div>
      </div>

      <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <%= f.label :physical_address_2, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-xs rounded-md shadow-sm">
            <%= f.text_field :physical_address_2, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" %>
          </div>
        </div>
      </div>

      <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <%= f.label :phone_number, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-xs rounded-md shadow-sm">
            <%= f.text_field :phone_number, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" %>
          </div>
        </div>
      </div>
      <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <%= f.label :details, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-xs rounded-md shadow-sm">
            <%= f.text_field :details, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" %>
          </div>
        </div>
      </div>

    </div>
  </div>

  <div class="mt-8 border-t border-gray-200 pt-5">    <div class="flex justify-end">
      <span class="inline-flex rounded-md shadow-sm">
        <%= link_to "Cancel", :back,
          class: "py-2 px-4 border border-gray-300 rounded-md text-sm leading-5 font-medium
                   text-gray-700 hover:text-gray-500 focus:outline-none focus:border-blue-300
                   focus:shadow-outline-blue active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out" %>
      </span>
      <span class="ml-3 inline-flex rounded-md shadow-sm">
        <%= f.submit  "Save", class:"inline-flex justify-center py-2 px-4 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-500 focus:outline-none focus:border-indigo-700 focus:shadow-outline-indigo active:bg-indigo-700 transition duration-150 ease-in-out" %>
      </span>
    </div>
  </div>
<% end %>
