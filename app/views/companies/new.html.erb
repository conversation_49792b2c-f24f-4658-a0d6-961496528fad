<% content_for :head do %>
  <header class="bg-gray-800 shadow-sm">
    <div class="max-w-7xl mx-auto py-4 sm:px-6 lg:px-8">
      <h1 class="leading-6 font-semibold text-white text-sm">
        Create a new company
      </h1>
    </div>
  </header>
<% end %>

<%= form_with model: @company, scope: :company_form, url: organisation_companies_path, local: true do |f| %>

    <% if @company.errors.any? %>
      <div id="error_explanation " class='bg-red-50 p-8 text-red-900'>
        <h2 class="font-semibold"><%= pluralize(@company.errors.count, "error") %> prohibited this post from being saved:</h2>

        <ul>
          <% @company.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

  <div>
    <div class="mt-6 sm:mt-5">
      <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:pt-5">
        <%= f.label "Company Name", class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-xs rounded-md shadow-sm">
            <%= f.text_field :name, class: "max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
          </div>
          <div class="text-sm text-red-800">
          </div>
        </div>
      </div>

      <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <%= f.label :telephone, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-lg rounded-md shadow-sm">
            <%= f.text_field :phone_number, class: "max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
      </div>

      <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <%= f.label :email, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-lg rounded-md shadow-sm">
            <%= f.email_field :email, class: "max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
      </div>

      <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <%= f.label "Industry Sector", class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-lg rounded-md shadow-sm">
            <%= f.text_field :industry_sector, class: "max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
      </div>

      <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <%= f.label "About", class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-lg flex rounded-md shadow-sm">
            <%= f.text_area :about, rows: 3, class:"max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
          </div>
          <p class="mt-2 text-sm text-gray-500">Write a few sentences about yourself.</p>
        </div>
      </div>

      <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <%= f.label :street_address, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-lg rounded-md shadow-sm">
            <%= f.text_field :street_address, class: "max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
      </div>


      <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <%= f.label :suburb, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-lg rounded-md shadow-sm">
            <%= f.text_field :suburb, class: "max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
      </div>

      <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <%= f.label :city, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <%= f.text_field :city, class: "max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
        </div>
      </div>
    </div>
  </div>

  <div class="mt-8 border-t border-gray-200 pt-5">
    <div class="flex justify-end">
      <span class="inline-flex rounded-md shadow-sm">
        <%= link_to "Cancel", :back,
          class: "py-2 px-4 border border-gray-300 rounded-md text-sm leading-5 font-medium
                   text-gray-700 hover:text-gray-500 focus:outline-none focus:border-blue-300
                   focus:shadow-outline-blue active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out" %>
      </span>
      <span class="ml-3 inline-flex rounded-md shadow-sm">
        <%= f.submit  "Save", class:"inline-flex justify-center py-2 px-4 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-500 focus:outline-none focus:border-indigo-700 focus:shadow-outline-indigo active:bg-indigo-700 transition duration-150 ease-in-out" %>
      </span>
    </div>
  </div>
<% end %>
