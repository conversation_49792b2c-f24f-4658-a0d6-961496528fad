<%# <h2>Edit <%= resource_name.to_s.humanize %1></h2> %>

<%# <%= simple_form_for(resource, as: resource_name, url: registration_path(resource_name), html: { method: :put }) do |f| %1> %>
<%#   <%= f.error_notification %1> %>

<%#   <div class="form-inputs"> %>
  <%#     <%= f.input :email, required: true, autofocus: true %1> %>

  <%#     <% if devise_mapping.confirmable? && resource.pending_reconfirmation? %1> %>
    <%#       <p>Currently waiting confirmation for: <%= resource.unconfirmed_email %1></p> %>
    <%#     <% end %1> %>

  <%#     <%= f.input :password, %>
  <%#       hint: "leave it blank if you don't want to change it", %>
  <%#       required: false, %>
  <%#       input_html: { autocomplete: "new-password" } %1> %>
  <%#     <%= f.input :password_confirmation, %>
  <%#       required: false, %>
  <%#       input_html: { autocomplete: "new-password" } %1> %>
  <%#     <%= f.input :current_password, %>
  <%#       hint: "we need your current password to confirm your changes", %>
  <%#       required: true, %>
  <%#       input_html: { autocomplete: "current-password" } %1> %>
  <%#   </div> %>

<%#   <div class="form-actions"> %>
  <%#     <%= f.button :submit, "Update" %1> %>
  <%#   </div> %>
<%# <% end %1> %>

<%# <h3>Cancel my account</h3> %>

<%# <%= link_to "Back", :back %1> %>

<nav class="md:w-1/4 md:pr-8">
  <%= render "shared/settings_nav" %>
</nav>

<div class="mt-4 md:mt-0 md:w-3/4">
  <div class="mb-4 bg-white shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
 <%= form_for(resource, as: resource_name, url: registration_path(resource_name)) do |f| %>
<%= devise_error_messages! %>
        <div>
          <div>
            <div class="border-b border-gray-200 pb-5 mb-6 sm:mb-5">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                Profile
              </h3>
              <p class="mt-1 max-w-2xl text-sm leading-5 text-gray-500">
                Update your first and last name, email, and other personal information.
              </p>
            </div>
            <div class="mt-6 grid grid-cols-1 row-gap-6 col-gap-4 sm:grid-cols-6">
              <div class="sm:col-span-4">
                <label for="first_name" class="block text-sm font-medium leading-5 text-gray-700">
                  First Name
                </label>
                <div class="mt-1 rounded-md shadow-sm">
                  <%= f.text_field :first_name, required: true, autofocus: true, class:"form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" %>
                </div>
              </div>

              <div class="sm:col-span-4">
                <label for="last_name" class="block text-sm font-medium leading-5 text-gray-700">
                 Last Name
                </label>
                <div class="mt-1 rounded-md shadow-sm">
                  <%= f.text_field :last_name, required: true, autofocus: true, class:"form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" %>
                </div>
              </div>

              <div class="sm:col-span-4">
                <label class="block text-sm font-medium leading-5 text-gray-700" for="user_email">Email</label>
                <div class="mt-1 rounded-md shadow-sm">
                  <input autocomplete="email" class="form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" type="email" value="<EMAIL>" name="user[email]" id="user_email" />
                </div>
              </div>
            </div>
          </div>
          <p class="text-gray-400 mt-5">Unhappy? <%= link_to "Cancel my account", registration_path(resource_name), data: {confirm: "Are you sure?"}, method: :delete %></p>
        </div>
        <div class="mt-8 border-t border-gray-200 pt-5">
          <div class="flex justify-end ">
            <span class="inline-flex rounded-md shadow-sm">
              <input type="submit" name="commit" value="Save" class="inline-flex justify-center py-2 px-4 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-500 focus:outline-none focus:border-indigo-700 focus:shadow-outline-indigo active:bg-indigo-700 transition duration-150 ease-in-out" data-disable-with="Save" />
            </span>
            <span class="ml-3 inline-flex rounded-md shadow-sm">
              <a class="py-2 px-4 border border-gray-300 rounded-md text-sm leading-5 font-medium text-gray-700 hover:text-gray-500 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out" href="/">Cancel</a>
            </span>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
