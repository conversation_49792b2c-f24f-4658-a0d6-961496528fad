<%= notice %>
<%= alert %>

<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <img class="mx-auto h-12 w-auto" src="https://tailwindui.com/img/logos/workflow-mark-on-white.svg" alt="Workflow" />
    <h2 class="mt-6 text-center text-3xl leading-9 font-extrabold text-gray-900">
      Sign up for an account
    </h2>
    <p class="mt-2 text-center text-sm leading-5 text-gray-600 max-w">
      Or
      <%= link_to "log into your account", new_user_session_path,
      class: "font-medium text-indigo-600 hover:text-indigo-500
              focus:outline-none focus:underline transition ease-in-out duration-150" %>
    </p>
  </div>
  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">

      <%= form_for :user, url: registration_path(:user) do |f| %>
        <div class="form-inputs">
          <div class="">
            <%= f.label :first_name, class: "block text-md font-medium leading-5 text-gray-700" do %>
              First Name
            <% end %>

            <div class="mt-1 rounded-md shadow-sm">
              <%= f.text_field :first_name,
                required: true,
                autofocus: true,
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 transition duration-150 ease-in-out sm:text-sm sm:leading-5",
                input_html: {autocomplete: "first_name"} %>
            </div>

          </div>
          <div class="mt-4">
            <%= f.label :last_name, class: "block text-md font-medium leading-5 text-gray-700" do %>
              Last Name
            <% end %>

            <div class="mt-1 rounded-md shadow-sm">
              <%= f.text_field :last_name,
                required: true,
                autofocus: true,
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 transition duration-150 ease-in-out sm:text-sm sm:leading-5",
                input_html: {autocomplete: "last_name"} %>

            </div>

          </div>

          <div class="mt-12">
            <%= f.label :organisation_name, class: "block text-md font-medium leading-5 text-gray-700" do %>
              Organisation Name
            <% end %>

            <div class="mt-1 rounded-md shadow-sm">
              <%= f.text_field :organisation_name,
                required: true,
                autofocus: true,
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 transition duration-150 ease-in-out sm:text-sm sm:leading-5",
                input_html: {autocomplete: "organisation_name"} %>

            </div>

            <div class="mt-12">
              <%= f.label :email, class: "block text-md font-medium leading-5 text-gray-700" do %>
                Email address
              <% end %>

              <div class="mt-1 rounded-md shadow-sm">
                <%= f.text_field :email,
                  required: true,
                  autofocus: true,
                  class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 transition duration-150 ease-in-out sm:text-sm sm:leading-5",
                  input_html: {autocomplete: "email"} %>

              </div>

            </div>

            <div class="mt-4">
              <label class="block text-md font-medium leading-5 text-gray-700" for="user_password">Password</label>
              <div class="mt-1 rounded-md shadow-sm">
                <%= f.password_field :password,
                  required: true,
                  class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400
                        focus:outline-none focus:shadow-outline-blue focus:border-blue-300 transition duration-150
                        ease-in-out sm:text-sm sm:leading-5",
                        input_html: {autocomplete: "new-password"} %>

              </div>
            </div>

            <div class="mt-4">
              <label class="block text-md font-medium leading-5 text-gray-700" for="user_password_confirmation">Confirmation Password</label>
              <div class="mt-1 rounded-md shadow-sm">

              </div>

              <%= f.password_field :password_confirmation,
                required: true,
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400
                        focus:outline-none focus:shadow-outline-blue focus:border-blue-300 transition duration-150
                        ease-in-out sm:text-sm sm:leading-5",
                        input_html: {autocomplete: "new-password"} %>
            </div>
          </div>

          <div class="mt-4">
            <div class="text-sm text-font-thin text-gray-500 mb-2">
              By registering, you agree to the privacy policy and terms of service.
            </div>
            <span class="block w-full rounded-md shadow-sm">
              <%= f.submit "Sign Up", class: "w-full flex justify-center py-2 px-4 border
              border-transparent text-sm font-medium rounded-md
                                            text-white bg-indigo-600 hover:bg-indigo-500
                                            focus:outline-none focus:border-indigo-700
                                            focus:shadow-outline-indigo active:bg-indigo-700
                                            transition duration-150 ease-in-out" %>
            </span>
          </div>
        <% end %>
        </div>
    </div>
  </div>
</div>
