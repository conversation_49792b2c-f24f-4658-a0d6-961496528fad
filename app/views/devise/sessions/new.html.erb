<% if alert.present? %>
  <div class="rounded-md bg-red-50 p-4">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
        </svg>
      </div>
      <div class="ml-3">
        <h3 class="text-sm leading-5 font-medium text-red-800">
          Error detected
        </h3>
        <div class="mt-2 text-sm leading-5 text-red-700">
          <p>
            <%= alert %>
          </p>
        </div>
      </div>
    </div>
  </div>
<% end %>

<% if notice.present? %>
  <div class="rounded-md bg-yellow-50 p-4">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
        </svg>
      </div>
      <div class="ml-3">
        <h3 class="text-sm leading-5 font-medium text-yellow-800">
          Attention needed
        </h3>
        <div class="mt-2 text-sm leading-5 text-yellow-700">
          <p>
            <%= notice %>
          </p>
        </div>
      </div>
    </div>
  </div>
<% end %>

<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <img class="mx-auto h-12 w-auto" src="https://tailwindui.com/img/logos/workflow-mark-on-white.svg" alt="Workflow">
    <h2 class="mt-6 text-center text-3xl leading-9 font-extrabold text-gray-900">
      Log in to your account
    </h2>
    <p class="mt-2 text-center text-sm leading-5 text-gray-600 max-w">
      Or
      <%= link_to "sign up for an account", new_user_registration_path,
      class: "font-medium text-indigo-600 hover:text-indigo-500
              focus:outline-none focus:underline transition ease-in-out duration-150"
            %>
    </p>
  </div>
  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">

      <%= form_for :user, url: session_path(:user) do |f| %>
        <div class="form-inputs">

          <div>
            <%= f.label :email, class: "block text-sm font-medium leading-5 text-gray-700" do %>
              Email address
            <% end %>

            <div class="mt-1 rounded-md shadow-sm">
              <%= f.text_field :email,
                required: true,
                autofocus: true,
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 transition duration-150 ease-in-out sm:text-sm sm:leading-5",
                input_html: { autocomplete: "email" }
              %>
            </div>

          </div>

          <div class="mt-6">
            <label class="block text-sm font-medium leading-5 text-gray-700" for="user_password">Password</label>
            <div class="mt-1 rounded-md shadow-sm">
              <%= f.password_field :password,
                required: true,
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400
                        focus:outline-none focus:shadow-outline-blue focus:border-blue-300 transition duration-150
                        ease-in-out sm:text-sm sm:leading-5",
                        input_html: { autocomplete: "password" }
                      %>
            </div>
          </div>

          <div class="mt-6 flex items-center justify-between">
            <div class="flex items-center">

              <%= f.check_box :remember_me, class: "form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out"  %>
              <%= f.label :remember_me, class: "ml-2 block text-sm leading-5 text-gray-900"%>

            </div>
            <div class="text-sm leading-5">
              <%= link_to "Forgot your password?", new_password_path(resource_name),
                class: "font-medium text-indigo-600 hover:text-indigo-500
                        focus:outline-none focus:underline transition
                        ease-in-out duration-150" %>
            </div>
          </div>

        </div>

        <div class="mt-6">
          <span class="block w-full rounded-md shadow-sm">
            <%= f.submit "Sign In", class: "w-full flex justify-center py-2 px-4 border
                                            border-transparent text-sm font-medium rounded-md
                                            text-white bg-indigo-600 hover:bg-indigo-500
                                            focus:outline-none focus:border-indigo-700
                                            focus:shadow-outline-indigo active:bg-indigo-700
                                            transition duration-150 ease-in-out"
                                          %>
          </span>
        </div>
      <% end %>
      <div class="text-gray-400 text-sm mt-6">
        <%= link_to "Didn't receive confirmation instructions?", new_user_confirmation_path(resource_name) %><br />
      </div>

    </div>
  </div>
</div>

