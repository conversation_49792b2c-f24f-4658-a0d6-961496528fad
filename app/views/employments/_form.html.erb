<%= turbo_frame_tag dom_id(employment) do %>
  <%= form_with model: employment, scope: :employment, url: url, data: {"turbo-frame" => "_top"} , local: true do |f| %>

    <% if employment.errors.any? %>
      <div id="error_explanation" class="bg-red-50 text-red-800 p-8">
        <h2 class='mb-2'><%= pluralize(employment.errors.count, "error") %> prohibited this post from being saved:</h2>
        <ul class="list-disc ml-6">
          <% employment.errors.full_messages.each do |message| %>
            <li class="list-disc"><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <div class="">
      <div class="mt-6 sm:mt-5">
        <div class="sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:pt-5">
          <%= f.label :department, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <div class="max-w-sm rounded-md shadow-sm">
              <%= f.text_field :department, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" %>
            </div>
          </div>
        </div>

        <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= f.label :induction_date , class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <div class="max-w-sm rounded-md shadow-sm">
              <div class="max-w-lg mt-1 relative rounded-md shadow-sm">              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none ">
                  <%= inline_svg_tag( "calendar.svg", class: "fill-current h-4 w-4 text-gray-500" ) %>
                </div>
                <%= f.text_field :induction_date, as: :string,
                  data:
                  {
                    controller: "flatpickr",
                    flatpickr_alt_input: true,
                    flatpickr_alt_format: "l, d M Y",
                    flatpickr_date_format: "Y-m-d",
                    flatpickr_min_date: Time.zone.now - 80.years,
                    flatpickr_max_date: Time.zone.now ,
                    flatpickr_default_date: Time.zone.now - 1.day
                  },
                  class: "max-w-xs form-input block w-full pl-10 sm:text-sm sm:leading-5 text-gray-500" %>
              </div>

            </div>
          </div>
        </div>

        <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= f.label :position, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <div class="max-w-xs rounded-md shadow-sm">
              <%= f.text_field :position, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" %>
            </div>
          </div>
        </div>

        <div class="mt-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= f.label :employment_type, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <div class="max-w-xs rounded-md shadow-sm">

              <%= f.select :employment_type,
                ENUMS::EMPLOYMENT_TYPES.keys.map {|type| [type.titleize, type]},
                {include_blank: "Select medical type"},
                {class: "block form-select w-full transition
                    duration-150 ease-in-out sm:text-sm sm:leading-5"} %>
            </div>
          </div>
        </div>

        <div class="mt-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= f.label :company, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <% if @companies.blank? %>
              <div class="text-red-700 font-bold">
                Error - No companies in this organisation, please click here to add one
              </div>
            <% else %>
              <div class="max-w-xs rounded-md shadow-sm">
                <%= f.select :company_id,
                  @companies.map {|company| [company.name, company.id]},
                  {include_blank: "Select company"},
                  {class: "block form-select w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5"}%>
              </div>

            <% end %>
          </div>
        </div>

        <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= f.label :termination_date , class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <div class="max-w-sm rounded-md shadow-sm">
              <div class="max-w-lg mt-1 relative rounded-md shadow-sm">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none ">
                  <%= inline_svg_tag( "calendar.svg", class: "fill-current h-4 w-4 text-gray-500" ) %>
                </div>
                <%= f.text_field :termination_date, as: :string,
                  data:
                  {
                    controller: "flatpickr",
                    flatpickr_alt_input: true,
                    flatpickr_alt_format: "l, d M Y",
                    flatpickr_date_format: "Y-m-d",
                    flatpickr_min_date: Time.zone.now - 80.years,
                    flatpickr_max_date: Time.zone.now,
                    flatpickr_default_date: Time.zone.now
                  },
                  class: "max-w-xs form-input block w-full pl-10 sm:text-sm sm:leading-5 text-gray-500" %>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
          <%= f.label :termination_reason, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
          <div class="mt-1 sm:mt-0 sm:col-span-2">
            <div class="max-w-lg rounded-md shadow-sm">
              <%= f.text_field :termination_reason, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" %>
            </div>
          </div>
        </div>

      </div>
    </div>

    <div class="flex justify-between items-center mt-8 border-t border-gray-200 pt-5">
      <div class="hover:text-indigo-400 text-gray-400">
        <% if employment.persisted? %>
          <%= link_to "Delete", patient_path(employment.patient), 
            data: 
            { controller: "employment", 
              action: "employment#destroy",
              "turbo_method": :delete,
              "confirmation-message-value": 'Are you sure'
            } %>
        <% end %>
      </div>
      <div class="flex ">
        <span class="inline-flex rounded-md shadow-sm">
          <%= link_to "Cancel", :back,
            class: "py-2 px-4 border border-gray-300 rounded-md text-sm leading-5 font-medium
                   text-gray-700 hover:text-gray-500 focus:outline-none focus:border-blue-300
                   focus:shadow-outline-blue active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out" %>
        </span>
        <span class="ml-3 inline-flex rounded-md shadow-sm">
          <%= f.submit  "Save", class:"inline-flex justify-center py-2 px-4 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-500 focus:outline-none focus:border-indigo-700 focus:shadow-outline-indigo active:bg-indigo-700 transition duration-150 ease-in-out" %>
        </span>
      </div>
    <% end %>
  <% end %>
