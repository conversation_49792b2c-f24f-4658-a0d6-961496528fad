<% content_for :head do %>
  <%= render PatientInformationComponent.new patient: @patient%>
<% end %>

<div class="w-full flex ">
  <div class="w-full pb-5 space-y-3 flex-col sm:items-center sm:justify-between sm:space-y-0">
    <div class="justify-between flex mb-6">
      <h3 class="text-lg leading-6 font-mediumtext-gray-900">

      </h3>
      <div class="flex space-x-3">
        <span class="shadow-sm rounded-md">
          <%= link_to "Cancel",
            evaluation_path( @evaluation ),
            class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm
                      leading-5 font-medium rounded-md text-gray-700 bg-white
                      hover:text-gray-500 focus:outline-none focus:shadow-outline-blue
                      focus:border-blue-300 active:text-gray-800 active:bg-gray-50
                      transition duration-150 ease-in-out" %>
        </span>
        <span class="shadow-sm rounded-md">
          <%= button_to "Sign Off",
            evaluation_sign_off_path,
            method: :post,
            class: "inline-flex items-center px-4 py-2 border border-transparent
                      text-sm leading-5 font-medium rounded-md text-white bg-indigo-600
                      hover:bg-indigo-500 focus:outline-none focus:shadow-outline-indigo
                      focus:border-indigo-700 active:bg-indigo-700 transition duration-150
                      ease-in-out" %>
        </span>
      </div>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
      <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
          Medical Information
          <% unless @evaluation.status.blank?%>
            <span class="px-2 py-1 ml-2 bg-gray-200 rounded-full text-sm"><%= @evaluation.status.capitalize %></span>
          <% end %>
        </h3>
        <p class="mt-1 max-w-2xl text-sm leading-5 text-gray-500">
        Below contains the results for this medical
        </p>
      </div>
      <div>
        <dl>
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
              Medical Certificate Reference
            </dt>
            <dd class="mt-1 text-sm leading-5 text-red-600 sm:mt-0 sm:col-span-2">
              <%= @evaluation.name %>
            </dd>
          </div>
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
              Type of Medical
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
              <%= @evaluation.medical_type %>
            </dd>
          </div>

          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
              Outcome
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
              <%= @evaluation.outcome %>
            </dd>
          </div>

          <div class="bg-white-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
              Validity Period
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
              <%= "#{@evaluation.medical_examination_date} - #{ @evaluation.medical_expiry_date }"%>
            </dd>
          </div>

          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
              Final Comment
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
              <% if @evaluation.outcome_comment.blank?%>
                -- No comment was made --
              <% else %>
                <%= @evaluation.outcome_comment %>
              <% end %>
            </dd>
          </div>

          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
              Tests performed
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
              <ul>
                <% if @evaluation.cannabis_performed %>
                  <li>Urine - Cannabis</li>
                <% end %>
                <% if @evaluation.physical_exam_performed %>
                  <li>Physical Exam</li>
                <% end %>
                <% if @evaluation.visual_performed %>
                  <li>Vision Screening</li>
                <% end %>
                <% if @evaluation.audio_performed %>
                  <li>Audiometry</li>
                <% end %>
                <% if @evaluation.spiro_performed %>
                  <li>Spirometry</li>
                <% end %>
                <% if @evaluation.xray_performed %>
                  <li>Chest X Ray</li>
                <% end %>
                <% if @evaluation.ecg_performed %>
                  <li>Resting ECG</li>
                <% end %>
                <% if @evaluation.heat_performed %>
                  <li>Heat Stress Evaluation</li>
                <% end %>
                <% if @evaluation.height_performed %>
                  <li>Working @ Heights Evaluation</li>
                <% end %>
              </ul>
            </dd>
          </div>



          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
              Exclusion(s)
            </dt>
            <dd class=" mt-1 text-sm leading-5 flex flex-col text-gray-700 sm:mt-0 sm:col-span-2">
              <% if @evaluation.exclusion.blank? %>
                -- No exclusion required --
              <% else %>
                <div class="bg-white p-4 inline mt-8 text-sm leading-5">
                  <label class=" font-medium py-0.5 text-gray-700">Heat Stress</label>
                  <p class="text-gray-500 text-sm mt-1">Got Problems this guy</p>
                </div>
              <% end %>
            </dd>
          </div>

          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
              Referral(s)
            </dt>
            <dd class=" mt-1 text-sm leading-5 flex flex-col text-gray-700 sm:mt-0 sm:col-span-2">
              <% if @evaluation.referral.blank? %>
                -- No referral required --
              <% else %>
                <div class="bg-gray-50 p-4 rounded-lg inline text-sm leading-5">
                  <label class="font-medium py-0.5 text-gray-700">Dentist</label>
                  <p class="text-gray-500 text-sm mt-1">Get notified when someones does this a comment on a posting.</p>
                </div>
              <% end %>

            </dd>
          </div>

          <% if @evaluation.status == 'signed_off' %>
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm leading-5 font-medium text-gray-500">
                Signed off by
              </dt>
              <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= @evaluation.evaluation_signoff.user.name.full %>
              </dd>
            </div>
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm leading-5 font-medium text-gray-500">
                Sign off date
              </dt>
              <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= @evaluation.evaluation_signoff.date_signed.strftime("%d %B %Y") %>
              </dd>
            </div>

          <% end %>

        </dl>
      </div>
    </div>
    <div class="flex justify-between items-center  mt-8 pt-5">
      <div class="flex text-sm text-indigo-400">
        <%= link_to "Delete Medical", evaluation_path( @evaluation ), method: :delete %>
      </div>
      <div class="flex ">
      </div>
    </div>
  </div>
