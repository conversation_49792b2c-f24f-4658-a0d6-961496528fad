<%= turbo_frame_tag dom_id(evaluation) do %>
  <%= form_with model: evaluation, scope: :evaluation_form, data: {"turbo-frame" => "_top"}, url: url , local: true do |f| %>

    <% if evaluation.errors.any? %>
      <div id="error_explanation" class="bg-red-50 text-red-800 p-8">
        <h2 class='mb-2'><%= pluralize(evaluation.errors.count, "error") %> prohibited this post from being saved:</h2>

        <ul class="list-disc ml-6">
          <% evaluation.errors.full_messages.each do |message| %>
            <li class="list-disc"><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <div>
      <div>
        <h3 class="text-lg leading-6 font-medium text-gray-900">
          Patient Information
        </h3>
      </div>
      <div class="mt-5 border-t border-gray-200 pt-5">
        <dl>
          <div class=" sm:grid sm:grid-cols-3 sm:gap-4">
            <dt class="text-sm leading-5 font-medium text-gray-500">
              Identification Number
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
              <%= @patient.identification_number%>
            </dd>
          </div>
          <div class="mt-8 sm:grid sm:mt-5 sm:grid-cols-3 sm:gap-4 sm:border-t sm:border-gray-200 sm:pt-5">
            <dt class="text-sm leading-5 font-medium text-gray-500">
              Full name
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
              <%= @patient.name.full%>
            </dd>
          </div>
        </dl>
      </div>

      <div class="mt-10">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
          Clinic Information
        </h3>
      </div>

      <div class="mt-5 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start ">
        <label for="country" class="block text-sm font-medium leading-5 text-gray-500 sm:mt-px sm:pt-2">
          Clinic used
        </label>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <% if @patient.organisation.clinics.blank?%>
            <div class="text-red-700 font-bold">
              Error - No clinics for this organisation - please click here to add it
            </div>
          <% else %>
            <div class="max-w-xs rounded-md shadow-sm">
              <%= f.collection_select :clinic_id,
                @clinics, :id, :clinic_name,
                {include_blank: "Select the Clinic"},
                {class: "max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md"} %>
            </div>
          <% end %>
        </div>
      </div>

      <div class="mt-10">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
          Employment Information
        </h3>
      </div>

      <div class="mt-5 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start ">
        <label for="country" class="block text-sm font-medium leading-5 text-gray-500 sm:mt-px sm:pt-2">
          Employment
        </label>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <% if @patient.employments.blank?%>
            <div class="text-red-700 font-bold">
              Error - No employment history for this patient - <%= link_to 'please click here to add it', new_patient_employment_path(@patient), class: "hover:text-red-600"%>
            </div>
          <% else %>
            <div class="max-w-xs rounded-md shadow-sm">
              <%= f.select :employment_id, {}, {}, { class: "block form-select w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" } do %>
                <% @patient.employments.each do |employment| %>
                  <option value="<%= employment.id%>"><%= "#{ employment.company.name }| #{ employment.position }| #{ employment.induction_date }"%></option>
                <% end %>
              <% end %>
            </div>
          <% end %>
        </div>
      </div>

      <div class="mt-10">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
          Medical Information
        </h3>
      </div>

      <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <label for="last_name" class="block text-sm font-medium leading-5 text-gray-500 sm:mt-px sm:pt-2">
          Medical Reference Number
        </label>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-xs rounded-md shadow-sm">
            <%= f.text_field :name,
              class: "form-input block w-full transition
                    duration-150 ease-in-out sm:text-sm sm:leading-5"
                  %>
          </div>
        </div>
      </div>


      <div class="mt-5 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <label for="country" class="block text-sm font-medium leading-5 text-gray-500 sm:mt-px sm:pt-2">
          Medical Examination Type
        </label>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-xs rounded-md shadow-sm">

            <%= f.select :medical_type,
              ENUMS::EVALUATION_TYPES.values.map {|type| [type, type]},
              {include_blank: "Select medical type"},
              { class: "block form-select w-full transition
                    duration-150 ease-in-out sm:text-sm sm:leading-5" }
                  %>
          </div>
        </div>
      </div>

      </dl>
    </div>

    <div class="mt-5 sm:border-t sm:border-gray-200 sm:pt-5">
      <fieldset>
        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-baseline">
          <div>
            <legend class="text-base leading-6 font-medium text-gray-500 sm:text-sm sm:leading-5 sm:text-gray-500">
              Special Investigations Performed
            </legend>
          </div>
          <div class="mt-4 sm:mt-0 sm:col-span-2">
            <div class="max-w-lg">

              <%= render(InvestigationComponent.new(
                title: 'Physical Examination',
                select_collection: @physicals,
                form_builder: evaluation,
                input_name: 'physical_exam',
                input_checkbox: 'physical_exam_checkbox',
              )) %>

            <%= f.check_box :physical_exam_unlink %>

            <%= render(InvestigationComponent.new(
              title: 'Visual Screening',
              select_collection: @visuals,
              form_builder: evaluation,
              input_name: 'visual',
              input_checkbox: 'visual_checkbox',
            )) %>
          <%= f.check_box :visual_unlink %>

          <%= render(InvestigationComponent.new(
            title: 'Audiometry',
            select_collection: @audios,
            form_builder: evaluation,
            input_name: 'audio',
            input_checkbox: 'audio_checkbox',
          ))%>
        <%= f.check_box :audio_unlink %>

        <%= render(InvestigationComponent.new(
          title: 'Spirometry',
          select_collection: @spiros,
          form_builder: evaluation,
          input_name: 'spiro',
          input_checkbox: 'spiro_checkbox',
        ))%>
      <%= f.check_box :spiro_unlink %>

      <%= render(InvestigationComponent.new(
        title: 'Drug Screening',
        select_collection: @labs,
        form_builder: evaluation,
        input_name: 'drug',
        input_checkbox: 'drug_checkbox',
      ))%>
    <%= f.check_box :drug_unlink %>

            </div>
          </div>
        </div>
      </fieldset>
    </div>

    <div class="mt-8">
      <h3 class="text-lg leading-6 font-medium text-gray-900">
        Results
      </h3>
    </div>

    <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
      <label for="country" class="block text-sm font-medium leading-5 text-gray-500 sm:mt-px sm:pt-2">
        Conclusion
      </label>
      <div class="mt-1 sm:mt-0 sm:col-span-2">
        <div class="max-w-lg rounded-md shadow-sm">
          <%= f.select :outcome,
            ENUMS::OUTCOMES.values.map { |result| [result,result] },
            {include_blank: "Select the medical outcome"},
            { class: "block form-select w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" }
          %>
        </div>
      </div>
    </div>

    <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
      <label for="country" class="block text-sm font-medium leading-5 text-gray-500 sm:mt-px sm:pt-2">
        Exclusions
      </label>
      <div class="mt-1 sm:mt-0 sm:col-span-2">
        <div class="max-w-xs rounded-md shadow-sm">
          <%= f.collection_select :exclusion_ids, @exclusions, :id, :name, {:prompt => "Please select an exclusion"}, {:multiple => true}%>
        </div>
      </div>
    </div>


    <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
      <label for="country" class="block text-sm font-medium leading-5 text-gray-500 sm:mt-px sm:pt-2">
        Referrals
      </label>
      <div class="mt-1 sm:mt-0 sm:col-span-2">
        <div class="max-w-xs rounded-md shadow-sm">
        <%= f.collection_select :referral_ids, @referrals, :id, :name, {:prompt => "Please select a referral note"}, {:multiple => true}%>
        </div>
      </div>
    </div>

    <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
      <label for="country" class="block text-sm font-medium leading-5 text-gray-500 sm:mt-px sm:pt-2">
        Final Comments
      </label>
      <div class="mt-1 sm:mt-0 sm:col-span-2">
        <div class="max-w-lg flex rounded-md shadow-sm">
          <%= f.text_area :outcome_comment, rows: 3, class: "form-textarea block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" %>
        </div>
        <p class="mt-2 text-sm text-gray-500">Comment on the overall status of the patient's medical results.</p>
      </div>
    </div>

    <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
      <label for="country" class="block text-sm font-medium leading-5 text-gray-500 sm:mt-px sm:pt-2">
        Date of the Medical Examination
      </label>

      <div class="mt-1 sm:mt-0 sm:col-span-2">
        <div class="max-w-lg mt-1 relative rounded-md shadow-sm">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none ">
            <%= inline_svg_tag( "calendar.svg", class: "fill-current h-4 w-4 text-gray-500" ) %>
          </div>
          <%= f.text_field :medical_examination_date, as: :string,
            data:
            {
              controller: 'flatpickr',
              flatpickr_alt_input: true,
              flatpickr_alt_format: "l, d M Y",
              flatpickr_date_format: "Y-m-d",
              flatpickr_min_date: Time.zone.now - 45.days,
              flatpickr_max_date: Time.zone.now + 45.days,
              flatpickr_default_date: Time.zone.now
            },
            class: "max-w-xs form-input block w-full pl-10 sm:text-sm sm:leading-5 text-gray-500"
          %>
        </div>
      </div>
    </div>

    <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
      <label for="country" class="block text-sm font-medium leading-5 text-gray-500 sm:mt-px sm:pt-2">
        Expiry Date of the Medical
      </label>

      <div class="mt-1 sm:mt-0 sm:col-span-2">
        <div class="max-w-lg mt-1 relative rounded-md shadow-sm">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none ">
            <%= inline_svg_tag( "calendar.svg", class: "fill-current h-4 w-4 text-gray-500" ) %>
          </div>
          <%= f.text_field :medical_expiry_date, as: :string,
            data:
            {
              controller: "flatpickr",
              flatpickr_alt_input: true,
              flatpickr_alt_format: "l, d M Y",
              flatpickr_date_format: "Y-m-d",
              flatpickr_min_date: Time.zone.now,
              flatpickr_max_date: Time.zone.now + 545.days,
              flatpickr_default_date: Time.zone.now + 1.year
            },
            class: "max-w-xs form-input block w-full pl-10 sm:text-sm sm:leading-5 text-gray-500"
          %>
        </div>
      </div>
    </div>

    <div class="mt-8 border-t border-gray-200 pt-5">
      <div class="flex justify-end">
        <span class="inline-flex rounded-md shadow-sm">
          <button type="button" class="py-2 px-4 border border-gray-300 rounded-md text-sm leading-5 font-medium text-gray-700 hover:text-gray-500 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out">
            Cancel
          </button>
        </span>
        <span class="ml-3 inline-flex rounded-md shadow-sm">
          <%= f.submit 'Save', class: "inline-flex justify-center py-2 px-4 border border-transparent
          text-sm leading-5 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-500
          focus:outline-none focus:border-indigo-700 focus:shadow-outline-indigo active:bg-indigo-700 transition duration-150 ease-in-out" %>
        </span>
      </div>
    </div>

  <% end %>
<% end %>
