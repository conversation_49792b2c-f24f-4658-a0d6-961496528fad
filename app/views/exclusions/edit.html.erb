<% content_for :head do %>
  <header class="bg-gray-800 shadow-sm">
    <div class="max-w-7xl mx-auto py-4 sm:px-6 lg:px-8">
      <h1 class="leading-6 font-semibold text-white text-sm">
        <%= link_to @patient.name.full, patient_path( @patient ), class: 'text-gray-400'%> | Edit Exclusion
      </h1>
    </div>
  </header>
<% end %>

<%= form_with model: @exclusion, url: exclusion_path, local: true, html: { autocomplete: "off" } do |f| %>
  <div class="">

    <% if @exclusion.errors.any? %>
      <div id="error_explanation " class='bg-red-50 p-8 text-red-900'>
        <h2 class="font-semibold"><%= pluralize(@exclusion.errors.count, "error") %> prohibited this post from being saved:</h2>

        <ul>
          <% @exclusion.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <div class="mt-6 sm:mt-5">
      <div class="sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:pt-5">
        <%= f.label :category, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-sm rounded-md shadow-sm">
            <%= f.text_field :category, class: "max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
      </div>

      <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <%= f.label :note, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-xs rounded-md shadow-sm">
            <%= f.text_area :note, rows: 3, class: "max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
      </div>

    </div>
  </div>

  <div class="flex justify-between items-center  mt-8 border-t border-gray-200 pt-5">
    <div class="hover:text-indigo-400 text-gray-400">
      <% if @exclusion.persisted? %>
        <%= link_to "Delete", exclusion_path(@exclusion), method: "delete", data: { confirm: "Are you sure?", disable_with: "Processing..." } %>
      <% end %>
    </div>

    <div class="flex ">
      <span class="inline-flex rounded-md shadow-sm">
        <%= link_to "Cancel", :back,
          class: "py-2 px-4 border border-gray-300 rounded-md text-sm leading-5 font-medium
                   text-gray-700 hover:text-gray-500 focus:outline-none focus:border-blue-300
                   focus:shadow-outline-blue active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out" %>
      </span>
      <span class="ml-3 inline-flex rounded-md shadow-sm">
        <%= f.submit  "Save", class:"inline-flex justify-center py-2 px-4 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-500 focus:outline-none focus:border-indigo-700 focus:shadow-outline-indigo active:bg-indigo-700 transition duration-150 ease-in-out" %>
      </span>
    </div>
  </div>
<% end %>
