<div class="bg-white">
  <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="px-4 py-6 sm:px-0">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Expired Medicals Report</h1>
          <p class="mt-2 text-sm text-gray-600">
            Organisation: <%= @organisation.name %> | Generated on: <%= Date.current.strftime('%d %B %Y') %>
          </p>
        </div>
        <div class="flex space-x-3">
          <%= link_to "Export CSV", 
              expired_medicals_path(organisation_id: @organisation_id, format: :csv), 
              method: :get,
              class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500" %>
        </div>
      </div>
    </div>

    <!-- Summary -->
    <div class="px-4 py-4 sm:px-0">
      <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800">
              Summary
            </h3>
            <div class="mt-2 text-sm text-yellow-700">
              <p>Total companies with expired medicals: <strong><%= @grouped_expired_medicals.keys.count %></strong></p>
              <p>Total expired medicals: <strong><%= @total_expired %></strong></p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Content -->
    <% if @grouped_expired_medicals.empty? %>
      <div class="px-4 py-6 sm:px-0">
        <div class="text-center">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No expired medicals</h3>
          <p class="mt-1 text-sm text-gray-500">Great! All medicals are up to date.</p>
        </div>
      </div>
    <% else %>
      <div class="px-4 py-6 sm:px-0">
        <% @grouped_expired_medicals.each do |company_name, evaluations| %>
          <div class="mb-8">
            <!-- Company Header -->
            <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
              <h2 class="text-lg font-medium text-gray-900"><%= company_name %></h2>
              <p class="text-sm text-gray-600"><%= evaluations.count %> expired medical<%= evaluations.count == 1 ? '' : 's' %></p>
            </div>

            <!-- Table -->
            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5">
              <table class="min-w-full divide-y divide-gray-300">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Full Name
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Contact Number
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      ID Number
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Medical Reference
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Expired On
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <% evaluations.each do |evaluation| %>
                    <tr class="hover:bg-gray-50">
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        <%= evaluation.patient.full_name %>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <%= evaluation.patient.phone_number || "N/A" %>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <%= evaluation.patient.identification_number %>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <%= evaluation.name %>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">
                        <%= evaluation.medical_expiry_date.strftime('%d %b %Y') %>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          </div>
        <% end %>
      </div>
    <% end %>
  </div>
</div>
