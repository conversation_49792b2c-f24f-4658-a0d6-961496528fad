<% content_for :head do %>
  <header class="bg-gray-800 shadow-sm">
    <div class="max-w-7xl mx-auto py-4 sm:px-6 lg:px-8">
      <h1 class="leading-6 font-semibold text-white text-sm">
        <%= link_to "#{ @lab_test.patient.name.full} ( ID: #{ @lab_test.patient.identification_number })", patient_path( @lab_test.patient ), class: "font-normal text-gray-300" %> | Edit Drug Screening results
      </h1>
    </div>
  </header>
<% end %>

<div>

  <div class="mt-10">
    <h3 class="text-lg leading-6 font-medium text-gray-900">
      Results
    </h3>
  </div>

  <%= simple_form_for @lab_test, url: lab_test_path do |f| %>

    <div class="mt-6 sm:mt-5 sm:border-t sm:border-gray-200 sm:pt-5">
      <fieldset>
        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-baseline">
          <div>
            <legend class="text-base leading-6 font-medium text-gray-900 sm:text-sm sm:leading-5 sm:text-gray-700">
              Cannabis (Urine)
            </legend>
          </div>
          <div class="sm:col-span-2">
            <div class="max-w-lg">
              <div class="mt-4">
                <div class="flex-col items-center">

                  <%= f.collection_radio_buttons(
                        :cannabis, LabTest::LAB_RESULTS, :first, :last,
                  ) do |b|
                    b.label( class: "mt-4 block text-sm leading-5 font-medium text-gray-700" ) { b.radio_button( class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out mr-3") + b.text }
                  end %>

                </div>
              </div>
            </div>
          </div>
      </fieldset>
        </div>

        <div class="mt-6 sm:mt-5 sm:border-t sm:border-gray-200 sm:pt-5">
          <fieldset>
            <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-baseline">
              <div>
                <legend class="text-base leading-6 font-medium text-gray-900 sm:text-sm sm:leading-5 sm:text-gray-700">
                  Six Panel Drug Test
                </legend>
              </div>
              <div class="sm:col-span-2">
                <div class="max-w-lg">
                  <div class="mt-4">
                    <div class="flex-col items-center">

                      <%= f.collection_radio_buttons(
                            :six_panel, LabTest::LAB_RESULTS, :first, :last,
                      ) do |b|
                        b.label( class: "mt-4 block text-sm leading-5 font-medium text-gray-700" ) { b.radio_button( class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out mr-3") + b.text }
                      end %>

          </fieldset>
                    </div>
                    <div class="mt-6 sm:mt-5 sm:border-t sm:border-gray-200 sm:pt-5">
                      <fieldset>
                        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-baseline">
                          <div>
                            <legend class="text-base leading-6 font-medium text-gray-900 sm:text-sm sm:leading-5 sm:text-gray-700">
                              Gamma GT
                            </legend>
                          </div>
                          <div class="sm:col-span-2">
                            <div class="max-w-lg">
                              <div class="mt-4">
                                <div class="flex-col items-center">

                                  <%= f.collection_radio_buttons(
                                        :gamma, LabTest::LAB_RESULTS, :first, :last,
                                  ) do |b|
                                    b.label( class: "mt-4 block text-sm leading-5 font-medium text-gray-700" ) { b.radio_button( class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out mr-3") + b.text }
                                  end %>

                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </fieldset>
                    </div>
                    <div class="mt-6 sm:mt-5 sm:border-t sm:border-gray-200 sm:pt-5">
                      <fieldset>
                        <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-baseline">
                          <div>
                            <legend class="text-base leading-6 font-medium text-gray-900 sm:text-sm sm:leading-5 sm:text-gray-700">
                              AST
                            </legend>
                          </div>
                          <div class="sm:col-span-2">
                            <div class="max-w-lg">
                              <div class="mt-4">
                                <div class="flex-col items-center">

                                  <%= f.collection_radio_buttons(
                                        :ast, LabTest::LAB_RESULTS, :first, :last,
                                  ) do |b|
                                    b.label( class: "mt-4 block text-sm leading-5 font-medium text-gray-700" ) { b.radio_button( class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out mr-3") + b.text }
                                  end %>

                                </div>
                              </div>
                            </div>
                          </div>
                      </fieldset>
                        </div>
                        <div class="mt-6 sm:mt-5 sm:border-t sm:border-gray-200 sm:pt-5">
                          <fieldset>
                            <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-baseline">
                              <div>
                                <legend class="text-base leading-6 font-medium text-gray-900 sm:text-sm sm:leading-5 sm:text-gray-700">
                                  FBC
                                </legend>
                              </div>
                              <div class="sm:col-span-2">
                                <div class="max-w-lg">
                                  <div class="mt-4">
                                    <div class="flex-col items-center">
                                      <%= f.collection_radio_buttons(
                                            :fbc, LabTest::LAB_RESULTS, :first, :last,
                                      ) do |b|
                                        b.label( class: "mt-4 block text-sm leading-5 font-medium text-gray-700" ) { b.radio_button( class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out mr-3") + b.text }
                                      end %>
                                    </div>
                                  </div>
                                </div>
                              </div>
                          </fieldset>
                            </div>

                            <div class="mt-6 sm:mt-5 sm:border-t sm:border-gray-200 sm:pt-5">
                              <fieldset>
                                <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-baseline">
                                  <div>
                                    <legend class="text-base leading-6 font-medium text-gray-900 sm:text-sm sm:leading-5 sm:text-gray-700">
                                      HIV
                                    </legend>
                                  </div>
                                  <div class="sm:col-span-2">
                                    <div class="max-w-lg">
                                      <div class="mt-4">
                                        <div class="flex-col items-center">
                                          <%= f.collection_radio_buttons(
                                                :hiv, LabTest::LAB_RESULTS, :first, :last,
                                          ) do |b|
                                            b.label( class: "mt-4 block text-sm leading-5 font-medium text-gray-700" ) { b.radio_button( class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out mr-3") + b.text }
                                          end %>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                              </fieldset>
                                </div>

                                <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">                                  <label for="about" class="block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                   Date Performed
                                  </label>
                                 <div class="mt-1 sm:mt-0 sm:col-span-2">
                                   <div class="max-w-lg flex rounded-md shadow-sm">
                                     <%= f.text_field :date_performed, as: :string,
                                        data:
                                        {
                                          controller: "flatpickr",
                                          flatpickr_alt_input: true,
                                          flatpickr_alt_format: "l, d M Y",
                                          flatpickr_date_format: "Y-m-d",
                                          flatpickr_min_date: Time.zone.now - 45.days,
                                          flatpickr_max_date: Time.zone.now + 45.days,
                                          flatpickr_default_date: Time.zone.now
                                        },
                                        class: "max-w-xs form-input block w-full pl-10 sm:text-sm sm:leading-5 text-gray-500" %>
                                    </div>
                                 </div>
                               </div>

                                <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
                                 <label for="about" class="block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                   Performed By
                                  </label>
                                 <div class="mt-1 sm:mt-0 sm:col-span-2">
                                   <div class="max-w-lg flex rounded-md shadow-sm">
                                     <%= f.select :performed_by,
                                        @users.map{ |result| [result.name.full,result.name.full] },
                                        {include_blank: "Select the User"},
                                        {class: "block form-select w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5"} %>
                                    <%= f.error_for :performed_by, class: "text-red-600 block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" %>
                                    </div>
                                 </div>
                               </div>

                                <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
                                 <label for="about" class="block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                   Clinic
                                  </label>
                                 <div class="mt-1 sm:mt-0 sm:col-span-2">
                                   <div class="max-w-lg flex rounded-md shadow-sm">
                                     <%= f.select :clinic_id,
                                        @clinics.map{ |result| [result.clinic_name,result.id] },
                                        {include_blank: "Select the Clinic"},
                                        {class: "block form-select w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5"} %>
                                    <%= f.error_for :clinic_id, class: "text-red-600 block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" %>
                                    </div>
                                 </div>
                               </div>

                                <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
                                 <label for="about" class="block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
                                   Results
                                  </label>
                                 <div class="mt-1 sm:mt-0 sm:col-span-2">
                                   <div class="max-w-lg flex rounded-md shadow-sm">
                                     <%= f.input_field :comment, rows: 3, class: "form-textarea block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" %>
                                    </div>
                                   <p class="mt-2 text-sm text-gray-500">Write a few sentences about the Lab test results.</p>
                                 </div>
                               </div>

                                <div class="mt-8 border-t border-gray-200 pt-5">
                                  <div class="flex justify-end">
                                    <span class="inline-flex rounded-md shadow-sm">
                                      <button type="button"
                 class="py-2 px-4 border border-gray-300 rounded-md text-sm leading-5 font-medium text-gray-700 hover:text-gray-500 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out">
                                        Cancel
                                      </button>
                                    </span>
                                    <span class="ml-3 inline-flex rounded-md shadow-sm">
                                      <%= f.button :submit, "Save", class: "inline-flex justify-center py-2 px-4 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-500 focus:outline-none focus:border-indigo-700 focus:shadow-outline-indigo active:bg-indigo-700 transition duration-150 ease-in-out" %>
                                    </span>
                                  </div>
                                </div>

                                <%= f.error_notification %>
                                <%= f.error_notification message: f.object.errors[:base].to_sentence if f.object.errors[:base].present? %>

                              <% end %>
