<% content_for :head do %>
  <header class="bg-gray-800 shadow-sm">
    <div class="max-w-7xl mx-auto py-4 sm:px-6 lg:px-8">
      <h1 class="leading-6 font-semibold text-white text-sm">
        <%= link_to "#{ @lab_test.patient.name.full} ( ID: #{ @lab_test.patient.identification_number })", patient_path( @lab_test.patient ), class: "font-normal text-gray-300" %>
        | Display Drug Screening results
      </h1>
    </div>
  </header>
<% end %>

<div class="w-full flex ">
  <div class="w-full pb-5 space-y-3 flex-col sm:items-center sm:justify-between sm:space-y-0">
    <div class="justify-between flex mb-6">
      <h3 class="text-lg leading-6 font-mediumtext-gray-900">

      </h3>
      <div class="flex space-x-3">
        <span class="shadow-sm rounded-md">
          <%= button_to "Edit",
            edit_lab_test_path( @lab_test ),
            method: :get,
            class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm
                      leading-5 font-medium rounded-md text-gray-700 bg-white
                      hover:text-gray-500 focus:outline-none focus:shadow-outline-blue
                      focus:border-blue-300 active:text-gray-800 active:bg-gray-50
                      transition duration-150 ease-in-out" %>
        </span>
        <span class="shadow-sm rounded-md">
          <%= button_to "Review",
            new_lab_test_sign_off_path(@lab_test),
            method: :get,
            class: "inline-flex items-center px-4 py-2 border border-transparent
                      text-sm leading-5 font-medium rounded-md text-white bg-indigo-600
                      hover:bg-indigo-500 focus:outline-none focus:shadow-outline-indigo
                      focus:border-indigo-700 active:bg-indigo-700 transition duration-150
                      ease-in-out" %>
        </span>
      </div>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
      <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
          Drug Screening Results
          <% unless @lab_test.status.blank? %>
            <span class="px-2 py-1 ml-2 bg-gray-200 rounded-full text-sm"><%= @lab_test.status.capitalize %></span>
          <% end %>

        </h3>
        <p class="mt-1 max-w-2xl text-sm leading-5 text-gray-500">
          Below contains the results for this test
        </p>
      </div>
      <div>
        <dl>
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
            Patient name
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
            <%= @lab_test.patient.name.full %>
            </dd>
          </div>
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
            Performed By
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
            <%= @lab_test.performed_by %>
            </dd>
          </div>

          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
            Date performed
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
            <%= @lab_test.date_performed.strftime("%d %B %Y") %>
            </dd>
          </div>

          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
            Screening Report
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">

            <ul class="max-w-md border border-gray-200 rounded-md">
              <%= render DrugScreeningReportItemComponent.new name: "Cannabis Testing", result: @lab_test.cannabis %>
              <%= render DrugScreeningReportItemComponent.new name: "Six Panel", result: @lab_test.six_panel %>
              <%= render DrugScreeningReportItemComponent.new name: "Gamma", result: @lab_test.gamma %>
              <%= render DrugScreeningReportItemComponent.new name: "HIV", result: @lab_test.hiv %>
              <%= render DrugScreeningReportItemComponent.new name: "AST", result: @lab_test.ast %>
              <%= render DrugScreeningReportItemComponent.new name: "FBC", result: @lab_test.fbc %>
            </ul>
            </dd>
          </div>
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
            Tests not performed
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
            <ul>
              <% if @lab_test.hiv == "Not Performed" %>
                <li>HIV</li>
              <% end %>
              <% if @lab_test.gamma == "Not Performed" %>
                <li>Gamma GT</li>
              <% end %>
              <% if @lab_test.fbc == "Not Performed" %>
                <li>FBC</li>
              <% end %>
              <% if @lab_test.ast == "Not Performed" %>
                <li>AST</li>
              <% end %>
              <% if @lab_test.cannabis == "Not Performed" %>
                <li>Cannabis</li>
              <% end %>
              <% if @lab_test.six_panel == "Not Performed" %>
                <li>Six Panel Drug Test</li>
              <% end %>
            </ul>
            </dd>
          </div>
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
            Clinic
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
            <%= @lab_test.clinic.clinic_name %>
            </dd>
          </div>
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
            Results
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
            <% if @lab_test.result.blank? %>
              -- No notable abnormalities --
            <% else %>
              <%= @lab_test.result %>
            <% end %>
            </dd>
          </div>
          <% if @lab_test.status == 'signed off' %>
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm leading-5 font-medium text-gray-500">
              Signed off by
              </dt>
              <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
              <%= @lab_test.lab_test_signoff.user.name.full %>
              </dd>
            </div>
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm leading-5 font-medium text-gray-500">
              Sign off date
              </dt>
              <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
              <%= @lab_test.lab_test_signoff.date_signed.strftime("%d %B %Y") %>
              </dd>
            </div>

          <% end %>

          <% if @lab_test.assessment_reports %>
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm leading-5 font-medium text-gray-500">
              Attachments
              </dt>
              <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
              <ul class="border border-gray-200 rounded-md">
                <% @lab_test.assessment_reports.each do |report| %>
                  <li class="border-t border-gray-200 first:border-t-0 pl-3 pr-4 py-3 flex items-center justify-between text-sm leading-5">
                    <div class="w-0 flex-1 flex items-center">
                      <svg class="flex-shrink-0 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a1 1 0 112 0v4a7 7 0 11-14 0V7a5 5 0 0110 0v4a3 3 0 11-6 0V7a1 1 0 012 0v4a1 1 0 102 0V7a3 3 0 00-3-3z" clip-rule="evenodd" />
                      </svg>
                      <span class="ml-2 flex-1 w-0 truncate">
          <%= @lab_test.report_name %>
                      </span>
                    </div>
                    <div class="ml-4 flex-shrink-0">
                      <%= link_to "Download", generated_report_path( report ), target: "_blank", class: "font-medium text-indigo-600 hover:text-indigo-500 transition duration-150 ease-in-out" %>
                    </div>
                  </li>
                <% end %>
              </ul>
              </dd>
            </div>
          <% end %>
        </dl>
      </div>
    </div>
  </div>
