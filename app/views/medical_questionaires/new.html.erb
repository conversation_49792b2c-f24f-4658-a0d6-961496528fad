<% content_for :head do %>
  <header class="bg-gray-800 shadow-sm">
    <div class="max-w-7xl mx-auto py-4 sm:px-6 lg:px-8">
      <h1 class="leading-6 font-semibold text-white text-sm">
        Add a new Occupational Medical Questionaire 
      </h1>
    </div>
  </header>
<% end %>

<%= form_with scope: :questionaire, url: patient_medical_questionaires_path do |f| %>
  <div class="flex-1 flex flex-col">
    <main class="flex-1 overflow-y-auto focus:outline-none" tabindex="0">
      <div class="relative max-w-4xl mx-auto md:px-8 xl:px-0">
        <!-- Description list with inline editing -->
        <div class="mt-10 space-y-6 divide-y divide-gray-200">
          <div class="space-y-1">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Personal History 
            </h3>
            <p class="max-w-2xl text-sm leading-5 text-gray-500">
            </p>
          </div>
          <div>
            <dl class="divide-y divide-gray-200">

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Do you smoke 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Do you use alcohol 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :alcohol_user, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Do you use drugs 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :alcohol_user, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Do you exercise regularly 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :alcohol_user, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 items-center sm:grid sm:grid-cols-3 sm:gap-4">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Comment 
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-1">
                <%= f.text_field :comment, class: "form-input block w-full
                                                       transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 items-center sm:grid sm:grid-cols-3 sm:gap-4">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Performed by 
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-1">
                <%= f.text_field :performed_by, value: current_user.name.full, class: "form-input block w-full
                                                       transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                </dd>
              </div>

              <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
                <%= f.label "Date of Screening" , class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
                <div class="mt-1 sm:mt-0 sm:col-span-2">
                  <div class="max-w-sm rounded-md shadow-sm">
                    <div class="max-w-lg mt-1 relative rounded-md shadow-sm">
                      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none ">
                        <%= inline_svg_tag( "calendar.svg", class: "fill-current h-4 w-4 text-gray-500" ) %>
                      </div>
                      <%= f.text_field :date_of_screening, as: :string,
                        data:
                        {
                          controller: "flatpickr",
                          flatpickr_alt_input: true,
                          flatpickr_alt_format: "l, d M Y",
                          flatpickr_date_format: "Y-m-d",
                          flatpickr_min_date: Time.zone.now - 12.month,
                          flatpickr_max_date: Time.zone.now ,
                          flatpickr_default_date: Time.zone.now - 1.day
                        },
                        class: "max-w-xs form-input block w-full pl-10 sm:text-sm sm:leading-5 text-gray-500"
                      %>
                    </div>

                  </div>
                </div>
              </div>
            </dl>
          </div>
        </div>

        <div class="mt-10 space-y-6 divide-y divide-gray-200">
          <div class="space-y-1">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Family History 
            </h3>
            <p class="max-w-2xl text-sm leading-5 text-gray-500">
            </p>
          </div>
          <div>
            <dl class="divide-y divide-gray-200">

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500 sm:col-span-1">
                  Have any of your relatives suffered from Hypertention, high cholesterol, heart disease, epilepsy, diabetes, blindness, porphyria, cancer or any other disease 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

            </dl>
          </div>
        </div>

        <div class="mt-10 space-y-6 divide-y divide-gray-200">
          <div class="space-y-1">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Occupational History 
            </h3>
            <p class="max-w-2xl text-sm leading-5 text-gray-500">
            </p>
          </div>
          <div>
            <dl class="divide-y divide-gray-200">

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Noise Exposure
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Radiation Exposure
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Dust Exposure
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Hazardous Chemicals Exposure
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Heat Stress Exposure
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Other
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

            </dl>
          </div>
        </div>

        <div class="mt-10 space-y-6 divide-y divide-gray-200">
          <div class="space-y-1">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Medical History 
            </h3>
            <p class="max-w-2xl text-sm leading-5 text-gray-500">
            </p>
          </div>
          <div>
            <dl class="divide-y divide-gray-200">

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Heart Disease, high blood pressure, chest pain from blood clots
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Asthma,tubercolosis, chronic bronchitis or shortness of breath 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Hayfever 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Heartburn
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Gout 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Epilepsy 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Depression 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                   Loss of Hearing 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Diabetes 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Disorders of Kidney 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Scaling 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Cancer or tumour 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Operations 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Any other conditions 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Current medication 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Prosthesis 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Fear of confined spaces 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Injury of any kind 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Fear of heights 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                  Females: Pregant? 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                   Is everything correct?? 
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :smoker, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>


            </dl>
          </div>
        </div>

        <div class="mt-8 border-t border-gray-200 pt-5">    <div class="flex justify-end">
            <span class="inline-flex rounded-md shadow-sm">
              <%= link_to "Cancel", :back,
                class: "py-2 px-4 border border-gray-300 rounded-md text-sm leading-5 font-medium
                         text-gray-700 hover:text-gray-500 focus:outline-none focus:border-blue-300
                         focus:shadow-outline-blue active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out" %>
            </span>
            <span class="ml-3 inline-flex rounded-md shadow-sm">
              <%= f.submit  "Save", class:"inline-flex justify-center py-2 px-4 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-500 focus:outline-none focus:border-indigo-700 focus:shadow-outline-indigo active:bg-indigo-700 transition duration-150 ease-in-out" %>
            </span>
          </div>
        </div>
      </div>
  </div>
    </main>
    </div>


  <% end %>

