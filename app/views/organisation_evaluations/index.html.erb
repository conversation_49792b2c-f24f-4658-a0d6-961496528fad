<% content_for :head do %>
  <header class="bg-gray-800 shadow-sm">
    <div class="max-w-7xl mx-auto py-4 sm:px-6 lg:px-8">
      <h1 class="leading-6 font-semibold text-white text-sm">
        Evaluation Listing
      </h1>
    </div>
  </header>
<% end %>

<div data-controller="evaluation-search" data-reflex-root="#morph" class="w-full">

  <div class="box py-6">
    <div class="box-wrapper flex justify-between items-center">
      <div class=" bg-white rounded flex items-center w-3/5 p-3 shadow-sm border border-gray-200">
        <svg class=" w-5 text-gray-600 h-5 "
             fill="none" stroke-linecap="round" stroke-linejoin="round"
                                                stroke-width="2" stroke="currentColor" viewBox="0 0 24 24">
          <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
        <input type="search"
               id="evaluation_search"
               placeholder="Search for certificate by patient name or id"
               data-target="evaluation-search.query"
               data-action="debounced:input->evaluation-search#perform"
               class="w-full pl-4 text-sm outline-none focus:outline-none bg-transparent">

      </div>

    </div>
  </div>

  <span data-target="evaluation-search.activity" class="text-danger flex items-center hidden" >
    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    Searching for certificates...
  </span>


  <div class="flex flex-col <%= "hidden" unless @data.present? %>" data-target="evaluation-search.list" >
    <div class="-my-2 py-2 overflow-x-auto sm:-mx-6 sm:px-6 lg:-mx-8 lg:px-8">
      <div class="align-middle inline-block min-w-full shadow overflow-hidden sm:rounded-lg border-b border-gray-200">
        <table class="min-w-full">
          <thead>
            <tr>
              <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                Patient Name (Medical Certifcate Ref.)
              </th>
              <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                Employer
              </th>
              <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                ID 
              </th>
              <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                medical_examination_date
              </th>
              <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                Validity
              </th>


              <th class="px-6 py-3 border-b border-gray-200 bg-gray-50">
              </th>
            </tr>
          </thead>
          <tbody>
            <% if @data.present? %>
              <% @data.each do |evaluation| %>
                <tr class="bg-white even:bg-gray-50">
                  <td class="px-6 py-4 whitespace-no-wrap text-sm leading-5 font-medium text-gray-900">
                    <%= "#{ evaluation.patient.name.full} "%>
                    <span class="text-gray-400"><%= "- Ref:#{ evaluation.name }"%></span>
                  </td>
                  <td class="px-6 py-4 whitespace-no-wrap text-sm leading-5 text-gray-500">
                    <% evaluation.patient.employers.first.name %>
                  </td>
                  <td class="px-6 py-4 whitespace-no-wrap text-sm leading-5 text-gray-500">
                    <%= evaluation.patient.identification_number %>
                  </td>
                  <td class="px-6 py-4 whitespace-no-wrap text-sm leading-5 text-gray-500">
                    <%= evaluation.medical_examination_date %>
                  </td>
                  <td class="px-6 py-4 whitespace-no-wrap text-sm leading-5 text-gray-500">
                    <%= evaluation.medical_expiry_date %>
                  </td>

                  <td class="px-6 py-4 whitespace-no-wrap text-right text-sm leading-5 font-medium">
                    <%= link_to "Select", evaluation_path(evaluation.id), class:"text-indigo-600 hover:text-indigo-900 focus:outline-none focus:underline"%>
                  </td>
                </tr>
              <% end %>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>



