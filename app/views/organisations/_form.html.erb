
<%= simple_form_for(@organisation) do |f| %>
  <%= f.error_notification %>
  <%= f.error_notification message: f.object.errors[:base].to_sentence if f.object.errors[:base].present? %>

  <div class="form-inputs">
    <%= f.input :organisation_name %>
    <%= f.input :registration_number %>
    <%= f.input :email_address %>
    <%= f.input :web_address %>
    <%= f.input :contact_number %>
  </div>

  <div class="form-actions">
    <%= f.button :submit %>
  </div>
<% end %>
