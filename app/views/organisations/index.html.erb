<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4 lg:mt-10">
  <div class="md:flex md:items-center md:justify-between">
    <div class="flex-1 min-w-0">
      <h2 class="text-center text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:leading-9 sm:truncate">
        Choose the Organisation
      </h2>
    </div>
  </div>
  <div class="mt-4 sm:mt-8 grid grid-cols-1 sm:grid-cols-4 col-gap-4 row-gap-4">
    <% @organisations.each do |organisation| %>
        <li class="col-span-2 flex flex-col text-center bg-white rounded-lg shadow">
          <%= link_to organisation_path(organisation) do %>
          <div class="flex-1 flex flex-col p-8">
            <img class="w-32 h-32 flex-shrink-0 mx-auto bg-black rounded-full" src="https://source.unsplash.com/RFDP7_80v5A/256x256" alt="building image">
            <h3 class="mt-6 text-gray-900 text-sm leading-5 font-medium"><%= organisation.initials || organisation.organisation_name.chr.capitalize%></h3>
            <dl class="mt-1 flex-grow flex flex-col justify-between">
              <dt class="sr-only">Title</dt>
              <dd class="text-gray-500 text-sm leading-5"><%= organisation.organisation_name %></dd>
              <dt class="sr-only">Preference</dt>
              <dd class="mt-3">
              <span class="px-2 py-1 text-teal-800 text-xs leading-4 font-medium bg-teal-100 rounded-full">Default</span>
              </dd>
            </dl>
          </div>
          <%# <div class="border-t border-gray-200">
 %>
            <%#   <div class="-mt-px flex">
 %>
              <%#     <div class="w-0 flex-1 flex border-r border-gray-200">
 %>
                <%#       <a href="#" class="relative -mr-px w-0 flex-1 inline-flex items-center justify-center py-4 text-sm leading-5 text-gray-700 font-medium border border-transparent rounded-bl-lg hover:text-gray-500 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 focus:z-10 transition ease-in-out duration-150">
 %>
                  <%#         <svg class="w-5 h-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
 %>
                    <%#           <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
 %>
                    <%#           <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
 %>
                    <%#         </svg>
 %>
                  <%#         <span class="ml-3">Email</span>
 %>
                  <%#       </a>
 %>
                <%#     </div>
 %>
              <%#     <div class="-ml-px w-0 flex-1 flex">
 %>
                <%#       <a href="#" class="relative w-0 flex-1 inline-flex items-center justify-center py-4 text-sm leading-5 text-gray-700 font-medium border border-transparent rounded-br-lg hover:text-gray-500 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 focus:z-10 transition ease-in-out duration-150">
 %>
                  <%#         <svg class="w-5 h-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
 %>
                    <%#           <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
 %>
                    <%#         </svg>
 %>
                  <%#         <span class="ml-3">Call</span>
 %>
                  <%#       </a>
 %>
                <%#     </div>
 %>
              <%#   </div>
 %>
            <%# </div>
 %>
        <% end %>
        </li>
    <% end %>
  </div>

<div class="my-8 text-center text-gray-700 text-sm font-medium">
  If you don't see an organisation that should be here?
  <a class="text-indigo-700 hover:text-indigo-600" href="mailto:<EMAIL>?subject=A%20new%20org%20request%">Let me know</a>
  <p></p>
</div>
</div>


