<main class="flex-1 relative pb-8 z-0 overflow-y-auto">
  <div class="mt-8">
    <div class="">
      <h2 class="text-lg leading-6 font-medium text-cool-gray-900">Overview</h2>
      <div class="mt-2 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        <!-- Card -->
        
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">

                <svg class="h-6 w-6 text-cool-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"> <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"> </path> </svg>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm leading-5 font-medium text-cool-gray-500 truncate">
                  Patients
                  </dt>
                  <dd>
                  <div class="text-lg leading-7 font-medium text-cool-gray-900">
                    <%= @patient_count%>
                  </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-cool-gray-50 px-5 py-3">
            <div class="text-sm leading-5">
              <%= link_to "View all", organisation_patients_path(params[:id]), class: "font-medium text-indigo-600 hover:text-indigo-900 transition ease-in-out duration-150" %>
            </div>
          </div>
        </div>
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">

                <svg class="h-6 w-6 text-cool-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"> <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"> </path> </svg>

              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm leading-5 font-medium text-cool-gray-500 truncate">
                  Medical Certificate
                  </dt>
                  <dd>
                  <div class="text-lg leading-7 font-medium text-cool-gray-900">
                    <%= @evaluations_count %>
                  </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-cool-gray-50 px-5 py-3">
            <div class="text-sm leading-5">
              <%= link_to "View all", organisation_evaluations_path(params[:id]), class: "font-medium text-indigo-600 hover:text-indigo-900 transition ease-in-out duration-150" %>
            </div>
          </div>
        </div>
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">

                <svg class="h-6 w-6 text-cool-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path></svg>

              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm leading-5 font-medium text-cool-gray-500 truncate">
                  Companies
                  </dt>
                  <dd>
                  <div class="text-lg leading-7 font-medium text-cool-gray-900">
                    <%= @company_count %>
                  </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-cool-gray-50 px-5 py-3">
            <div class="text-sm leading-5">
              <%= link_to "View all", organisation_companies_path(params[:id]), class: "font-medium text-indigo-600 hover:text-indigo-900 transition ease-in-out duration-150" %>
            </div>
          </div>
        </div>
        
        <!-- More cards... -->
      </div>
    </div>
    

    <div>
      <h2 class="max-w-6xl mt-8 text-lg leading-6 font-medium text-cool-gray-900 ">
        Quick Action
      </h2>
      <ul class="mt-3 grid grid-cols-1 gap-5 sm:gap-6 sm:grid-cols-2 lg:grid-cols-5">

        <li class="col-span-1 flex items-center bg-white border border-gray-200 rounded-md shadow-sm overflow-hidden">
          <div class="flex-shrink-0 flex items-center justify-center w-16 h-16 text-white text-center text-sm leading-5 font-medium bg-red-600">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"></path></svg>
          </div>
          <%= link_to new_organisation_patient_path(params[:id]), class:"flex-1 px-4 py-2 truncate" do %>
            <div class="text-gray-900 text-sm leading-5 font-medium hover:text-gray-600 transition ease-in-out duration-150">
              New Patient
            </div>
          <% end %>
        </li>

        <li class="col-span-1 flex items-center bg-white border border-gray-200 rounded-md shadow-sm overflow-hidden">
          <div class="flex-shrink-0 flex items-center justify-center w-16 h-16 text-white text-center text-sm leading-5 font-medium bg-gray-800">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clip-rule="evenodd"></path></svg>
          </div>
          <%= link_to new_organisation_company_path(params[:id]), class:"flex-1 px-4 py-2 truncate" do %>
            <div class="text-gray-900 text-sm leading-5 font-medium hover:text-gray-600 transition ease-in-out duration-150">
              New Company
            </div>
          <% end %>
        </li>
      </ul>
    </div>


    <h2 class="max-w-6xl  mt-8  text-lg leading-6 font-medium text-cool-gray-900 ">
      Recent activity
    </h2>
    
    <!-- Activity list (smallest breakopoint only) -->
    <div class="shadow sm:hidden">
      <ul class="mt-2 divide-y divide-cool-gray-200 overflow-hidden shadow sm:hidden">
        <li>
          <a href="#" class="block px-4 py-4 bg-white hover:bg-cool-gray-50">
            <div class="flex items-center space-x-4">
              <div class="flex-1 flex space-x-2 truncate">
                <svg class="flex-shrink-0 h-5 w-5 text-cool-gray-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                </svg>
                <div class="text-cool-gray-500 text-sm truncate">
                  <p class="truncate">[Aqua &middot Bulk] John Favior</p>
                  <p>July 11, 2020</p>
                </div>
              </div>
              <div>
                <svg class="flex-shrink-0 h-5 w-5 text-cool-gray-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
              </div>
            </div>
          </a>
        </li>
        
        <!-- More items... -->
      </ul>
      <%# <nav class="bg-white px-4 py-3 flex items-center justify-between border-t border-cool-gray-200">
 %>
        <%#   <div class="flex-1 flex justify-between">
 %>
          <%#     <a href="#" class="relative inline-flex items-center px-4 py-2 border border-cool-gray-300 text-sm leading-5 font-medium rounded-md text-cool-gray-700 bg-white hover:text-cool-gray-500 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 active:bg-cool-gray-100 active:text-cool-gray-700 transition ease-in-out duration-150">
 %>
            <%#       Previous
 %>
            <%#     </a>
 %>
          <%#     <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-cool-gray-300 text-sm leading-5 font-medium rounded-md text-cool-gray-700 bg-white hover:text-cool-gray-500 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 active:bg-cool-gray-100 active:text-cool-gray-700 transition ease-in-out duration-150">
 %>
            <%#       Next
 %>
            <%#     </a>
 %>
          <%#   </div>
 %>
        <%# </nav>
 %>
    </div>
    
    <!-- Activity table (small breakopoint and up) -->
    <div class="hidden sm:block">
      <div class="">
        <div class="flex flex-col mt-2">
          <div class="align-middle min-w-full overflow-x-auto shadow overflow-hidden sm:rounded-lg">
            <table class="min-w-full divide-y divide-cool-gray-200">
              <thead>
                <tr>
                  <th class="px-6 py-3 bg-cool-gray-50 text-left text-xs leading-4 font-medium text-cool-gray-500 uppercase tracking-wider">
                    Patient Details
                  </th>
                  <%# <th class="hidden px-6 py-3 bg-cool-gray-50 text-left text-xs leading-4 font-medium text-cool-gray-500 uppercase tracking-wider md:block">
 %>
                    <%#   Status %>
                    <%# </th>
 %>
                  <th class="px-6 py-3 bg-cool-gray-50 text-right text-xs leading-4 font-medium text-cool-gray-500 uppercase tracking-wider">
                   File updated
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-cool-gray-200">
                <% if @latest_patients.empty? %>
                  <div class="text-center mt-8">
                    <p class="text-gray-500">No activity in this organisation for the last 2 weeks.</p>
                  </div>
                <% else %>
                  <% @latest_patients.each do |patient|%>
                    <tr class="bg-white">
                      <td class="max-w-0 w-full px-6 py-4 whitespace-no-wrap text-sm leading-5 text-cool-gray-900">
                        <div class="flex">
                          <%= link_to patient_path(patient) ,class: "group inline-flex space-x-2 truncate text-sm leading-5" do %>
                            <svg class="flex-shrink-0 h-5 w-5 text-cool-gray-400 group-hover:text-cool-gray-500 transition ease-in-out duration-150" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"></path><path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"></path></svg>
                            <p class="text-cool-gray-500 truncate group-hover:text-cool-gray-900 transition ease-in-out duration-150">
                              <% if patient.employers.blank? %>
                                <%= "#{patient.name.full}"%>
                              <% else %>
                                <%= "[#{patient.employers.first.name}] #{patient.name.full}"%>
                              <% end %>
                            </p>
                          <% end %>
                        </div>
                      </td>
                      <td class="px-6 py-4 text-right whitespace-no-wrap text-sm leading-5 text-cool-gray-500">
                        <%= patient.updated_at.strftime("%d %B %Y")%>
                      </td>
                    </tr>

                  <% end %>
                <% end %>
                
                <!-- More rows... -->
              </tbody>
            </table>

            <!-- Pagination -->
            <%# <nav class="bg-white px-4 py-3 flex items-center justify-between border-t border-cool-gray-200 sm:px-6">
 %>
              <%#   <div class="hidden sm:block">
 %>
                <%#     <p class="text-sm leading-5 text-cool-gray-700">
 %>
                  <%#       Showing
 %>
                  <%#       <span class="font-medium">1</span>
 %>
                  <%#       to
 %>
                  <%#       <span class="font-medium">10</span>
 %>
                  <%#       of
 %>
                  <%#       <span class="font-medium">20</span>
 %>
                  <%#       results
 %>
                  <%#     </p>
 %>
                <%#   </div>
 %>
              <%# <div class="flex-1 flex justify-between sm:justify-end">
 %>
                <%#   <a href="#" class="relative inline-flex items-center px-4 py-2 border border-cool-gray-300 text-sm leading-5 font-medium rounded-md text-cool-gray-700 bg-white hover:text-cool-gray-500 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 active:bg-cool-gray-100 active:text-cool-gray-700 transition ease-in-out duration-150">
 %>
                  <%#     Previous
 %>
                  <%#   </a>
 %>
                <%#   <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-cool-gray-300 text-sm leading-5 font-medium rounded-md text-cool-gray-700 bg-white hover:text-cool-gray-500 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 active:bg-cool-gray-100 active:text-cool-gray-700 transition ease-in-out duration-150">
 %>
                  <%#     Next
 %>
                  <%#   </a>
 %>
                <%# </div>
 %>
              <%# </nav>
 %>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>
