<%# <h2>Edit <%= resource_name.to_s.humanize %1></h2> %>

<%# <%= simple_form_for(resource, as: resource_name, url: registration_path(resource_name), html: { method: :put }) do |f| %1> %>
<%#   <%= f.error_notification %1> %>

<%#   <div class="form-inputs"> %>
  <%#     <%= f.input :email, required: true, autofocus: true %1> %>

  <%#     <% if devise_mapping.confirmable? && resource.pending_reconfirmation? %1> %>
    <%#       <p>Currently waiting confirmation for: <%= resource.unconfirmed_email %1></p> %>
    <%#     <% end %1> %>

  <%#     <%= f.input :password, %>
  <%#       hint: "leave it blank if you don't want to change it", %>
  <%#       required: false, %>
  <%#       input_html: { autocomplete: "new-password" } %1> %>
  <%#     <%= f.input :password_confirmation, %>
  <%#       required: false, %>
  <%#       input_html: { autocomplete: "new-password" } %1> %>
  <%#     <%= f.input :current_password, %>
  <%#       hint: "we need your current password to confirm your changes", %>
  <%#       required: true, %>
  <%#       input_html: { autocomplete: "current-password" } %1> %>
  <%#   </div> %>

<%#   <div class="form-actions"> %>
  <%#     <%= f.button :submit, "Update" %1> %>
  <%#   </div> %>
<%# <% end %1> %>

<%# <h3>Cancel my account</h3> %>

<%# <p>Unhappy? <%= link_to "Cancel my account", registration_path(resource_name), data: { confirm: "Are you sure?" }, method: :delete %1></p> %>

<%# <%= link_to "Back", :back %1> %>

<nav class="md:w-1/4 md:pr-8">

  <%= render SettingsNavComponent.new() %>

</nav>

<div class="mt-4 md:mt-0 md:w-3/4">
  <div class="mb-4 bg-white shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900">
        Change Password
      </h3>
      <p class="mt-1 max-w-2xl text-sm leading-5 text-gray-500">
        Make sure you choose a secure password. All passwords must be at least 6 characters.
      </p>
      <div class="mt-6 sm:mt-5 sm:border-t sm:border-gray-200 sm:pt-5">
        <%= form_for(resource, as: resource_name, url: registration_path(resource_name)) do |f| %>
          <%= devise_error_messages! %>
          <div class="grid grid-cols-1 row-gap-6 col-gap-4 sm:grid-cols-6">
            <div class="sm:col-span-3">
              <label class="form-control-label" for="current_password">Current password</label>
              <div class="mt-1 rounded-md shadow-sm">
                <%= f.password_field :current_password, autocomplete: "current-password", class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" %>
              </div>
              <p class="mt-2 text-sm text-gray-500">We need your current password to confirm your changes</p>
            </div>
          </div>
          <div class="mt-4 grid grid-cols-1 row-gap-6 col-gap-4 sm:grid-cols-6">
            <div class="sm:col-span-3">
              <div class="form-group">
                <label class="form-control-label" for="password">New password</label>
                <div class="mt-1 rounded-md shadow-sm">
                  <%= f.password_field :password, autocomplete: "new-password", class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" %>
                </div>
              </div>
            </div>
          </div>
          <div class="grid grid-cols-1 row-gap-6 col-gap-4 sm:grid-cols-6">
            <div class="sm:col-span-3">
              <div class="form-group">
                <label class="form-control-label" for="password_confirmation">Confirm new password</label>
                <div class="mt-1 rounded-md shadow-sm">
                  <%= f.password_field :password_confirmation, autocomplete: "new-password", class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5" %>

                </div>
              </div>
            </div>
          </div>
          <div class="mt-4">
            <div class="flex">
              <input type="submit" name="commit" value="Update password" class="inline-flex justify-center py-2 px-4 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-500 focus:outline-none focus:border-indigo-700 focus:shadow-outline-indigo active:bg-indigo-700 transition duration-150 ease-in-out" data-disable-with="Update password" />
            </div>
          </div>
          </form>
      </div>
    </div>
  </div>
</div>
