<%= form_for @patient_note, path: patient_note_path  do |f| %>
  <div class="">
    <div class="mt-6 sm:mt-5">
      <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <label for="about" class="block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
          Note
        </label>
        <divclass="mt-1 sm:mt-0 sm:col-span-2">
        <div class="max-w-lg flex rounded-md shadow-sm">
          <%= f.text_area :note, rows: 3, class: "form-textarea block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5"%>
        </div>
        <p class="mt-2 text-sm text-gray-500">Write a few sentences about the patient, for this note.</p>
      </div>
    </div>

    <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
      <label for="about" class="block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2">
        Attachments
      </label>
      <div class="mt-1 sm:mt-0 sm:col-span-2">
        <div id="uppy-dropzone" class="max-w-lg mb-6 ">
          <div>
            <%= f.file_field :name, as: :file, multiple: true,
              class: "block w-full mb-4 transition duration-150 ease-in-out sm:text-sm sm:leading-5",
              data: {
                upload_result_element: "patient_note_supporting_pdfs",
                uppy: 'patient_note[attachments_attributes]'
              }
            %>
          </div>
        </div>

        <%= f.fields_for :attachments do |pf| %>
          <% render partial: "attachment", locals: { attachment: pf.object, f: pf } %>
        <% end %>

      </div>
    </div>


  </div>
  </div>

  <div class="flex justify-between items-center  mt-8 border-t border-gray-200 pt-5">

    <div class="flex text-sm text-indigo-400">
      <%= link_to "Delete note", patient_note_path( @patient_note ), method: :delete %>
    </div>

    <div class="flex ">
      <span class="inline-flex rounded-md shadow-sm">
        <%= link_to "Cancel", :back,
          class: "py-2 px-4 border border-gray-300 rounded-md text-sm leading-5 font-medium
                   text-gray-700 hover:text-gray-500 focus:outline-none focus:border-blue-300
                   focus:shadow-outline-blue active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out" %>
      </span>
      <span class="ml-3 inline-flex rounded-md shadow-sm">
        <%= f.submit  "Save", class:"inline-flex justify-center py-2 px-4 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-500 focus:outline-none focus:border-indigo-700 focus:shadow-outline-indigo active:bg-indigo-700 transition duration-150 ease-in-out" %>
      </span>
    </div>
  </div>
<% end %>

