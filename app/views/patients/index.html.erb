
<% content_for :head do %>
  <header class="bg-gray-800 shadow-sm">
    <div class="max-w-7xl mx-auto py-4 sm:px-6 lg:px-8">
      <h1 class="leading-6 font-semibold text-white text-sm">
        <%= link_to "Find a patient ", organisation_patients_path(params[:organisation_id]), class: "font-normal text-gray-300"%>
      </h1>
    </div>
  </header>
<% end %>

<div data-controller="patient-search" data-reflex-root="#morph" class="w-full">

  <div class="box py-6">
    <div class="box-wrapper flex justify-between items-center">
      <div class=" bg-white rounded flex items-center w-3/5 p-3 shadow-sm border border-gray-200">
        <svg class=" w-5 text-gray-600 h-5 "
             fill="none" stroke-linecap="round" stroke-linejoin="round"
                                                stroke-width="2" stroke="currentColor" viewBox="0 0 24 24">
          <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
        <input type="search"
               id="patient_search"
               placeholder="Search for a Patient by Name or ID"
               data-target="patient-search.query"
               data-action="debounced:input->patient-search#perform"
               class="w-full pl-4 text-sm outline-none focus:outline-none bg-transparent">

      </div>

      <div class=" rounded-md shadow-sm">
        <%= link_to "New patient",  new_organisation_patient_path(params[:organisation_id]) , class:"inline-flex items-center px-4 py-2 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-500 focus:outline-none focus:border-indigo-700 focus:shadow-outline-indigo active:bg-indigo-700 transition ease-in-out duration-150" %>
      </div>

    </div>
  </div>

  <span data-target="patient-search.activity" class="text-danger flex items-center hidden" >
    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    Searching for patients...
  </span>


  <div class="flex flex-col <%= "hidden" unless @data.present? %>" data-target="patient-search.list" >
    <div class="-my-2 py-2 overflow-x-auto sm:-mx-6 sm:px-6 lg:-mx-8 lg:px-8">
      <div class="align-middle inline-block min-w-full shadow overflow-hidden sm:rounded-lg border-b border-gray-200">
        <table class="min-w-full">
          <thead>
            <tr>
              <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                ID No. or Passport
              </th>
              <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                Employer(s) 
              </th>
              <th class="px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs leading-4 font-medium text-gray-500 uppercase tracking-wider">
                Contact number
              </th>

              <th class="px-6 py-3 border-b border-gray-200 bg-gray-50"></th>
            </tr>
          </thead>
          <tbody>
            <% if @data.present? %>
              <% @data.each do |patient| %>
                <tr class="bg-white even:bg-gray-50">
                  <td class="px-6 py-4 whitespace-no-wrap text-sm leading-5 font-medium text-gray-900">
                    <%= patient.name.full %>
                  </td>
                  <td class="px-6 py-4 whitespace-no-wrap text-sm leading-5 text-gray-500">
                    <%= patient.identification_number %>

                  </td>
                  <td class="px-6 py-4 whitespace-no-wrap text-sm leading-5 text-gray-500">
                    <% patient.employers.each do |employer|%> <%= "#{ employer.name } | "%> <% end %> 
                  </td>
                  <td class="px-6 py-4 whitespace-no-wrap text-sm leading-5 text-gray-500">
                    <%= patient.phone_number %>
                  </td>
                  <td class="px-6 py-4 whitespace-no-wrap text-right text-sm leading-5 font-medium">
                    <% if params[:from] == "create-cof" %>
                      <%= link_to "Select", new_patient_evaluation_path(patient.id), class:"text-indigo-600 hover:text-indigo-900 focus:outline-none focus:underline"%>
                    <% else %>
                      <%= link_to "Select", patient_path( patient.id ), class:"text-indigo-600 hover:text-indigo-900 focus:outline-none focus:underline"%>
                    <% end %>
                  </td>
                </tr>
              <% end %>
            <% end %>

          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

