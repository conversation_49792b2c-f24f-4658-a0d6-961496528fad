<% content_for :head do %>
  <header class="bg-gray-800 shadow-sm">
    <div class="max-w-7xl mx-auto py-4 sm:px-6 lg:px-8">
      <h1 class="leading-6 font-semibold text-white text-sm">
        Add a new patient
      </h1>
    </div>
  </header>
<% end %>

<%= form_with model: @patient, url: organisation_patients_path, local: true do |f| %>
  <div class="">

    <% if @patient.errors.any? %>
      <div id="error_explanation " class='bg-red-50 p-8 text-red-900'>
        <h2 class="font-semibold"><%= pluralize(@patient.errors.count, "error") %> prohibited this post from being saved:</h2>

        <ul>
          <% @patient.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <div class="mt-6 sm:mt-5">
      <div class="sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:pt-5">
        <%= f.label :identification_number, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-sm rounded-md shadow-sm">
            <%= f.text_field :identification_number, class: "max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
      </div>

      <div class="mt-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <%= f.label :first_name, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-xs rounded-md shadow-sm">
            <%= f.text_field :first_name, class: "max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
      </div>

      <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <%= f.label :last_name, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-xs rounded-md shadow-sm">
            <%= f.text_field :last_name, class: "max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
      </div>

      <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <%= f.label "Date of Birth" , class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-sm rounded-md shadow-sm">
            <div class="max-w-lg mt-1 relative rounded-md shadow-sm">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none ">
                <%= inline_svg_tag( "calendar.svg", class: "fill-current h-4 w-4 text-gray-500" ) %>
              </div>
              <%= f.text_field :dob, as: :string,
                data:
                {
                  controller: "flatpickr",
                  flatpickr_alt_input: true,
                  flatpickr_alt_format: "l, d M Y",
                  flatpickr_date_format: "Y-m-d",
                  flatpickr_min_date: Time.zone.now - 90.years,
                  flatpickr_max_date: Time.zone.now ,
                  flatpickr_default_date: Time.zone.now - 1.day
                },
                class: "pl-10 max-w-xs max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md text-gray-500" %>
            </div>

          </div>
        </div>
      </div>

      <div class="mt-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <%= f.label :gender, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-xs rounded-md shadow-sm">

            <%= f.select :gender,
              ENUMS::GENDER.keys.map {|type| [type.titleize, type]},
              {include_blank: "Select gender"},
              {class: "text-gray-500 max-w-xs max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md"} %>

          </div>
        </div>
      </div>

      <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <%= f.label :email, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-lg rounded-md shadow-sm">
            <%= f.text_field :email, class: "max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
      </div>

      <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
        <%= f.label :phone_number, class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
        <div class="mt-1 sm:mt-0 sm:col-span-2">
          <div class="max-w-xs rounded-md shadow-sm">
            <%= f.text_field :phone_number, class: "max-w-lg block w-full shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:max-w-xs sm:text-sm border-gray-300 rounded-md" %>
          </div>
        </div>
      </div>

    </div>
  </div>

  <div class="mt-8 border-t border-gray-200 pt-5">    <div class="flex justify-end">
      <span class="inline-flex rounded-md shadow-sm">
        <%= link_to "Cancel", :back,
          class: "py-2 px-4 border border-gray-300 rounded-md text-sm leading-5 font-medium
                   text-gray-700 hover:text-gray-500 focus:outline-none focus:border-blue-300
                   focus:shadow-outline-blue active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out" %>
      </span>
      <span class="ml-3 inline-flex rounded-md shadow-sm">
        <%= f.submit  "Save", class:"inline-flex justify-center py-2 px-4 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-500 focus:outline-none focus:border-indigo-700 focus:shadow-outline-indigo active:bg-indigo-700 transition duration-150 ease-in-out" %>
      </span>
    </div>
  </div>
<% end %>
