<% content_for :head do %>
  <%= render PatientInformationComponent.new patient: @patient %>
<% end %>

<div class="grid w-full gap-8 lg:grid-cols-12">
  <div class="lg:col-span-9">
    <div class="mt-10">
      <div class="grid grid-cols-1 row-gap-6 col-gap-2 mt-4 sm:grid-cols-2">
        <dt class="text-sm font-medium leading-5 text-gray-500">
          Recent Medical Certificates
        </dt>
        <span class="inline-flex justify-end rounded-md">
          <%= link_to "Add Medical Certificate ", new_patient_evaluation_path(@patient), class: "inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs leading-4 font-medium rounded text-gray-200 bg-gray-800 hover:text-gray-100 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:text-gray-800 active:bg-gray-50 transition ease-in-out duration-150" %>
        </span>

        <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
          <% if @medicals.empty? %>
            <div class="mt-8 text-center">
              <p class="text-gray-500">No Medical Certificates have been done yet.</p>
            </div>
          <% else %>
            <div class="mt-4 overflow-hidden bg-white shadow sm:rounded-md">
              <ul>
                <% @medicals.each do |medical| %>
                  <li class="border-t border-gray-200 first:border-gray-100">
                    <%= link_to evaluation_path( medical ), class: "block hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition duration-150 ease-in-out" do %>
                      <div class="flex items-center px-4 py-4 sm:px-6">
                        <div class="flex items-center flex-1 min-w-0">
                          <div class="flex-1 min-w-0 px-4 md:grid md:grid-cols-2 md:gap-4">
                            <div>
                              <div class="text-sm font-medium leading-5 text-indigo-600 truncate"><%= medical.name %></div>
                              <div class="flex items-center mt-2 text-sm leading-5 text-gray-500">
                                <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                  <path fill-rule="evenodd" d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884zM18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" clip-rule="evenodd" />
                                </svg>
                                <span class="truncate">Valid until <%= medical.medical_expiry_date.strftime("%d %B %Y") %></span>
                              </div>
                            </div>
                            <div class="hidden md:block">
                              <div>
                                <div class="text-sm leading-5 text-gray-900">
                                  Created on
                                  <time datetime="2020-01-07"><%= medical.created_at.strftime("%d %B %Y") %></time>
                                </div>

                                <div class="flex items-center mt-2 text-sm leading-5 text-gray-500">
                                  <% if medical.evaluation_signoff %>
                                    <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                    Signed on <%= medical.evaluation_signoff.created_at.strftime("%d %B %Y") %>
                                <% else %>
                                    --- Not yet signed off ---
                                  <% end %>

                                </div>

                              </div>
                            </div>
                          </div>                      </div>
                          <div>
                            <svg clas="h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fil-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                          </div>
                      </div>
                    <% end %>
                  </li>
                <% end %>
              </ul>
            </div>
          <% end %>
        </dd>
      </div>
    </div>

    <div class="mt-10">
      <div class="grid grid-cols-1 row-gap-6 col-gap-2 mt-4 sm:grid-cols-2">
        <dt class="text-sm font-medium leading-5 text-gray-500">
          Employment History
        </dt>
        <span class="inline-flex justify-end rounded-md ">
          <%= link_to "Add Employment ", new_patient_employment_path(@patient), class: "inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs leading-4 font-medium rounded text-gray-200 bg-gray-800 hover:text-gray-100 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:text-gray-800 active:bg-gray-50 transition ease-in-out duration-150" %>
        </span>

        <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
          <% if @patient.employments.empty? %>
            <div class="mt-8 text-center">
              <p class="text-gray-500">No employment recorded.</p>
            </div>
          <% else %>
            <div class="mt-4 overflow-hidden bg-white shadow sm:rounded-md">
              <ul>
                <% @patient.employments.each do|employment| %>
                  <li class="border-t border-gray-200  first:border-gray-100">
                    <%= link_to edit_employment_path( employment ),
                      class: "block hover:bg-gray-50 focus:outline-none focus:bg-gray-50 transition duration-150 ease-in-out" do %>
                      <div class="flex items-center px-4 py-4 sm:px-6">
                        <div class="flex items-center min-w-0 flex-">
                          <div class="min-w-0 px-4 flex1 md:grid md:grid-cols-2 md:gap-4">
                            <div>
                              <div class="text-sm font-medium text-indigo-600 truncate leding-5"><%= employment.company.name %> &middot <%= employment.position %></div>
                              <div class="flex mt-2 text-sm leading-5 text-gray-500 iems-center">
                                <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                  <path fill-rule="evendd" d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884zM18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" clip-rule="evenodd" />
                                </svg>
                                <span class="truncate">Inducted on <%# employment.induction_date.strftime("%d %B %Y") %></span>
                              </div>
                            </div>
                            <div class="hidden md:block">
                              <div>
                                <div class="text-sm leading-5 ext-gray-900">
                                  <% if employment.termination_date.present? %>
                                    Terminated on
                                    <time datetime=<%= "#{ employment.termination_date }" %>><%# employment.termination_date.strftime("%d %B %Y") %></time>
                                  <% end %>
                                </div>
                                <div class="flex mt-2 text-sm leading-5 text-gray-500 items-cente">
                                  <% if employment.active? %>
                                    <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                      <path fill-rule="evenodd" d="10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                    Employment active

                                  <% else %>
                                    <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-red-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>
                                    <span class="truncate">
                                      Terminated due to: <%= employment.termination_reason %>
                                    </span>

                                  <% end %>

                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <svg class="w-5 h-5 ext-gray-400" fill="currentClor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                          </svg>
                        </div>
                      </div>
                    <% end %>
                  </li>
                <% end %>
              </ul>
            </div>

          <% end %>
        </dd>
      </div>
    </div>

    <div class="mt-10">

      <div class="grid row-gap-6 col-gap-2 mt-4 grd-cols-1 sm:grid-cols-2">

        <dt class="flex justify-between text-sm font-medium leading-5 text-gray-500">
          Medical History
        </dt>
        <span class="inline-flex justify-end rounded-md ">
          <%= link_to "Add Medical Information ", patient_assessments_path(@patient), class: "inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs leading-4 font-medium rounded text-gray-200 bg-gray-800 hover:text-gray-100 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:text-gray-800 active:bg-gray-50 transition ease-in-out duration-150" %>
        </span>
        <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">

          <ul>
            <% if @notes.empty? %>
              <div class="m-8 text-center">
                <p class="text-gray-500">No recorded activity yet.</p>
              </div>
            <% else %>
              <div class="relative ">
                <div class="absolute top-0 z-0 h-full border-r-2 border-gray-200 border-solid" style="left: 19px"></div>
                <ul class="p-0 m-0 list-none">
                  <% @notes.each do |note| %>
                    <% if note.class.to_s == 'PatientNote' %>
                      <%= render PatientNoteTimelineComponent.new(note: note) %>
                    <% elsif note.class.to_s == 'Audio' %>
                      <%= render AudioTimelineComponent.new(audio: note) %>
                    <% elsif note.class.to_s == 'LabTest' %>
                      <%= render LabTestTimelineComponent.new(labs: note) %>
                    <% elsif note.class.to_s == ('VisionScreeningUpload') %>
                      <%= render VisionScreeningUploadTimelineComponent.new(data: note) %>
                    <% elsif note.class.to_s == ('VisionScreening') %>
                      <%= render VisionScreeningTimelineComponent.new(data: note) %>
                    <% elsif note.class.to_s == ('Exclusion') %>
                      <%= render ExclusionTimelineComponent.new(data: note) %>
                    <% elsif note.class.to_s == ('Referral') %>
                      <%= render ReferralTimelineComponent.new(data: note) %>
                    <% end %>
                  <% end %>
                </ul>
              <% end %>
              </div>
          </ul>

        </dd>

      </div>
    </div>
  </div>
  <div class="mt-4 lg:mt-0 lg:col-span-3">
    <%# <div class="p-8 my-8 text-sm text-gray-800 bg-gray-200 rounded-lg">%>
      <%#   <h6 class="text-xs font-bold text-gray-800 uppercase">Need to add more information</h6>%>
      <%#    <p class="mt-2 text-gray-600">For example: refferals, exclusions and client required forms</p>%>
      <%#   <p class="mt-4">%>
      <%#     <span class="inline-flex rounded-md shadow-sm">%>
        <%# link_to "Add supplementary docs", patient_supplementary_docs_path(@patient), class: "inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs leading-4 font-medium rounded text-gray-700 bg-white hover:text-gray-500 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:text-gray-800 active:bg-gray-50 transition ease-in-out duration-150"%>
        <%#              </span>%>
      <%#          </p>%>
      <%#       <span class="inline-flex mt-4 rounded-md shadow-sm">%>
        <%# link_to "Add Medical Certificate ", new_patient_evaluation_path(@patient), class: "inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs leading-4 font-medium rounded text-gray-200 bg-gray-800 hover:text-gray-100 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:text-gray-800 active:bg-gray-50 transition ease-in-out duration-150"%>
        <%#          </span>%>
      <%#       </p>%>
      <%#    </div>%>
  </div>
</div>
