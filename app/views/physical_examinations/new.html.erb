<% content_for :head do %>
  <header class="bg-gray-800 shadow-sm">
    <div class="max-w-7xl mx-auto py-4 sm:px-6 lg:px-8">
      <h1 class="leading-6 font-semibold text-white text-sm">
        Add a new physical examination
      </h1>
    </div>
  </header>
<% end %>

<%= form_with scope: :exam, url: patient_physical_examinations_path do |f| %>
  <div class="flex-1 flex flex-col">
    <main class="flex-1 overflow-y-auto focus:outline-none" tabindex="0">
      <div class="relative max-w-4xl mx-auto md:px-8 xl:px-0">
        <!-- Description list with inline editing -->
        <div class="mt-10 space-y-6 divide-y divide-gray-200">
          <div class="space-y-1">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Profile
            </h3>
            <p class="max-w-2xl text-sm leading-5 text-gray-500">
            </p>
          </div>
          <div>
            <dl class="divide-y divide-gray-200">

              <div class="py-4 space-y-1 sm:py-5 items-center sm:grid sm:grid-cols-3 sm:gap-6">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Height (cm)
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-1">
                <%= f.text_field :height, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 items-center sm:grid sm:grid-cols-3 sm:gap-4">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Weight (kg)
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-1">
                <%= f.text_field :weight, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 items-center sm:grid sm:grid-cols-3 sm:gap-4">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Blood Pressure
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-1">
                <%= f.text_field :blood_pressure, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 items-center sm:grid sm:grid-cols-3 sm:gap-4">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Pulse
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-1">
                <%= f.text_field :pulse, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 items-center sm:grid sm:grid-cols-3 sm:gap-4">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Blood Sugar
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-1">
                <%= f.text_field :blood_sugar, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 items-center sm:grid sm:grid-cols-3 sm:gap-4">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Gender
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-1">
                <label class="block">
                  <%= f.select :gender,
                    { Male: "Male", Female: "Female", Other: "Other" },
                    { include_blank: true },
                    class: "form-select block w-full mt-1 transition duration-150 ease-in-out sm:text-sm sm:leading-5" %>
                </label>

                </dd>
              </div>

            </dl>
          </div>
        </div>
        
        <div class="mt-10 space-y-6 divide-y divide-gray-200">
          <div class="space-y-1">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Tests
            </h3>
            <p class="max-w-2xl text-sm leading-5 text-gray-500">
            </p>
          </div>
          <div>
            <dl class="divide-y divide-gray-200">

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Urine
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :urine, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                NAD
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :nad, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Leucocytes
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :leucocytes, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Nitrite
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :nitrite, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Blood
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :blood, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Protein
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :protein, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Glucose
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :glucose, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5  sm:grid sm:grid-cols-3 sm:gap-4">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                General Appearance and Exam
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.text_area :appearance_and_exam, rows:3, class: "form-textarea block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                </dd>
              </div>
            </dl>
          </div>
        </div>

        <div class="mt-10 space-y-6 divide-y divide-gray-200">
          <div class="space-y-1">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Systemic Exam
            </h3>
            <p class="max-w-2xl text-sm leading-5 text-gray-500">
            </p>
          </div>
          <div>
            <dl class="divide-y divide-gray-200">

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Peripheral Signs
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :peripheral_signs, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Skin and Appendages
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :skin_or_appendages, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                ENT
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :ent, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                CVS
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :cvs, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Chest
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :chest, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Gastro Intestinal System
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :gastro_intestinal_system, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Urinary System
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :urinary_system, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Musculo skeletal system
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :musculo_skeletal_system, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                CNS
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :cns, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Endocrine System
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :endocrine_system, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

            </dl>
          </div>
        </div>

        <div class="mt-10 space-y-6 divide-y divide-gray-200">
          <div class="space-y-1">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Chronic Diseases
            </h3>
            <p class="max-w-2xl text-sm leading-5 text-gray-500">
            </p>
          </div>
          <div>
            <dl class="divide-y divide-gray-200">

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Hypertension
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :chronic_hypertension, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Asthma
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :chronic_asthma, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Drug/Achohol
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :chronic_drug_alcohol, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Thyroid
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :chronic_thyroid, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Epilepsy
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :chronic_epilepsy, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                COPD
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :chronic_copd, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Mental
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :chronic_mental, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Cardiac
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :chronic_cardiac, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Obesity
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :chronic_obesity, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Prosthesis
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :chronic_prosthesis, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Diabetes
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :chronic_diabetes, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Arthritis
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :chronic_arthritis, class: "text-indigo-600 form-checkbox h-6 w-6" %>
                </dd>
              </div>
            </dl>
          </div>
        </div>

        <div class="mt-10 space-y-6 divide-y divide-gray-200">
          <div class="space-y-1">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Commentary
            </h3>
            <p class="max-w-2xl text-sm leading-5 text-gray-500">
            </p>
          </div>
          <div>
            <dl class="divide-y divide-gray-200">

              <div class="py-4 space-y-1 sm:py-5 items-center sm:grid sm:grid-cols-3 sm:gap-4">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Chest X Ray
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.text_area :chest_x_ray_comment, rows: 3, class: "form-textarea block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5  sm:grid sm:grid-cols-3 sm:gap-4">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Description of abnormalities
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.text_area :abnormalities, rows: 3, class: "form-textarea block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5  sm:grid sm:grid-cols-3 sm:gap-4">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Occupational Disorders Identified
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.text_area :occupational_disorders,
                  rows: 3 ,
                  class: "form-textarea block w-full
                                                                transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 items-center sm:grid sm:grid-cols-3 sm:gap-4">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Performed by
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-1">
                <%= f.text_field :performed_by, value: current_user.name.full, class: "form-input block w-full
                                                       transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                </dd>
              </div>


              <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
                <%= f.label "Medical Examination Date" , class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
                <div class="mt-1 sm:mt-0 sm:col-span-2">
                  <div class="max-w-sm rounded-md shadow-sm">
                    <div class="max-w-lg mt-1 relative rounded-md shadow-sm">
                      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none ">
                        <%# inline_svg( "images/calendar.svg", class: "fill-current h-4 w-4 text-gray-500" ) %>
                      </div>
                      <%= f.text_field :examination_date, as: :string,
                        data:
                        {
                          controller: "flatpickr",
                          flatpickr_alt_input: true,
                          flatpickr_alt_format: "l, d M Y",
                          flatpickr_date_format: "Y-m-d",
                          flatpickr_min_date: Time.zone.now - 12.month,
                          flatpickr_max_date: Time.zone.now ,
                          flatpickr_default_date: Time.zone.now - 1.day
                        },
                        class: "max-w-xs form-input block w-full pl-10 sm:text-sm sm:leading-5 text-gray-500"
                      %>
                    </div>

                  </div>
                </div>
              </div>



            </dl>
          </div>
        </div>

        <div class="mt-8 border-t border-gray-200 pt-5">    <div class="flex justify-end">
            <span class="inline-flex rounded-md shadow-sm">
              <%= link_to "Cancel", :back,
                class: "py-2 px-4 border border-gray-300 rounded-md text-sm leading-5 font-medium
                         text-gray-700 hover:text-gray-500 focus:outline-none focus:border-blue-300
                         focus:shadow-outline-blue active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out" %>
            </span>
            <span class="ml-3 inline-flex rounded-md shadow-sm">
              <%= f.submit  "Save", class:"inline-flex justify-center py-2 px-4 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-500 focus:outline-none focus:border-indigo-700 focus:shadow-outline-indigo active:bg-indigo-700 transition duration-150 ease-in-out" %>
            </span>
          </div>
        </div>
      </div>
  </div>
    </main>
    </div>


  <% end %>
