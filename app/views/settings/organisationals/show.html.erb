<nav class="md:w-1/4 md:pr-8">
  <%= render "shared/settings_nav" %>
</nav>

<div class="mt-4 md:mt-0 md:w-3/4">

  <div class="mb-4 bg-white shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <div>
        <div>
          <div class="flex border-b border-gray-200">
            <div class="flex-1 mb-6 sm:mb-5">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                Organisation
              </h3>
              <p class="mt-1 max-w-2xl text-sm leading-5 text-gray-500">
              Update your organisation settings.
              </p>
            </div>
            <div>
              <%= link_to "Add Organisation",
                new_organisation_path,
                class: "inline-flex items-center px-4 py-2 border border-transparent
                    text-sm leading-5 font-medium rounded-md text-white bg-indigo-600
                    hover:bg-indigo-500 focus:outline-none focus:shadow-outline-indigo
                    focus:border-indigo-700 active:bg-indigo-700 transition duration-150
                    ease-in-out" %>
            </div>
          </div>
          <div class="w-full">

            <% @organisations.each do |organisation| %>
              <div class="mt-5">
                <div class="rounded-md bg-gray-50 px-6 py-5 sm:flex sm:items-start sm:justify-between">
                  <div class="sm:flex sm:items-start">
                    <div class="mt-3 sm:mt-0 sm:ml-4">
                      <div class="font-medium text-sm leading-5 text-gray-50 bg-gray-600 uppercase rounded px-2 py-1 inline">
                        <%= organisation.organisation_name %>
                      </div>
                      <div class="mt-2 text-sm leading-5 text-gray-500 sm:flex sm:items-center">
                        <div>
                          Users: <%= organisation.users.size %> &middot
                          Teams: <%= organisation.teams.count %> &middot
                          Clinics: <%= organisation.clinics.count %> &middot
                          Patients: <%= organisation.patients.size %> &middot
                          Companies: <%= organisation.companies.count %>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="mt-4 sm:mt-0 sm:ml-6 sm:flex-shrink-0">

                    <span class="relative z-0 inline-flex shadow-sm rounded-md">

                      <%= link_to "Edit organisation",
                        edit_organisation_path( organisation ),
                        class: "relative inline-flex items-center px-4 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500" %>

                      <%# <button type="button" class="relative inline-flex items-center px-4 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"> %>
                        <%#   Save changes %>
                        <%# </button> %>

                      <span x-data="{ open: false }" class="-ml-px relative block">
                        <button @click="open = !open" @click.away="open = false" id="option-menu" type="button" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-10 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                          <span class="sr-only">Open options</span>
                          <svg class="h-5 w-5" x-description="Heroicon name: chevron-down" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                          </svg>
                        </button>
                        <transition enter-active-class="transition ease-out duration-100" enter-class="transform opacity-0 scale-95" enter-to-class="transform opacity-100 scale-100" leave-active-class="transition ease-in duration-75" leave-class="transform opacity-100 scale-100" leave-to-class="transform opacity-0 scale-95"><div x-show="open" x-description="Dropdown panel, show/hide based on dropdown state." class="origin-top-right absolute right-0 mt-2 -mr-1 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5" style="display: none;">
                          <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="option-menu">
                            <%= link_to "Manage clinics", organisation_clinics_path( organisation ) ,class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900" %>
                            <%# <%= link_to "Manage users", "#", class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900" %1> %>
                            <%# <%= link_to "Manage teams", "#", class: "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900" %1> %>
                          </div>
                        </transition>
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
