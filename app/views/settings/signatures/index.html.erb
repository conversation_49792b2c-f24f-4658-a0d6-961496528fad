<nav class="md:w-1/4 md:pr-8">
  <%= render "shared/settings_nav" %>
</nav>

<div class="mt-4 md:mt-0 md:w-3/4">
  <div class="mb-4 bg-white shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <div>
        <div>
          <div class="flex border-b border-gray-200">
            <div class="flex-1 mb-6 sm:mb-5">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                Signatures
              </h3>
              <p class="mt-1 max-w-2xl text-sm leading-5 text-gray-500">
                Update your signitures that will appear on signed off documents.
              </p>
            </div>
            <div>
              <%= link_to "Add Signature",
                new_settings_signature_path,
                class: "inline-flex items-center px-4 py-2 border border-transparent
                    text-sm leading-5 font-medium rounded-md text-white bg-indigo-600
                    hover:bg-indigo-500 focus:outline-none focus:shadow-outline-indigo
                    focus:border-indigo-700 active:bg-indigo-700 transition duration-150
                    ease-in-out" %>
            </div>
          </div>
          <div class="w-full">
            <% if @signatures.blank? %>
              <div class="text-gray-500 p-12 flex justify-center"> -- No recorded signatures yet --</div>
            <% else %>
            <% @signatures.each do |signature| %>
              <section class="text-gray-700 ">
                <div class="container px-5 py-2 mx-auto">
                  <div class="mx-auto">
                    <div class="p-4">
                      <div class="h-full bg-gray-100 px-8 pt-8 pb-24 rounded-lg overflow-hidden text-center relative">
                        <div class="bg-white rounded-lg p-8 text-center">
                          <div class="flex justify-center">
                            <%= image_tag ( signature.image) %> 
                          </div>
                          <div class=" border-t text-center ">
                            <%= signature.first_line %>
                          </div>
                          <div class=" text-center ">
                            <%= signature.second_line %>
                          </div>
                          <div class=" text-center ">
                            <%= signature.third_line %>
                          </div>
                          <div class=" text-center ">
                            <%= signature.forth_line %>
                          </div>
                        </div>

                        <div class=" text-center mt-2 leading-none flex justify-center absolute bottom-0 left-0 w-full py-4 ">
                          <%= link_to edit_settings_signature_path( signature ) do %>
                            <span class=" hover:text-gray-900 text-gray-600 mr-3 inline-flex items-center leading-none text-sm pr-3 py-1 border-r-2 border-gray-300 ">
                              <svg fill=" none " viewBox=" 0 0 24 24 " stroke=" currentColor " class=" cog w-6 h-6 "><path stroke-linecap=" round " stroke-linejoin=" round " stroke-width=" 2 " d=" M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z "></path><path stroke-linecap=" round " stroke-linejoin=" round " stroke-width=" 2 " d=" M15 12a3 3 0 11-6 0 3 3 0 016 0z "></path></svg>
                            </span>
                          <% end %>

                          <%= button_to settings_signature_path(signature ), method: :delete do %>
                          <span class=" hover:text-gray-900 text-gray-600 inline-flex items-center leading-none text-sm ">
                            <svg fill=" none " viewBox=" 0 0 24 24 " stroke=" currentColor " class=" trash w-6 h-6 mr-1 ">
                              <path stroke-linecap=" round " stroke-linejoin=" round " stroke-width=" 2 "
                                                                                   d=" M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16 ">
                              </path>
                            </svg>
                          </span>
                          <% end %>

                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>
            <% end %>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
