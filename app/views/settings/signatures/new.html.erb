<div class="mx-auto mt-4 md:mt-0 md:w-3/4">
  <div class="mb-4 bg-white shadow sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <div>
        <div>
          <div class="flex border-b border-gray-200">
            <div class="flex-1 mb-6 sm:mb-5">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                Add Signature
              </h3>
              <p class="mt-1 max-w-2xl text-sm leading-5 text-gray-500">
                Sign in the block below and fill in the relevant fields
              </p>
            </div>
            <div>
                <%= link_to "Cancel",
                  :back,
                  class: "inline-flex items-center px-4 py-2 border
                        border-transparent text-sm leading-5 font-medium
                        rounded-md text-gray-700 hover:bg-gray-50
                        focus:outline-none focus:shadow-outline-indigo
                        focus:border-indigo-700 active:bg-indigo-700
                        transition duration-150 ease-in-out" %>

                    <%= button_tag "Save", form: "form_create",
                      id: "save_button",
                      class: "inline-flex items-center px-4 py-2 border
                        border-transparent text-sm leading-5 font-medium
                        rounded-md text-white bg-indigo-600
                        hover:bg-indigo-500 focus:outline-none
                        focus:shadow-outline-indigo focus:border-indigo-700
                        active:bg-indigo-700 transition duration-150
                        ease-in-out" %>
            </div>
          </div>
          <div class="w-full">
            <section class="text-gray-700 ">
              <div class="container px-5 py-2 mx-auto">
                <div class="mx-auto">
                  <div class="p-4">
                    <div class="h-full bg-gray-100 px-8 pt-8 pb-8 rounded-lg overflow-hidden text-center relative">
                      <div data-controller='signature' class="bg-white rounded-lg p-8 text-center">
                        <% if @signature.errors.any? %>
                          <div id="error_explanation mb-4 text-red-500">
                            <h2>
                              <%= pluralize(@signature.errors.count, "error") %>
                              prohibited this post from being saved:
                            </h2>

                            <ul>
                              <% @signature.errors.full_messages.each do |msg| %>
                                <li class='text-red-500'><%= msg %></li>
                              <% end %>
                            </ul>
                          </div>
                        <% end %>
                        <div class="wrapper mx-auto flex justify-center">
                          <canvas id="signature-pad"
                                  data-signature-target="canvas"
                                  class="signature-pad border border-gray-500"
                                  width=600
                                  height=300>
                          </canvas>
                        </div>
                        <div>
                          <button data-action="signature#clear">Clear |</button>
                          <button data-action="signature#save">Save</button>
                        </div>

                        <div class=" flex flex-col items-center" data-signature-target="previewContainer">
                          <img class="align-center border border-gray-700 bg-gray-100 w-3/4 h-64 mt-6" data-signature-target="previewImage" />
                          <div class="text-gray-500">
                            Preview of saved signature
                          </div>
                        </div>
                        <%= form_with model: @signature, url: settings_signatures_path, local: true, id: "form_create" do |f| %>

                        <div class="w-full">
                          <div class="w-full mt-16 px-3 flex justify-center ">
                            <%= f.text_field :first_line,
                              class: "text-center flex-col max-w-md appearance-none block w-full bg-gray-200
                            text-gray-700 border border-gray-200 rounded py-3 px-4 mb-3 leading-tight
                            focus:outline-none focus:bg-white focus:border-gray-500",
                            placeholder: "First Line" %>

                          </div>
                          <div class="w-full mt-2 px-3 flex justify-center ">
                            <%= f.text_field :second_line,
                              class: "text-center flex-col max-w-md appearance-none block w-full bg-gray-200
                            text-gray-700 border border-gray-200 rounded py-3 px-4 mb-3 leading-tight
                            focus:outline-none focus:bg-white focus:border-gray-500",
                            placeholder: "Second Line" %>

                          </div>
                          <div class="w-full mt-2 px-3 flex justify-center ">
                            <%= f.text_field :third_line,
                              class: "text-center flex-col max-w-md appearance-none block w-full bg-gray-200
                            text-gray-700 border border-gray-200 rounded py-3 px-4 mb-3 leading-tight
                            focus:outline-none focus:bg-white focus:border-gray-500",
                            placeholder: "Third Line" %>
                          </div>
                          <div class="w-full mt-2 px-3 flex justify-center ">
                            <%= f.text_field :forth_line,
                              class: "text-center flex-col max-w-md appearance-none block w-full bg-gray-200
                            text-gray-700 border border-gray-200 rounded py-3 px-4 mb-3 leading-tight
                            focus:outline-none focus:bg-white focus:border-gray-500",
                            placeholder: "Forth Line" %>
                          </div>
                          <%= f.hidden_field :image_data_uri, data: {"signature-target": "output"} %>
                        </div>

                      </div>
                    <% end %>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
