<% content_for :head do %>
  <header class="bg-gray-800 shadow-sm">
    <div class="max-w-7xl mx-auto py-4 sm:px-6 lg:px-8">
      <h1 class="leading-6 font-semibold text-white text-sm">
        <%= link_to "#{ @vision_screening.patient.name.full} ( ID: #{ @vision_screening.patient.identification_number })", patient_path( @vision_screening.patient ), class: "font-normal text-gray-300" %>
        | Display Vision Screening results
      </h1>
    </div>
  </header>
<% end %>

<div class="w-full flex ">
  <div class="w-full pb-5 space-y-3 flex-col sm:items-center sm:justify-between sm:space-y-0">
    <div class="justify-between flex mb-6">
      <h3 class="text-lg leading-6 font-mediumtext-gray-900">

      </h3>
      <div class="flex space-x-3">
        <span class="shadow-sm rounded-md">
          <%= button_to "Go Back",
            vision_screening_path( @vision_screening ),
            method: :get,
            class: "inline-flex items-center px-4 py-2 border border-gray-300 text-sm
                      leading-5 font-medium rounded-md text-gray-700 bg-white
                      hover:text-gray-500 focus:outline-none focus:shadow-outline-blue
                      focus:border-blue-300 active:text-gray-800 active:bg-gray-50
                      transition duration-150 ease-in-out" %>
        </span>
        <span class="shadow-sm rounded-md">
          <%= button_to "Sign Off",
            vision_screening_sign_off_path,
            method: :post,
            class: "inline-flex items-center px-4 py-2 border border-transparent
                      text-sm leading-5 font-medium rounded-md text-white bg-indigo-600
                      hover:bg-indigo-500 focus:outline-none focus:shadow-outline-indigo
                      focus:border-indigo-700 active:bg-indigo-700 transition duration-150
                      ease-in-out" %>
        </span>
      </div>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
      <div class="px-4 py-5 border-b border-gray-200 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
          Vision Screening Results
          <% unless @vision_screening.status.blank? %>
            <span class="px-2 py-1 ml-2 bg-gray-200 rounded-full text-sm"><%= @vision_screening.status.capitalize %></span>
          <% end %>

        </h3>
        <p class="mt-1 max-w-2xl text-sm leading-5 text-gray-500">
          Below contains the results for this test
        </p>
      </div>
      <div>
        <dl>
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
            Patient name
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
            <%= @vision_screening.patient.name.full %>
            </dd>
          </div>
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
            Performed By
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
            <%= @vision_screening.performed_by %>
            </dd>
          </div>

          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
            Date of Screening
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
            <%= @vision_screening.date_of_screening.strftime("%d %B %Y") %>
            </dd>
          </div>

          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
            Left Eye
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">

            <ul class="max-w-md border border-gray-200 rounded-md">

              <li class="border-t border-gray-200 first:border-t-0 pl-3 pr-4 py-3 flex items-center justify-between text-sm leading-5">
                <div class="flex-shrink-0 h-5 w-5 text-green-400 flex-1 flex items-center">
                  <span class="text-gray-900 ml-2 flex-1 w-0 truncate">
                    Actual Horizontal Total Field
                  </span>
                </div>
                <div class="ml-4 flex-shrink-0">
                  <div class="uppercase font-medium transition duration-150 ease-in-out">
                    <%= @vision_screening.total_left %>
                  </div>
                </div>
              </li>
              <li class="border-t border-gray-200 first:border-t-0 pl-3 pr-4 py-3 flex items-center justify-between text-sm leading-5">
                <div class="flex-shrink-0 h-5 w-5 text-green-400 flex-1 flex items-center">
                  <span class="text-gray-900 ml-2 flex-1 w-0 truncate">
                    Actual Horizontal Temporal Field
                  </span>
                </div>
                <div class="ml-4 flex-shrink-0">
                  <div class="uppercase font-medium transition duration-150 ease-in-out">
                    <%= @vision_screening.temporal_left %>
                  </div>
                </div>
              </li>
              <li class="border-t border-gray-200 first:border-t-0 pl-3 pr-4 py-3 flex items-center justify-between text-sm leading-5">
                <div class="flex-shrink-0 h-5 w-5 text-green-400 flex-1 flex items-center">
                  <span class="text-gray-900 ml-2 flex-1 w-0 truncate">
                    Snellen Rating in Decimal Notation without glasses
                  </span>
                </div>
                <div class="ml-4 flex-shrink-0">
                  <div class="uppercase font-medium transition duration-150 ease-in-out">
                    <%= @vision_screening.snellen_left_eye_without_glasses %>
                  </div>
                </div>
              </li>
              <li class="border-t border-gray-200 first:border-t-0 pl-3 pr-4 py-3 flex items-center justify-between text-sm leading-5">
                <div class="flex-shrink-0 h-5 w-5 text-green-400 flex-1 flex items-center">
                  <span class="text-gray-900 ml-2 flex-1 w-0 truncate">
                    Snellen Rating in Decimal Notation with glasses
                  </span>
                </div>
                <div class="ml-4 flex-shrink-0">
                  <div class="uppercase font-medium transition duration-150 ease-in-out">
                    <%= @vision_screening.snellen_left_eye %>
                  </div>
                </div>
              </li>

            </ul>
            </dd>
          </div>
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
            Right Eye
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">

            <ul class="max-w-md border border-gray-200 rounded-md">

              <li class="border-t border-gray-200 first:border-t-0 pl-3 pr-4 py-3 flex items-center justify-between text-sm leading-5">
                <div class="flex-shrink-0 h-5 w-5 text-green-400 flex-1 flex items-center">
                  <span class="text-gray-900 ml-2 flex-1 w-0 truncate">
                    Actual Horizontal Total Field
                  </span>
                </div>
                <div class="ml-4 flex-shrink-0">
                  <div class="uppercase font-medium transition duration-150 ease-in-out">
                    <%= @vision_screening.total_right %>
                  </div>
                </div>
              </li>
              <li class="border-t border-gray-200 first:border-t-0 pl-3 pr-4 py-3 flex items-center justify-between text-sm leading-5">
                <div class="flex-shrink-0 h-5 w-5 text-green-400 flex-1 flex items-center">
                  <span class="text-gray-900 ml-2 flex-1 w-0 truncate">
                    Actual Horizontal Temporal Field
                  </span>
                </div>
                <div class="ml-4 flex-shrink-0">
                  <div class="uppercase font-medium transition duration-150 ease-in-out">
                    <%= @vision_screening.temporal_right %>
                  </div>
                </div>
              </li>
              <li class="border-t border-gray-200 first:border-t-0 pl-3 pr-4 py-3 flex items-center justify-between text-sm leading-5">
                <div class="flex-shrink-0 h-5 w-5 text-green-400 flex-1 flex items-center">
                  <span class="text-gray-900 ml-2 flex-1 w-0 truncate">
                    Snellen Rating in Decimal Notation without glasses
                  </span>
                </div>
                <div class="ml-4 flex-shrink-0">
                  <div class="uppercase font-medium transition duration-150 ease-in-out">
                    <%= @vision_screening.snellen_right_eye_without_glasses %>
                  </div>
                </div>
              </li>

              <li class="border-t border-gray-200 first:border-t-0 pl-3 pr-4 py-3 flex items-center justify-between text-sm leading-5">
                <div class="flex-shrink-0 h-5 w-5 text-green-400 flex-1 flex items-center">
                  <span class="text-gray-900 ml-2 flex-1 w-0 truncate">
                    Snellen Rating in Decimal Notation with glasses
                  </span>
                </div>
                <div class="ml-4 flex-shrink-0">
                  <div class="uppercase font-medium transition duration-150 ease-in-out">
                    <%= @vision_screening.snellen_right_eye %>
                  </div>
                </div>
              </li>

            </ul>
            </dd>
          </div>
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
            Color Discrimination
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
            <%= @vision_screening.color_discrimination.capitalize %>
            </dd>
          </div>
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
            Clinic
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
            <%= @vision_screening.clinic.clinic_name %>
            </dd>
          </div>
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm leading-5 font-medium text-gray-500">
            Results
            </dt>
            <dd class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
            <% if @vision_screening.result.blank? %>
              -- No notable abnormalities --
            <% else %>
              <%= @vision_screening.result %>
            <% end %>
            </dd>
          </div>
        </dl>
      </div>
    </div>
  </div>
