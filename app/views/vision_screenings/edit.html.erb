<% content_for :head do %>
  <header class="bg-gray-800 shadow-sm">
    <div class="max-w-7xl mx-auto py-4 sm:px-6 lg:px-8">
      <h1 class="leading-6 font-semibold text-white text-sm">
        <%= link_to "#{ @vision_screening.patient.name.full} ( ID: #{ @vision_screening.patient.identification_number })", vision_screening_path( @vision_screening.patient ), class: "font-normal text-gray-300" %> | Edit Vision Screening results
      </h1>
    </div>
  </header>
<% end %>

<%= form_for @vision_screening, url: vision_screening_path do |f| %>

  <div class="flex-1 flex flex-col">
    
    <main class="flex-1 overflow-y-auto focus:outline-none" tabindex="0">
      <div class="relative max-w-4xl mx-auto md:px-8 xl:px-0">
        <!-- Description list with inline editing -->

        <div class="mt-10 space-y-6 divide-y divide-gray-200">
          <div class="space-y-1">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Tests
            </h3>
            <p class="max-w-2xl text-sm leading-5 text-gray-500">
            </p>
          </div>
          <div>
            <dl class="divide-y divide-gray-200">
              <fieldset class="mt-4">
                <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-baseline">
                  <div>
                    <legend class="text-base leading-6 font-medium text-gray-900 sm:text-sm sm:leading-5 sm:text-gray-700">
                      Right Eye
                    </legend>
                  </div>
                  <div class="sm:col-span-2">
                    <div class="max-w-lg">
                      <p class="text-sm leading-5 text-gray-500">Indicate the level of acuity</p>
                      <div class="mt-4">
                        <div class="flex items-center">
                          <%= f.radio_button( "snellen_right_eye", 0.1, required: true,class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R01" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/6 | Decimal: 0.1</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_right_eye", 0.2, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R02" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/36 | Decimal: 0.2</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_right_eye", 0.3, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R03" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/24 | Decimal: 0.3</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_right_eye", 0.4, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R04" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/18 | Decimal: 0.4</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_right_eye", 0.5, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R05" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/12 | Decimal: 0.5</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_right_eye", 0.6, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R06" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/9 | Decimal: 0.6</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_right_eye", 0.7, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R07" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/9+ | Decimal: 0.7</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_right_eye", 0.8, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R08" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/7.5 | Decimal: 0.8</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_right_eye", 0.9, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R09" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/7.5+ | Decimal: 0.9</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_right_eye", 1, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R10" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/6 | Decimal: 1.0</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_right_eye", 1.1, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R11" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/6+ | Decimal: 1.1</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_right_eye", 1.2, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R12" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/5+ | Decimal: 1.2</span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <%= f.error_for :snellen_right_eye, class: "text-red-600 -ml-4" %>
              </fieldset>

              <fieldset class="mt-4">
                <div class="sm:grid sm:grid-cols-3 sm:gap-4 sm:items-baseline">
                  <div class="mt-4">
                    <legend class="text-base leading-6 font-medium text-gray-900 sm:text-sm sm:leading-5 sm:text-gray-700">
                      Left Eye
                    </legend>
                  </div>
                  <div class="sm:col-span-2">
                    <div class="max-w-lg">
                      <p class="text-sm leading-5 text-gray-500">Indicate the level of acuity</p>
                      <div class="mt-4">
                        <div class="flex items-center">
                          <%= f.radio_button( "snellen_left_eye", 0.1, required: true, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R01" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/6 | Decimal: 0.1</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_left_eye", 0.2, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R02" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/36 | Decimal: 0.2</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_left_eye", 0.3, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R03" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/24 | Decimal: 0.3</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_left_eye", 0.4, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R04" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/18 | Decimal: 0.4</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_left_eye", 0.5, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R05" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/12 | Decimal: 0.5</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_left_eye", 0.6, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R06" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/9 | Decimal: 0.6</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_left_eye", 0.7, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R07" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/9+ | Decimal: 0.7</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_left_eye", 0.8, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R08" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/7.5 | Decimal: 0.8</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_left_eye", 0.9, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R09" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/7.5+ | Decimal: 0.9</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_left_eye", 1, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R10" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/6 | Decimal: 1.0</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_left_eye", 1.1, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R11" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/6+ | Decimal: 1.1</span>
                          </label>
                        </div>
                        <div class="mt-4 flex items-center">
                          <%= f.radio_button( "snellen_left_eye", 1.2, class: "form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out")%>
                          <label for="R12" class="ml-3">
                            <span class="block text-sm leading-5 font-medium text-gray-700">Snellen 6/5+ | Decimal: 1.2</span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <%= f.error_for :snellen_left_eye, class: "text-red-600 -ml-4" %>
              </fieldset>
            </dl>
          </div>
        </div>

        <div class="mt-10 space-y-6 divide-y divide-gray-200">
          <div class="space-y-1">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Actual Horizontal Temporal Field
            </h3>
            <p class="max-w-2xl text-sm leading-5 text-gray-500">
            </p>
          </div>
          <div>
            <dl class="divide-y divide-gray-200">

              <div class="py-4 space-y-1 sm:py-5 items-center sm:grid sm:grid-cols-3 sm:gap-6">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Right Eye
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-1">
                <%= f.text_field :temporal_right, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                <%= f.error_for :temporal_right, class: "text-red-600 -ml-4" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 items-center sm:grid sm:grid-cols-3 sm:gap-4">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Left Eye
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-1">
                <%= f.text_field :temporal_left, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                <%= f.error_for :temporal_left, class: "text-red-600 -ml-4" %>
                </dd>
              </div>


            </dl>
          </div>
        </div>


        <div class="mt-10 space-y-6 divide-y divide-gray-200">
          <div class="space-y-1">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              Actual Horizontal Total Field
            </h3>
            <p class="max-w-2xl text-sm leading-5 text-gray-500">
            </p>
          </div>
          <div>
            <dl class="divide-y divide-gray-200">

              <div class="py-4 space-y-1 sm:py-5 items-center sm:grid sm:grid-cols-3 sm:gap-6">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Right Eye
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-1">
                <%= f.text_field :total_right, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                <%= f.error_for :total_right, class: "text-red-600 -ml-4" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 items-center sm:grid sm:grid-cols-3 sm:gap-4">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Left Eye
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-1">
                <%= f.text_field :total_left, class: "form-input block w-full transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                <%= f.error_for :total_left, class: "text-red-600 -ml-4" %>
                </dd>
              </div>


            </dl>
          </div>
        </div>

        <div class="mt-10 space-y-6 divide-y divide-gray-200">
          <div class="space-y-1">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              General
            </h3>
            <p class="max-w-2xl text-sm leading-5 text-gray-500">
            </p>
          </div>
          <div>
            <dl class="divide-y divide-gray-200">

              <div class="py-4 space-y-1 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:pt-5">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Colour Discrimination
                </dt>
                <dd class="flex text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2">
                <%= f.check_box :color_discrimination, class: "text-indigo-600 form-checkbox h-6 w-6" %>

                <%= f.error_for :color_discrimination, class: "text-red-600 -ml-4" %>
                </dd>
              </div>


              <div class="py-4 space-y-1 sm:py-5 items-center sm:grid sm:grid-cols-3 sm:gap-4">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Comment
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-1">
                <%= f.text_field :comment, class: "form-input block w-full
                                                       transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                                                     <%= f.error_for :comment, class: "text-red-600 -ml-4" %>
                </dd>
              </div>

              <div class="py-4 space-y-1 sm:py-5 items-center sm:grid sm:grid-cols-3 sm:gap-4">
                <dt class="text-sm leading-5 font-medium text-gray-500">
                Performed by
                </dt>
                <dd class="flex space-x-1 flex-col text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-1">
                <%= f.text_field :performed_by, value: current_user.name.full, class: "form-input block w-full
                                                       transition duration-150 ease-in-out sm:text-sm sm:leading-5 " %>
                                                     <%= f.error_for :performed_by, class: "text-red-600 -ml-4" %>
                </dd>
              </div>

              <div class="mt-6 sm:mt-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:items-start sm:border-t sm:border-gray-200 sm:pt-5">
                <%= f.label "Date of Screening" , class: "block text-sm font-medium leading-5 text-gray-700 sm:mt-px sm:pt-2" %>
                <div class="mt-1 sm:mt-0 sm:col-span-2">
                  <div class="max-w-sm rounded-md shadow-sm">
                    <div class="max-w-lg mt-1 relative rounded-md shadow-sm">
                      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none ">
                        <%= inline_svg_tag( "calendar.svg", class: "fill-current h-4 w-4 text-gray-500" ) %>
                      </div>
                      <%= f.text_field :date_of_screening, as: :string,
                        data:
                        {
                          controller: "flatpickr",
                          flatpickr_alt_input: true,
                          flatpickr_alt_format: "l, d M Y",
                          flatpickr_date_format: "Y-m-d",
                          flatpickr_min_date: Time.zone.now - 12.month,
                          flatpickr_max_date: Time.zone.now ,
                          flatpickr_default_date: Time.zone.now - 1.day
                        },
                        class: "max-w-xs form-input block w-full pl-10 sm:text-sm sm:leading-5 text-gray-500"
                      %>
                    </div>

                  </div>
                </div>
              </div>



            </dl>
          </div>
        </div>

        <div class="flex justify-between items-center  mt-8 border-t border-gray-200 pt-5">

          <div class="flex text-sm text-indigo-400">
            <%= link_to "Delete Vision Screening", vision_screening_path( @vision_screening ), method: :delete %>
          </div>

          <div class="flex ">
            <span class="inline-flex rounded-md shadow-sm">
              <%= link_to "Cancel", :back,
                class: "py-2 px-4 border border-gray-300 rounded-md text-sm leading-5 font-medium
                   text-gray-700 hover:text-gray-500 focus:outline-none focus:border-blue-300
                   focus:shadow-outline-blue active:bg-gray-50 active:text-gray-800 transition duration-150 ease-in-out" %>
            </span>

            <span class="ml-3 inline-flex rounded-md shadow-sm">
              <%= f.submit  "Save", class:"inline-flex justify-center py-2 px-4 border border-transparent text-sm leading-5 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-500 focus:outline-none focus:border-indigo-700 focus:shadow-outline-indigo active:bg-indigo-700 transition duration-150 ease-in-out" %>
            </span>

          </div>
        </div>


      </div>
    </main>
  </div>
<% end %>

