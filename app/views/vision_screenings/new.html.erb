<% content_for :head do %>
  <header class="bg-gray-800 shadow-sm">
    <div class="max-w-7xl mx-auto py-4 sm:px-6 lg:px-8">
      <h1 class="leading-6 font-semibold text-white text-sm">
        Add a new vision screening assessment
      </h1>
    </div>
  </header>
<% end %>

<div data-controller='toggle-if' class="flex flex-col">
  <h1 class='mb-2'>How do you want to capture your vision screening information</h1>

  <input type="hidden" name="capture_type" id="capture_type" value="" data-toggle-if-target="input" />

  <div class="flex w-full max-w-3xl mx-auto">
    <fieldset x-data="radioGroup()">
      <ul class="flex relative bg-white rounded-md -space-y-px">
        <li>
          <label :class="{ 'border-gray-200': !(active === 1), 'bg-indigo-50 border-indigo-200': active === 1 }" class="flex items-center text-sm cursor-pointer relative border p-4 flex flex-col md:pl-4 md:pr-6 md:grid md:grid-cols-3 border-gray-200">
            <input id='vision_capture_link' data-action="click->toggle-if#update" data-option='data-capture' data-toggle-if-target='toggleable'name="pricing_plan" type="radio" @click="select(1)" @keydown.space="select(1)" @keydown.arrow-up="onArrowUp(1)" @keydown.arrow-down="onArrowDown(1)" class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 cursor-pointer border-gray-300" aria-describedby="plan-option-pricing-1 plan-option-limit-1" >
            <span class="ml-3 font-medium text-gray-900">Data Capture</span>
          </label>
        </li>
        <li>
          <label :class="{ 'border-gray-200': !(active === 2), 'bg-indigo-50 border-indigo-200': active === 2 }" class="flex items-center text-sm cursor-pointer relative border p-4 flex flex-col md:pl-4 md:pr-6 md:grid md:grid-cols-3 border-gray-200">
            <input id='vision_upload_link' data-action="click->toggle-if#update" data-option='upload' data-toggle-if-target='toggleable' name="pricing_plan" type="radio" @click="select(2)" @keydown.space="select(2)" @keydown.arrow-up="onArrowUp(2)" @keydown.arrow-down="onArrowDown(2)" class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 cursor-pointer border-gray-300" aria-describedby="plan-option-pricing-2 plan-option-limit-2">
            <span class="ml-3 font-medium text-gray-900">Upload Certficate</span>
          </label>
        </li>
      </ul>
    </fieldset>
  </div>

  <div data-option='data-capture' data-toggle-if-target='option' class="hidden">
    <%= turbo_frame_tag "new_capture_vision", src: new_patient_capture_vision_screening_path(@patient) %>
  </div>
  <div data-option='upload' data-toggle-if-target='option' class="hidden">
    <%= turbo_frame_tag "new_vision_screening_upload", src: new_patient_upload_vision_screening_path(@patient) %>
  </div>
</div>
