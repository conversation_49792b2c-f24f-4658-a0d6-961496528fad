class GenerateVisionScreeningReportWorker
  include Sidekiq::Worker

  def perform(args = {})
    @report = VisionScreening.find args["report_id"]
    @user = User.find args["user_id"]

    get_report
    add_report
    upload_report
    true
  end

  private

  def upload_report
    @report.assessment_reports.create!(
      report: uploaded_file,
      date_generated: Time.zone.today,
      generated_by: @user.name.full
    )
  end

  def uploaded_file
    ActionDispatch::Http::UploadedFile.new({
      filename: "VisionScreening-#{@report.report_patient_name}-#{DateTime.now.strftime("%Y%m%d-%H%M")}.pdf",
      content_type: "application/pdf",
      tempfile: @pdf
    })
  end

  def add_report
    temp_pdf = @download.to_pdf
    temp = Tempfile.new(["", ".pdf"])
    File.binwrite(temp, temp_pdf)
    @pdf = temp
    temp.close
  end

  def get_report
    @download =
      Download.new(
        report: @report,
        url: Rails.application.routes.url_helpers.vision_screening_download_url(@report),
        filename: @report.report_name
      )
  end
end
