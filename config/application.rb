require_relative "boot"

require "rails/all"

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module Occumed
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 7.0

    config.lograge.enabled = false
    config.lograge.formatter = Lograge::Formatters::Json.new

    config.lograge.custom_options = lambda do |event|
      exceptions = %w[controller action format id]
      options = event.payload.slice(:source_ip, :user_id, :host)
      options[:time] = Time.zone.now
      options[:params] = event.payload[:params]
      options[:exception] = event.payload[:exception]
      options[:exception_object] = event.payload[:exception_object]
      options
    end

    ActiveStorage::Engine.config
      .active_storage
      .content_types_to_serve_as_binary
      .delete("image/svg+xml")

    config.skylight.probes += %w[graphql redis]

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    # config.time_zone = "Central Time (US & Canada)"
    # config.eager_load_paths << Rails.root.join("extras")
  end
end
