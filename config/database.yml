default: &default
  adapter: postgresql
  encoding: unicode
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>

development:
  <<: *default
  database: app_development
  host: localhost 
  username: postgres 
  password: password
  port: 5432

test:
  <<: *default
  database: app_test
  host: localhost 
  username: postgres 
  password: password
  port: 5432

production:
  <<: *default
  url: <%= ENV['DATABASE_URL'] %>
