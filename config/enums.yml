# IMPORTANT: All application enums should be defined in this file
#
# They will be made available to the application via: config/initializers/enums.rb
# ...and will be exposed uner the ENUMS module
#
# NOTE: Entries in this file can be either key/value or a simple list
#
# Examples
#
#   RATINGS:
#     gold: happy
#     silver: satisfied
#     bronze: sad
#
#   The YAML above will be converted to the following enums
#
#   pry>ENUMS::RATINGS::GOLD       # happy
#   pry>ENUMS::RATINGS::HAPPY      # gold
#   pry>ENUMS::RATINGS::SILVER     # satisfied
#   pry>ENUMS::RATINGS::SATISFIED  # silver
#   pry>ENUMS::RATINGS::BRONZE     # sad
#   pry>ENUMS::RATINGS::SAD        # bronze
#
#   ENUMs also supports the following helper methods
#
#   pry>ENUMS::RATINGS.keys             # [gold, silver, bronze]
#   pry>ENUMS::RATINGS.values           # [happy, satisfied, sad]
#   pry>ENUMS::RATINGS.happy? :gold     # true
#   pry>ENUMS::RATINGS.happy? :bronze   # false
#   pry>ENUMS::RATINGS["happy"]         # gold
#   pry>ENUMS::RATINGS["gold"]          # happy
#
#   ----------------------------------------------------------------------------
#
#   USER_ROLES:
#     - administrator
#     - advertiser
#     - publisher
#
#   The YAML above will be converted to the following enums
#
#   pry>ENUMS::USER_ROLES::ADMINISTRATOR # administrator
#   pry>ENUMS::USER_ROLES::ADVERTISER    # advertiser
#   pry>ENUMS::USER_ROLES::PUBLISHER     # publisher
#
#   pry>ENUMS::USER_ROLES.keys                          # [administrator, advertiser, publisher]
#   pry>ENUMS::USER_ROLES.values                        # [administrator, advertiser, publisher]
#   pry>ENUMS::USER_ROLES.administrator? :administrator # true
#   pry>ENUMS::USER_ROLES.administrator? :publisher     # false
#   pry>ENUMS::USER_ROLES["administrator"]              # administrator
#
ASSESSMENT_STATUS:
  - captured
  - reviewed
  - signed_off

EVALUATION_TYPES:
  Pre_Employment: Pre-Employment
  Bi_Annual: Bi-Annual
  Annual: Annual
  Exit: Exit

GENDER:
  - male
  - female
  - other

EMPLOYMENT_TYPES:
  - employee
  - contractor
  - visitor
  - intern
  - other

USER_ROLES:
  - owner
  - administrator
  - staff

RATINGS:
  gold: happy
  silver: satisfied
  bronze: sad

MEDICAL_STATUSES:
  - draft
  - active
  - expired

IMAGE_CONTENT_TYPES:
  - image/gif
  - image/jpeg
  - image/png

PDF_CONTENT_TYPE:
  - application/pdf

ASSESSMENT_TYPES:
  physical: Physical Examination
  visual: Visual Screening
  audio: Audiometry
  spiro: Spirometry
  xray: Chest X Ray
  ecg: Resting ECG
  heat: Heat Stress Evaluation
  height: Working at Heights Evaluation
  labs: Drug Screening

OUTCOMES:
  fit: Fit, Mentally and Physically to perform duties as per job description
  not_fit: Not fit to perform durites as per job description
  pending: Fitness pending on further investigation
  exclusions: Fit, with the following exclusion

EXCLUSIONS:
  - noise zones
  - nil
  - respiratory risk
  - heat stress
  - Driving codes - A1, A.B EB
  - Driving code - C1, C, EC1, EC
  - heights
  - open water
  - fire
  - electricity
  - moving machinery
  - other

REFERRAL:
  - nil
  - own health care provider
  - optician
  - audiologist
