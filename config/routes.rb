# frozen_string_literal: true

# == Route Map
#
#                                Prefix Verb   URI Pattern                                                                              Controller#Action
#                                  root GET    /                                                                                        organisations#index
#                      new_user_session GET    /users/sign_in(.:format)                                                                 users/sessions#new
#                          user_session POST   /users/sign_in(.:format)                                                                 users/sessions#create
#                  destroy_user_session DELETE /users/sign_out(.:format)                                                                users/sessions#destroy
#                     new_user_password GET    /users/password/new(.:format)                                                            users/passwords#new
#                    edit_user_password GET    /users/password/edit(.:format)                                                           users/passwords#edit
#                         user_password PATCH  /users/password(.:format)                                                                users/passwords#update
#                                       PUT    /users/password(.:format)                                                                users/passwords#update
#                                       POST   /users/password(.:format)                                                                users/passwords#create
#              cancel_user_registration GET    /users/cancel(.:format)                                                                  users/registrations#cancel
#                 new_user_registration GET    /users/sign_up(.:format)                                                                 users/registrations#new
#                edit_user_registration GET    /users/edit(.:format)                                                                    users/registrations#edit
#                     user_registration PATCH  /users(.:format)                                                                         users/registrations#update
#                                       PUT    /users(.:format)                                                                         users/registrations#update
#                                       DELETE /users(.:format)                                                                         users/registrations#destroy
#                                       POST   /users(.:format)                                                                         users/registrations#create
#                 new_user_confirmation GET    /users/confirmation/new(.:format)                                                        devise/confirmations#new
#                     user_confirmation GET    /users/confirmation(.:format)                                                            devise/confirmations#show
#                                       POST   /users/confirmation(.:format)                                                            devise/confirmations#create
#                accept_user_invitation GET    /users/invitation/accept(.:format)                                                       devise/invitations#edit
#                remove_user_invitation GET    /users/invitation/remove(.:format)                                                       devise/invitations#destroy
#                   new_user_invitation GET    /users/invitation/new(.:format)                                                          devise/invitations#new
#                       user_invitation PATCH  /users/invitation(.:format)                                                              devise/invitations#update
#                                       PUT    /users/invitation(.:format)                                                              devise/invitations#update
#                                       POST   /users/invitation(.:format)                                                              devise/invitations#create
#                           sidekiq_web        /sidekiq                                                                                 Sidekiq::Web
#                 shrine_direct_uploads        /s3/params                                                                               #<Shrine::PresignEndpoint(:cache)>
#                edit_settings_password GET    /settings/password/edit(.:format)                                                        settings/passwords#edit
#                   settings_signatures GET    /settings/signatures(.:format)                                                           settings/signatures#index
#                                       POST   /settings/signatures(.:format)                                                           settings/signatures#create
#                new_settings_signature GET    /settings/signatures/new(.:format)                                                       settings/signatures#new
#               edit_settings_signature GET    /settings/signatures/:id/edit(.:format)                                                  settings/signatures#edit
#                    settings_signature GET    /settings/signatures/:id(.:format)                                                       settings/signatures#show
#                                       PATCH  /settings/signatures/:id(.:format)                                                       settings/signatures#update
#                                       PUT    /settings/signatures/:id(.:format)                                                       settings/signatures#update
#                                       DELETE /settings/signatures/:id(.:format)                                                       settings/signatures#destroy
#               settings_organisational GET    /settings/organisational(.:format)                                                       settings/organisationals#show
#                    organisation_teams GET    /organisations/:organisation_id/teams(.:format)                                          teams#index
#                                       POST   /organisations/:organisation_id/teams(.:format)                                          teams#create
#                 new_organisation_team GET    /organisations/:organisation_id/teams/new(.:format)                                      teams#new
#                  organisation_clinics GET    /organisations/:organisation_id/clinics(.:format)                                        clinics#index
#                                       POST   /organisations/:organisation_id/clinics(.:format)                                        clinics#create
#               new_organisation_clinic GET    /organisations/:organisation_id/clinics/new(.:format)                                    clinics#new
#                organisation_companies GET    /organisations/:organisation_id/companies(.:format)                                      companies#index
#                                       POST   /organisations/:organisation_id/companies(.:format)                                      companies#create
#              new_organisation_company GET    /organisations/:organisation_id/companies/new(.:format)                                  companies#new
#                 organisation_patients GET    /organisations/:organisation_id/patients(.:format)                                       patients#index
#                                       POST   /organisations/:organisation_id/patients(.:format)                                       patients#create
#              new_organisation_patient GET    /organisations/:organisation_id/patients/new(.:format)                                   patients#new
#              organisation_evaluations GET    /organisations/:organisation_id/evaluations(.:format)                                    organisation_evaluations#index
#                         organisations GET    /organisations(.:format)                                                                 organisations#index
#                                       POST   /organisations(.:format)                                                                 organisations#create
#                      new_organisation GET    /organisations/new(.:format)                                                             organisations#new
#                     edit_organisation GET    /organisations/:id/edit(.:format)                                                        organisations#edit
#                          organisation GET    /organisations/:id(.:format)                                                             organisations#show
#                                       PATCH  /organisations/:id(.:format)                                                             organisations#update
#                                       PUT    /organisations/:id(.:format)                                                             organisations#update
#                                       DELETE /organisations/:id(.:format)                                                             organisations#destroy
#                     lab_test_download GET    /lab_test/:lab_test_id/download(.:format)                                                lab_test_downloads#show
#                 new_lab_test_sign_off GET    /lab_test/:lab_test_id/sign_off/new(.:format)                                            lab_test_sign_offs#new
#                     lab_test_sign_off POST   /lab_test/:lab_test_id/sign_off(.:format)                                                lab_test_sign_offs#create
#                         edit_lab_test GET    /lab_test/:id/edit(.:format)                                                             lab_test#edit
#                              lab_test GET    /lab_test/:id(.:format)                                                                  lab_test#show
#                                       PATCH  /lab_test/:id(.:format)                                                                  lab_test#update
#                                       PUT    /lab_test/:id(.:format)                                                                  lab_test#update
#                                       DELETE /lab_test/:id(.:format)                                                                  lab_test#destroy
#                      generated_report GET    /generated_reports/:id(.:format)                                                         generated_reports#show
#                   evaluation_download GET    /evaluations/:evaluation_id/download(.:format)                                           downloads#show
#                            evaluation GET    /evaluations/:id(.:format)                                                               evaluations#show
#                             edit_team GET    /teams/:id/edit(.:format)                                                                teams#edit
#                                  team GET    /teams/:id(.:format)                                                                     teams#show
#                                       PATCH  /teams/:id(.:format)                                                                     teams#update
#                                       PUT    /teams/:id(.:format)                                                                     teams#update
#                                       DELETE /teams/:id(.:format)                                                                     teams#destroy
#                           edit_clinic GET    /clinics/:id/edit(.:format)                                                              clinics#edit
#                                clinic GET    /clinics/:id(.:format)                                                                   clinics#show
#                                       PATCH  /clinics/:id(.:format)                                                                   clinics#update
#                                       PUT    /clinics/:id(.:format)                                                                   clinics#update
#                                       DELETE /clinics/:id(.:format)                                                                   clinics#destroy
#                          edit_company GET    /companies/:id/edit(.:format)                                                            companies#edit
#                               company GET    /companies/:id(.:format)                                                                 companies#show
#                                       PATCH  /companies/:id(.:format)                                                                 companies#update
#                                       PUT    /companies/:id(.:format)                                                                 companies#update
#                                       DELETE /companies/:id(.:format)                                                                 companies#destroy
#                     edit_patient_note GET    /patient_notes/:id/edit(.:format)                                                        patient_notes#edit
#                          patient_note GET    /patient_notes/:id(.:format)                                                             patient_notes#show
#                                       PATCH  /patient_notes/:id(.:format)                                                             patient_notes#update
#                                       PUT    /patient_notes/:id(.:format)                                                             patient_notes#update
#                                       DELETE /patient_notes/:id(.:format)                                                             patient_notes#destroy
#                            edit_audio GET    /audios/:id/edit(.:format)                                                               audios#edit
#                                 audio GET    /audios/:id(.:format)                                                                    audios#show
#                                       PATCH  /audios/:id(.:format)                                                                    audios#update
#                                       PUT    /audios/:id(.:format)                                                                    audios#update
#                                       DELETE /audios/:id(.:format)                                                                    audios#destroy
#                       edit_employment GET    /employments/:id/edit(.:format)                                                          employments#edit
#                            employment PATCH  /employments/:id(.:format)                                                               employments#update
#                                       PUT    /employments/:id(.:format)                                                               employments#update
#                                       DELETE /employments/:id(.:format)                                                               employments#destroy
#                   patient_employments POST   /patients/:patient_id/employments(.:format)                                              employments#create
#                new_patient_employment GET    /patients/:patient_id/employments/new(.:format)                                          employments#new
#            patient_supplementary_docs GET    /patients/:patient_id/supplementary_docs(.:format)                                       supplementary_docs#index
#                   patient_assessments GET    /patients/:patient_id/assessments(.:format)                                              assessments#index
#                        patient_audios POST   /patients/:patient_id/audios(.:format)                                                   audios#create
#                     new_patient_audio GET    /patients/:patient_id/audios/new(.:format)                                               audios#new
#                   patient_evaluations GET    /patients/:patient_id/evaluations(.:format)                                              evaluations#index
#                                       POST   /patients/:patient_id/evaluations(.:format)                                              evaluations#create
#                new_patient_evaluation GET    /patients/:patient_id/evaluations/new(.:format)                                          evaluations#new
#                patient_lab_test_index GET    /patients/:patient_id/lab_test(.:format)                                                 lab_test#index
#                                       POST   /patients/:patient_id/lab_test(.:format)                                                 lab_test#create
#                  new_patient_lab_test GET    /patients/:patient_id/lab_test/new(.:format)                                             lab_test#new
#                 patient_patient_notes POST   /patients/:patient_id/patient_notes(.:format)                                            patient_notes#create
#              new_patient_patient_note GET    /patients/:patient_id/patient_notes/new(.:format)                                        patient_notes#new
#                          edit_patient GET    /patients/:id/edit(.:format)                                                             patients#edit
#                               patient GET    /patients/:id(.:format)                                                                  patients#show
#                                       PATCH  /patients/:id(.:format)                                                                  patients#update
#                                       PUT    /patients/:id(.:format)                                                                  patients#update
#                                       DELETE /patients/:id(.:format)                                                                  patients#destroy
#         rails_postmark_inbound_emails POST   /rails/action_mailbox/postmark/inbound_emails(.:format)                                  action_mailbox/ingresses/postmark/inbound_emails#create
#            rails_relay_inbound_emails POST   /rails/action_mailbox/relay/inbound_emails(.:format)                                     action_mailbox/ingresses/relay/inbound_emails#create
#         rails_sendgrid_inbound_emails POST   /rails/action_mailbox/sendgrid/inbound_emails(.:format)                                  action_mailbox/ingresses/sendgrid/inbound_emails#create
#   rails_mandrill_inbound_health_check GET    /rails/action_mailbox/mandrill/inbound_emails(.:format)                                  action_mailbox/ingresses/mandrill/inbound_emails#health_check
#         rails_mandrill_inbound_emails POST   /rails/action_mailbox/mandrill/inbound_emails(.:format)                                  action_mailbox/ingresses/mandrill/inbound_emails#create
#          rails_mailgun_inbound_emails POST   /rails/action_mailbox/mailgun/inbound_emails/mime(.:format)                              action_mailbox/ingresses/mailgun/inbound_emails#create
#        rails_conductor_inbound_emails GET    /rails/conductor/action_mailbox/inbound_emails(.:format)                                 rails/conductor/action_mailbox/inbound_emails#index
#                                       POST   /rails/conductor/action_mailbox/inbound_emails(.:format)                                 rails/conductor/action_mailbox/inbound_emails#create
#     new_rails_conductor_inbound_email GET    /rails/conductor/action_mailbox/inbound_emails/new(.:format)                             rails/conductor/action_mailbox/inbound_emails#new
#    edit_rails_conductor_inbound_email GET    /rails/conductor/action_mailbox/inbound_emails/:id/edit(.:format)                        rails/conductor/action_mailbox/inbound_emails#edit
#         rails_conductor_inbound_email GET    /rails/conductor/action_mailbox/inbound_emails/:id(.:format)                             rails/conductor/action_mailbox/inbound_emails#show
#                                       PATCH  /rails/conductor/action_mailbox/inbound_emails/:id(.:format)                             rails/conductor/action_mailbox/inbound_emails#update
#                                       PUT    /rails/conductor/action_mailbox/inbound_emails/:id(.:format)                             rails/conductor/action_mailbox/inbound_emails#update
#                                       DELETE /rails/conductor/action_mailbox/inbound_emails/:id(.:format)                             rails/conductor/action_mailbox/inbound_emails#destroy
# rails_conductor_inbound_email_reroute POST   /rails/conductor/action_mailbox/:inbound_email_id/reroute(.:format)                      rails/conductor/action_mailbox/reroutes#create
#                    rails_service_blob GET    /rails/active_storage/blobs/:signed_id/*filename(.:format)                               active_storage/blobs#show
#             rails_blob_representation GET    /rails/active_storage/representations/:signed_blob_id/:variation_key/*filename(.:format) active_storage/representations#show
#                    rails_disk_service GET    /rails/active_storage/disk/:encoded_key/*filename(.:format)                              active_storage/disk#show
#             update_rails_disk_service PUT    /rails/active_storage/disk/:encoded_token(.:format)                                      active_storage/disk#update
#                  rails_direct_uploads POST   /rails/active_storage/direct_uploads(.:format)                                           active_storage/direct_uploads#create

Rails.application.routes.draw do
  namespace :admin do
    # resources :xrays
    # resources :visuals
    # resources :vision_screening_uploads
    # resources :vision_screening_signoffs
    # resources :vision_screenings
    # resources :team_users
    # resources :teams
    # resources :spiros
    # resources :signatures
    # resources :referrals
    # resources :physical_signoffs
    # resources :physicals
    # resources :patient_notes
    # resources :patient_documents
    # resources :patients
    # resources :organisation_users
    # resources :organisations
    # resources :lab_test_signoffs
    # resources :lab_tests
    # resources :heights
    # resources :heats
    # resources :exclusions
    # resources :evaluation_signoffs
    # resources :evaluations
    resources :employments
    # resources :ecgs
    resources :companies
    # resources :clinic_teams
    # resources :clinics
    # resources :audios
    # resources :assessment_reports
    # resources :assessments
    # resources :annexure_threes
    # resources :attachments
    # resources :users

    root to: "companies#index"
  end
  if Rails.env.development?
    mount GraphiQL::Rails::Engine, at: "/graphiql", graphql_path: "/graphql"
  end

  post "/graphql", to: "graphql#execute"

  root "organisations#index"

  get "/check.txt", to: proc { [200, {}, ["it_works"]] }

  devise_for :users,
    controllers: {
      sessions: "users/sessions",
      registrations: "users/registrations",
      passwords: "users/passwords"
    }

  require "sidekiq/web"
  mount Sidekiq::Web => "/sidekiq"

  namespace :settings do
    resource :password, only: %i[edit]
    resources :signatures
    resource :organisational, only: [:show]
  end

  resources :organisations do
    resources :teams, only: %i[index create new]
    resources :clinics, only: %i[index create new]
    resources :companies, only: %i[index create new]
    resources :patients, only: %i[index create new]
    resources :evaluations, only: [:index], controller: "organisation_evaluations"
  end

  resources :lab_test, only: %i[show edit update destroy] do
    resource :download, only: %i[show], controller: "lab_test_downloads"
    resource :sign_off, only: %i[new create], controller: "lab_test_sign_offs"
  end

  resources :vision_screenings, only: %i[show edit update destroy] do
    resource :download, only: %i[show], controller: "vision_screening_downloads"
    resource :sign_off, only: %i[new create], controller: "vision_screening_sign_offs"
  end

  resources :physical_examinations, only: %i[show edit update destroy] do
    resource :download, only: %i[show], controller: "physical_downloads"
    resource :sign_off, only: %i[new create], controller: "physical_sign_offs"
  end

  resources :generated_reports, only: %i[show]

  resources :evaluations, only: %i[edit update show destroy] do
    resource :download, only: %i[show], controller: "evaluation_downloads"
    resource :sign_off, only: %i[new create], controller: "evaluation_sign_offs"
  end

  resources :teams, only: %i[show edit update destroy]
  resources :clinics, only: %i[show edit update destroy]

  resources :companies, only: %i[show edit update destroy]
  resources :patient_notes, only: %i[show edit update destroy]
  resources :exclusions, only: %i[show edit update destroy]
  resources :referrals, only: %i[show edit update destroy]
  resources :audios, only: %i[show edit update destroy]
  resources :employments, only: %i[edit update destroy]
  resources :patient_documents, only: %i[edit update destroy]

  namespace :upload do
    resources :vision_screenings, only: %i[edit update]
  end
  namespace :capture do
    resources :vision_screenings, only: %i[edit update]
  end

  resources :patients, only: %i[show edit update destroy] do
    resources :referrals, only: %i[new create]
    resources :exclusions, only: %i[new create]
    resources :employments, only: %i[new create]
    resources :assessments, only: %i[index]
    resources :audios, only: %i[new create]
    resources :evaluations, only: %i[index new create]
    resources :lab_test, only: %i[index new create]
    resources :patient_notes, only: %i[new create]
    resources :medical_questionaires, only: %i[new create]
    resources :physical_examinations, only: %i[new create]
    resources :annexure_three, only: %i[new create]
    resources :vision_screenings, only: %i[new create]
    resources :patient_documents, only: %i[new create]
    namespace :upload do
      resources :vision_screenings, only: %i[new create]
    end
    namespace :capture do
      resources :vision_screenings, only: %i[new create]
    end
  end

  # Expired medicals report routes
  resources :expired_medicals, only: [:index] do
    collection do
      get :export_csv
    end
  end
end
