class CreateEvaluations < ActiveRecord::Migration[6.0]
  def change
    create_table :evaluations do |t|
      t.string :name

      t.string :medical_type

      t.boolean :physical_exam_performed, default: false, null: false
      t.boolean :spiro_performed, default: false, null: false
      t.boolean :audio_performed, default: false, null: false
      t.boolean :visual_performed, default: false, null: false
      t.boolean :xray_performed, default: false, null: false
      t.boolean :ecg_performed, default: false, null: false
      t.boolean :heat_performed, default: false, null: false
      t.boolean :height_performed, default: false, null: false
      t.boolean :cannabis_performed, default: false, null: false

      t.string :exclusions

      t.boolean :referral
      t.string :referral_comment

      t.text :outcome
      t.text :outcome_comment

      t.date :medical_examination_date
      t.date :medical_expiry_date

      t.belongs_to :patient, null: false, foreign_key: true

      t.timestamps
    end
  end
end
