class CreateEmployments < ActiveRecord::Migration[6.0]
  def change
    create_table :employments do |t|
      t.belongs_to :patient, null: false, foreign_key: true
      t.belongs_to :company, null: false, foreign_key: true
      t.date :induction_date
      t.date :termination_date
      t.string :position
      t.string :department
      t.string :employment_type
      t.string :termination_reason

      t.timestamps
    end
  end
end
