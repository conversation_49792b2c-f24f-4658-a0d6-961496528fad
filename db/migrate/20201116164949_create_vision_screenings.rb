class CreateVisionScreenings < ActiveRecord::Migration[6.0]
  def change
    create_table :vision_screenings do |t|
      t.string :name

      t.string :snellen_right_eye
      t.string :snellen_left_eye

      t.string :temporal_right
      t.string :temporal_left

      t.string :total_right
      t.string :total_left

      t.string :color_discrimination

      t.text :comment
      t.date :date_of_screening
      t.string :performed_by

      t.references :patient, null: false, foreign_key: true

      t.timestamps
    end
  end
end
