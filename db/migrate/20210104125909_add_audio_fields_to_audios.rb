class AddAudioFieldsToAudios < ActiveRecord::Migration[6.0]
  disable_ddl_transaction!

  def change
    safety_assured { remove_column :audios, :description }

    add_reference :audios, :patient, index: {algorithm: :concurrently}
    add_reference :audios, :clinic, index: {algorithm: :concurrently}

    add_column :audios, :note, :text
    add_column :audios, :signed_by, :text
    add_column :audios, :system_used, :text
    add_column :audios, :status, :text
  end
end
