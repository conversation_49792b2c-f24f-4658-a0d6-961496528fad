class CreateVisionScreeningUploads < ActiveRecord::Migration[6.0]
  def change
    create_table :vision_screening_uploads do |t|
      t.date :date_performed
      t.string :name
      t.text :note
      t.string :result
      t.date :signature_date
      t.string :signed_by
      t.string :status
      t.string :system_used
      t.references :clinic, null: false, foreign_key: true
      t.references :patient, null: false, foreign_key: true

      t.timestamps
    end
  end
end
