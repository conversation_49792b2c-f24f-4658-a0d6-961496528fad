class CreateAnnexureThrees < ActiveRecord::Migration[7.0]
  def change
    create_table :annexure_threes do |t|
      t.references :patient, null: false, foreign_key: true
      t.references :employment, null: false, foreign_key: true
      t.string :exposures, array: true, default: []
      t.string :job_requirements, array: true, default: []
      t.string :protective_equipment, array: true, default: []

      t.timestamps
    end
  end
end
