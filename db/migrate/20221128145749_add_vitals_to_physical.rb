class AddVitalsToPhysical < ActiveRecord::Migration[7.0]
  def change
    add_column :physicals, :height, :string
    add_column :physicals, :weight, :string
    add_column :physicals, :blood_pressure, :string
    add_column :physicals, :pulse, :string
    add_column :physicals, :blood_sugar, :string
    add_column :physicals, :date_performed, :date
    add_column :physicals, :note, :text

    safety_assured {
      add_reference :physicals, :clinic, null: false, foreign_key: true
      add_reference :physicals, :patient, null: false, foreign_key: true
    }
  end
end
