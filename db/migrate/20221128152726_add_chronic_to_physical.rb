class AddChronicToPhysical < ActiveRecord::Migration[7.0]
  def change
    add_column :physicals, :hypertension_chronic, :boolean
    add_column :physicals, :asthma_chronic, :boolean
    add_column :physicals, :epilepsy_chronic, :boolean
    add_column :physicals, :mental_chronic, :boolean
    add_column :physicals, :obesity_chronic, :boolean
    add_column :physicals, :diabetes_chronic, :boolean
    add_column :physicals, :drug_chronic, :boolean
    add_column :physicals, :thyroid_chronic, :boolean
    add_column :physicals, :copd_chronic, :boolean
    add_column :physicals, :cardiac_chronic, :boolean
    add_column :physicals, :prosthesis_chronic, :boolean
    add_column :physicals, :arthrithis_chronic, :boolean
  end
end
