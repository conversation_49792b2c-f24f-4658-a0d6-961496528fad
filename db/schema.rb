# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2023_01_01_111920) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "fuzzystrmatch"
  enable_extension "pg_trgm"
  enable_extension "plpgsql"

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "name", null: false
    t.string "record_type", null: false
    t.bigint "record_id", null: false
    t.bigint "blob_id", null: false
    t.datetime "created_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "index_active_storage_attachments_uniqueness", unique: true
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.string "key", null: false
    t.string "filename", null: false
    t.string "content_type"
    t.text "metadata"
    t.string "service_name", null: false
    t.bigint "byte_size", null: false
    t.string "checksum"
    t.datetime "created_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "active_storage_variant_records", force: :cascade do |t|
    t.bigint "blob_id", null: false
    t.string "variation_digest", null: false
    t.index ["blob_id", "variation_digest"], name: "index_active_storage_variant_records_uniqueness", unique: true
  end

  create_table "annexure_threes", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.bigint "employment_id", null: false
    t.string "exposures", default: [], array: true
    t.string "job_requirements", default: [], array: true
    t.string "protective_equipment", default: [], array: true
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["employment_id"], name: "index_annexure_threes_on_employment_id"
    t.index ["patient_id"], name: "index_annexure_threes_on_patient_id"
  end

  create_table "assessment_reports", force: :cascade do |t|
    t.text "report_data"
    t.date "date_generated"
    t.string "generated_by"
    t.string "assessable_type", null: false
    t.bigint "assessable_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["assessable_type", "assessable_id"], name: "index_assessment_reports_on_assessable_type_and_assessable_id"
  end

  create_table "assessments", force: :cascade do |t|
    t.string "name"
    t.string "description"
    t.string "performed_by"
    t.date "date_performed"
    t.integer "actable_id"
    t.string "actable_type"
    t.bigint "patient_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["patient_id"], name: "index_assessments_on_patient_id"
  end

  create_table "attachments", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.text "content_data"
    t.string "attachable_type", null: false
    t.bigint "attachable_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["attachable_type", "attachable_id"], name: "index_attachments_on_attachable_type_and_attachable_id"
  end

  create_table "audios", force: :cascade do |t|
    t.string "name"
    t.string "result"
    t.string "performed_by"
    t.date "date_performed"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "patient_id"
    t.bigint "clinic_id"
    t.text "note"
    t.text "signed_by"
    t.text "system_used"
    t.text "status"
    t.date "signature_date"
    t.index ["clinic_id"], name: "index_audios_on_clinic_id"
    t.index ["patient_id"], name: "index_audios_on_patient_id"
  end

  create_table "clinic_teams", force: :cascade do |t|
    t.bigint "clinic_id", null: false
    t.bigint "team_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["clinic_id"], name: "index_clinic_teams_on_clinic_id"
    t.index ["team_id"], name: "index_clinic_teams_on_team_id"
  end

  create_table "clinics", force: :cascade do |t|
    t.string "clinic_name"
    t.string "physical_address"
    t.string "details"
    t.bigint "organisation_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "physical_address_2"
    t.string "phone_number"
    t.index ["organisation_id"], name: "index_clinics_on_organisation_id"
  end

  create_table "companies", force: :cascade do |t|
    t.string "city"
    t.string "street_address"
    t.string "industry_sector"
    t.string "name"
    t.string "about"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "organisation_id", default: 1, null: false
    t.string "phone_number"
    t.string "email"
    t.string "suburb"
    t.index ["organisation_id"], name: "index_companies_on_organisation_id"
  end

  create_table "ecgs", force: :cascade do |t|
    t.string "result"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "employments", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.bigint "company_id", null: false
    t.date "induction_date"
    t.date "termination_date"
    t.string "position"
    t.string "department"
    t.string "employment_type"
    t.string "termination_reason"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["company_id"], name: "index_employments_on_company_id"
    t.index ["patient_id"], name: "index_employments_on_patient_id"
  end

  create_table "evaluation_signoffs", force: :cascade do |t|
    t.date "date_signed"
    t.bigint "user_id", null: false
    t.bigint "evaluation_id", null: false
    t.string "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["evaluation_id"], name: "index_evaluation_signoffs_on_evaluation_id"
    t.index ["user_id"], name: "index_evaluation_signoffs_on_user_id"
  end

  create_table "evaluations", force: :cascade do |t|
    t.string "name"
    t.string "medical_type"
    t.boolean "physical_exam_performed", default: false, null: false
    t.boolean "spiro_performed", default: false, null: false
    t.boolean "audio_performed", default: false, null: false
    t.boolean "visual_performed", default: false, null: false
    t.boolean "xray_performed", default: false, null: false
    t.boolean "ecg_performed", default: false, null: false
    t.boolean "heat_performed", default: false, null: false
    t.boolean "height_performed", default: false, null: false
    t.boolean "cannabis_performed", default: false, null: false
    t.boolean "referral"
    t.string "referral_comment"
    t.text "outcome"
    t.text "outcome_comment"
    t.date "medical_examination_date"
    t.date "medical_expiry_date"
    t.bigint "patient_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "employment_id"
    t.bigint "lab_test_id"
    t.bigint "clinic_id"
    t.string "status"
    t.bigint "audio_id"
    t.string "exclusion_comment"
    t.boolean "exclusion", default: false, null: false
    t.index ["audio_id"], name: "index_evaluations_on_audio_id"
    t.index ["clinic_id"], name: "index_evaluations_on_clinic_id"
    t.index ["employment_id"], name: "index_evaluations_on_employment_id"
    t.index ["lab_test_id"], name: "index_evaluations_on_lab_test_id"
    t.index ["patient_id"], name: "index_evaluations_on_patient_id"
  end

  create_table "evaluations_exclusions", id: false, force: :cascade do |t|
    t.bigint "evaluation_id", null: false
    t.bigint "exclusion_id", null: false
    t.index ["evaluation_id", "exclusion_id"], name: "index_evaluations_exclusions_on_evaluation_id_and_exclusion_id"
    t.index ["exclusion_id", "evaluation_id"], name: "index_evaluations_exclusions_on_exclusion_id_and_evaluation_id"
  end

  create_table "evaluations_referrals", id: false, force: :cascade do |t|
    t.bigint "evaluation_id", null: false
    t.bigint "referral_id", null: false
    t.index ["evaluation_id", "referral_id"], name: "index_evaluations_referrals_on_evaluation_id_and_referral_id"
    t.index ["referral_id", "evaluation_id"], name: "index_evaluations_referrals_on_referral_id_and_evaluation_id"
  end

  create_table "exclusions", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.text "note"
    t.string "category"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["patient_id"], name: "index_exclusions_on_patient_id"
  end

  create_table "heats", force: :cascade do |t|
    t.string "result"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "heights", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "lab_test_signoffs", force: :cascade do |t|
    t.date "date_signed"
    t.bigint "user_id", null: false
    t.bigint "lab_test_id", null: false
    t.string "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["lab_test_id"], name: "index_lab_test_signoffs_on_lab_test_id"
    t.index ["user_id"], name: "index_lab_test_signoffs_on_user_id"
  end

  create_table "lab_tests", force: :cascade do |t|
    t.string "cannabis"
    t.string "six_panel"
    t.string "gamma"
    t.string "ast"
    t.string "fbc"
    t.string "hiv"
    t.text "comment"
    t.bigint "clinic_id", null: false
    t.string "status"
    t.string "signed_by"
    t.date "date_signed"
    t.text "description"
    t.date "date_performed"
    t.string "name"
    t.string "performed_by"
    t.bigint "patient_id"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["clinic_id"], name: "index_lab_tests_on_clinic_id"
    t.index ["patient_id"], name: "index_lab_tests_on_patient_id"
  end

  create_table "organisation_users", force: :cascade do |t|
    t.bigint "user_id", null: false
    t.bigint "organisation_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["organisation_id"], name: "index_organisation_users_on_organisation_id"
    t.index ["user_id"], name: "index_organisation_users_on_user_id"
  end

  create_table "organisations", force: :cascade do |t|
    t.string "organisation_name"
    t.string "registration_number"
    t.string "email_address"
    t.string "web_address"
    t.string "contact_number"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "initials"
  end

  create_table "patient_documents", force: :cascade do |t|
    t.string "name"
    t.text "description"
    t.string "uploaded_by"
    t.bigint "patient_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["patient_id"], name: "index_patient_documents_on_patient_id"
  end

  create_table "patient_notes", force: :cascade do |t|
    t.text "note"
    t.string "last_edited_by"
    t.bigint "patient_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["patient_id"], name: "index_patient_notes_on_patient_id"
  end

  create_table "patients", force: :cascade do |t|
    t.string "first_name"
    t.string "last_name"
    t.string "identification_number"
    t.string "email"
    t.string "phone_number"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "gender"
    t.date "dob"
    t.bigint "organisation_id", default: 1, null: false
    t.index ["organisation_id"], name: "index_patients_on_organisation_id"
  end

  create_table "pg_search_documents", force: :cascade do |t|
    t.text "content"
    t.string "searchable_type"
    t.bigint "searchable_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["searchable_type", "searchable_id"], name: "index_pg_search_documents_on_searchable_type_and_searchable_id"
  end

  create_table "physical_signoffs", force: :cascade do |t|
    t.date "date_signed"
    t.string "notes"
    t.bigint "physical_id", null: false
    t.bigint "user_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["physical_id"], name: "index_physical_signoffs_on_physical_id"
    t.index ["user_id"], name: "index_physical_signoffs_on_user_id"
  end

  create_table "physicals", force: :cascade do |t|
    t.string "result"
    t.string "performed_by"
    t.string "height"
    t.string "weight"
    t.string "blood_pressure"
    t.string "pulse"
    t.string "blood_sugar"
    t.date "date_performed"
    t.text "note"
    t.bigint "clinic_id", null: false
    t.bigint "patient_id", null: false
    t.boolean "urine_test"
    t.boolean "nad_test"
    t.boolean "leucocytes_test"
    t.boolean "nitrite_test"
    t.boolean "blood_test"
    t.boolean "protein_test"
    t.boolean "glucose_test"
    t.string "general_appearance"
    t.boolean "peripheral_exam"
    t.boolean "skin_exam"
    t.boolean "ent_exam"
    t.boolean "cvs_exam"
    t.boolean "chest_exam"
    t.boolean "gastro_exam"
    t.boolean "urinary_exam"
    t.boolean "musculo_exam"
    t.boolean "cns_exam"
    t.boolean "endocrine_exam"
    t.boolean "glucose_exam"
    t.boolean "hypertension_chronic"
    t.boolean "asthma_chronic"
    t.boolean "epilepsy_chronic"
    t.boolean "mental_chronic"
    t.boolean "obesity_chronic"
    t.boolean "diabetes_chronic"
    t.boolean "drug_chronic"
    t.boolean "thyroid_chronic"
    t.boolean "copd_chronic"
    t.boolean "cardiac_chronic"
    t.boolean "prosthesis_chronic"
    t.boolean "arthrithis_chronic"
    t.string "status"
    t.index ["clinic_id"], name: "index_physicals_on_clinic_id"
    t.index ["patient_id"], name: "index_physicals_on_patient_id"
  end

  create_table "referrals", force: :cascade do |t|
    t.bigint "patient_id", null: false
    t.string "specialist_type"
    t.text "note"
    t.string "attention"
    t.date "referral_date"
    t.string "medical_centre"
    t.text "address"
    t.text "issues"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["patient_id"], name: "index_referrals_on_patient_id"
  end

  create_table "signatures", force: :cascade do |t|
    t.text "image_data"
    t.string "first_line"
    t.string "second_line"
    t.string "third_line"
    t.string "forth_line"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "signable_type", null: false
    t.bigint "signable_id", null: false
    t.index ["signable_type", "signable_id"], name: "index_signatures_on_signable_type_and_signable_id"
  end

  create_table "spiros", force: :cascade do |t|
    t.string "result"
    t.string "performed_by"
    t.bigint "patient_id", null: false
    t.bigint "clinic_id", null: false
    t.date "date_performed"
    t.string "name"
    t.text "note"
    t.string "status"
    t.string "system_used"
    t.index ["clinic_id"], name: "index_spiros_on_clinic_id"
    t.index ["patient_id"], name: "index_spiros_on_patient_id"
  end

  create_table "team_users", force: :cascade do |t|
    t.bigint "team_id", null: false
    t.bigint "user_id", null: false
    t.boolean "is_active"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["team_id"], name: "index_team_users_on_team_id"
    t.index ["user_id"], name: "index_team_users_on_user_id"
  end

  create_table "teams", force: :cascade do |t|
    t.string "team_name"
    t.bigint "organisation_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["organisation_id"], name: "index_teams_on_organisation_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "first_name"
    t.string "last_name"
    t.string "user_mobile"
    t.boolean "is_active"
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "remember_created_at", precision: nil
    t.integer "sign_in_count", default: 0, null: false
    t.datetime "current_sign_in_at", precision: nil
    t.datetime "last_sign_in_at", precision: nil
    t.inet "current_sign_in_ip"
    t.inet "last_sign_in_ip"
    t.string "confirmation_token"
    t.datetime "confirmed_at", precision: nil
    t.datetime "confirmation_sent_at", precision: nil
    t.string "unconfirmed_email"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "invitation_token"
    t.datetime "invitation_created_at", precision: nil
    t.datetime "invitation_sent_at", precision: nil
    t.datetime "invitation_accepted_at", precision: nil
    t.integer "invitation_limit"
    t.string "invited_by_type"
    t.bigint "invited_by_id"
    t.integer "invitations_count", default: 0
    t.integer "default_organisation_id"
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["invitation_token"], name: "index_users_on_invitation_token", unique: true
    t.index ["invitations_count"], name: "index_users_on_invitations_count"
    t.index ["invited_by_id"], name: "index_users_on_invited_by_id"
    t.index ["invited_by_type", "invited_by_id"], name: "index_users_on_invited_by_type_and_invited_by_id"
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  create_table "vision_screening_signoffs", force: :cascade do |t|
    t.date "date_signed"
    t.bigint "user_id", null: false
    t.bigint "vision_screening_id", null: false
    t.string "notes"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_vision_screening_signoffs_on_user_id"
    t.index ["vision_screening_id"], name: "index_vision_screening_signoffs_on_vision_screening_id"
  end

  create_table "vision_screening_uploads", force: :cascade do |t|
    t.date "date_performed"
    t.string "name"
    t.text "note"
    t.string "result"
    t.date "signature_date"
    t.string "signed_by"
    t.string "status"
    t.string "system_used"
    t.bigint "clinic_id", null: false
    t.bigint "patient_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "performed_by"
    t.index ["clinic_id"], name: "index_vision_screening_uploads_on_clinic_id"
    t.index ["patient_id"], name: "index_vision_screening_uploads_on_patient_id"
  end

  create_table "vision_screenings", force: :cascade do |t|
    t.string "name"
    t.string "snellen_right_eye"
    t.string "snellen_left_eye"
    t.string "temporal_right"
    t.string "temporal_left"
    t.string "total_right"
    t.string "total_left"
    t.string "color_discrimination"
    t.text "comment"
    t.date "date_of_screening"
    t.string "performed_by"
    t.bigint "patient_id", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "status"
    t.string "signed_by"
    t.date "date_signed"
    t.bigint "clinic_id"
    t.string "snellen_right_eye_without_glasses"
    t.string "snellen_left_eye_without_glasses"
    t.index ["clinic_id"], name: "index_vision_screenings_on_clinic_id"
    t.index ["patient_id"], name: "index_vision_screenings_on_patient_id"
  end

  create_table "visuals", force: :cascade do |t|
    t.string "result"
    t.string "performed_by"
  end

  create_table "xrays", force: :cascade do |t|
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  add_foreign_key "active_storage_attachments", "active_storage_blobs", column: "blob_id"
  add_foreign_key "active_storage_variant_records", "active_storage_blobs", column: "blob_id"
  add_foreign_key "annexure_threes", "employments"
  add_foreign_key "annexure_threes", "patients"
  add_foreign_key "assessments", "patients"
  add_foreign_key "clinic_teams", "clinics"
  add_foreign_key "clinic_teams", "teams"
  add_foreign_key "clinics", "organisations"
  add_foreign_key "companies", "organisations"
  add_foreign_key "employments", "companies"
  add_foreign_key "employments", "patients"
  add_foreign_key "evaluation_signoffs", "evaluations", on_delete: :cascade
  add_foreign_key "evaluation_signoffs", "users"
  add_foreign_key "evaluations", "clinics"
  add_foreign_key "evaluations", "employments"
  add_foreign_key "evaluations", "lab_tests"
  add_foreign_key "evaluations", "patients"
  add_foreign_key "exclusions", "patients"
  add_foreign_key "lab_test_signoffs", "lab_tests"
  add_foreign_key "lab_test_signoffs", "users"
  add_foreign_key "lab_tests", "clinics"
  add_foreign_key "lab_tests", "patients"
  add_foreign_key "organisation_users", "organisations"
  add_foreign_key "organisation_users", "users"
  add_foreign_key "patient_documents", "patients"
  add_foreign_key "patient_notes", "patients"
  add_foreign_key "patients", "organisations"
  add_foreign_key "physical_signoffs", "physicals"
  add_foreign_key "physical_signoffs", "users"
  add_foreign_key "physicals", "clinics"
  add_foreign_key "physicals", "patients"
  add_foreign_key "referrals", "patients"
  add_foreign_key "spiros", "clinics"
  add_foreign_key "spiros", "patients"
  add_foreign_key "team_users", "teams"
  add_foreign_key "team_users", "users"
  add_foreign_key "teams", "organisations"
  add_foreign_key "vision_screening_signoffs", "users"
  add_foreign_key "vision_screening_signoffs", "vision_screenings"
  add_foreign_key "vision_screening_uploads", "clinics"
  add_foreign_key "vision_screening_uploads", "patients"
  add_foreign_key "vision_screenings", "clinics"
  add_foreign_key "vision_screenings", "patients"
end
