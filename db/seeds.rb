# frozen_string_literal: true

require "faker"

Rails.logger.debug "Creating org and Usr"

org = Organisation.find_or_create_by(organisation_name: "MarianMed")

Rails.logger.debug "user"

usr = User.create(
  first_name: "vin",
  last_name: "ken",
  password: "123456",
  password_confirmation: "123456",
  confirmed_at: DateTime.now,
  email: "<EMAIL>"
)

OrganisationUser.create(user_id: usr.id, organisation_id: org.id)

Rails.logger.debug "Creating companies"

10.times do
  Company.create(
    email: Faker::Internet.email,
    name: Faker::Company.name,
    industry_sector: Faker::Company.industry,
    about: Faker::Company.catch_phrase,
    organisation_id: org.id,
    street_address: Faker::Address.street_address,
    city: Faker::Address.state,

    suburb: Faker::Address.city,
    phone_number: Faker::PhoneNumber.cell_phone
  )
end

Rails.logger.debug "Creating Patients"

Company.all.each do |company|
  3.times do
    Patient.create!(
      first_name: Faker::Name.first_name,
      last_name: Faker::Name.last_name,
      dob: Faker::Date.birthday(min_age: 18, max_age: 65),
      gender: Faker::Gender.binary_type,
      identification_number: Faker::IDNumber.south_african_id_number,
      email: Faker::Internet.email,
      phone_number: Faker::PhoneNumber.cell_phone,
      organisation_id: org.id
    )
  end
end

# puts "Creating Assessments"
# Patient.all.each do |patient|
#   7.times do
#     # Evaluation.create!(
#     #   name: "#{ Faker::Lorem.characters(number: 3, min_numeric: 0).upcase }#{ Faker::Number.number(digits: 7) }",
#     #   patient_id: patient.id,

#     #   audio: Faker::Boolean.boolean,
#     #   cannabis: Faker::Boolean.boolean,
#     #   ecg: Faker::Boolean.boolean,

#     #   heat: Faker::Boolean.boolean,
#     #   height: Faker::Boolean.boolean,
#     #   visual: Faker::Boolean.boolean,

#     #   xray: Faker::Boolean.boolean,
#     #   spiro: Faker::Boolean.boolean,
#     #   physical_exam: Faker::Boolean.boolean,

#     #   outcome: Faker::Lorem.word,
#     #   outcome_comment: Faker::Lorem.sentence,

#     #   medical_examination_date: Faker::Date.between(from: 17.days.ago, to: Date.today),
#     #   medical_expiry_date: Faker::Date.between(from: Date.today, to: 1.year.from_now),

#     # )

#     PatientNote.create!(
#       note: Faker::Lorem.paragraph(sentence_count: 4),
#       last_edited_by: "#{ Faker::Name.first_name } #{ Faker::Name.last_name }",
#       patient_id: patient.id
#     )

#     Spiro.create!(
#       result: Faker::Lorem.word,
#       performed_by: "#{ Faker::Name.first_name } #{ Faker::Name.last_name }",
#       patient_id: patient.id
#     )

#     Audio.create!(
#       result: Faker::Lorem.word,
#       performed_by: "#{ Faker::Name.first_name } #{ Faker::Name.last_name }",
#       patient_id: patient.id
#     )

#     Physical.create!(
#       result: Faker::Lorem.word,
#       performed_by: "#{ Faker::Name.first_name } #{ Faker::Name.last_name }",
#       patient_id: patient.id
#     )

#     Visual.create!(
#       result: Faker::Lorem.word,
#       performed_by: "#{ Faker::Name.first_name } #{ Faker::Name.last_name }",
#       patient_id: patient.id
#     )

#     Referral.create!(
#       patient_id: patient.id,
#       specialist_type: Faker::Company.profession,
#       note: Faker::Lorem.sentence,
#     )

#     Exclusion.create!(
#       patient_id: patient.id,
#       note: Faker::Lorem.sentence,
#       category: Faker::Lorem.sentence,
#     )

#   end
# end

Rails.logger.debug "Seeds created"
