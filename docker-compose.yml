version: '3'

volumes:
  socket:
    driver: local
  pgdata:
    driver: local
  minio_data:
    driver: local
  redis_data:
    driver: local

services:
  db:
    image: postgres:14
    container_name: postgres
    ports:
      - 5432:5432
    volumes:
      - socket:/var/run/postgresql/
      - pgdata:/var/lib/postgresql/data
    environment:
      POSTGRES_HOST: "localhost"
      POSTGRES_HOST_AUTH_METHOD: "trust"
      POSTGRES_PASSWORD: "password"
      POSTGRES_USER: "postgres"

  redis:
    image: redis:6.2.7-alpine
    command: redis-server
    ports:
      - 6379:6379
    volumes:
      - redis_data:/data

  aws:
    image: minio/minio:latest
    container_name: aws-minio
    ports:
      - 9000:9000
      - 9001:9001
    command: server /data --console-address ":9001"
    environment:
      MINIO_ACCESS_KEY: minio
      MINIO_SECRET_KEY: minio123
      MINIO_REGION_NAME: myregion
      MINIO_API_CORS_ALLOW_ORIGIN: '*'
    volumes:
      - minio_data:/data

  mail:
    image: stpaquet/alpinemailcatcher
    ports:
      - 1080:1080
      - 1025:1025
