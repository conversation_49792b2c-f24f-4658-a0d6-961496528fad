namespace :reports do
  desc "Generate expired medicals report grouped by company and ordered by expiry date"
  task :expired_medicals, [:organisation_id] => :environment do |task, args|
    organisation_id = args[:organisation_id]
    
    if organisation_id.blank?
      puts "Usage: rake reports:expired_medicals[organisation_id]"
      puts "Example: rake reports:expired_medicals[1]"
      exit 1
    end

    organisation = Organisation.find_by(id: organisation_id)
    unless organisation
      puts "Organisation with ID #{organisation_id} not found"
      exit 1
    end

    puts "Expired Medicals Report for #{organisation.name}"
    puts "Generated on: #{Date.current.strftime('%d %B %Y')}"
    puts "=" * 80
    puts

    # Get all evaluations with expired medical_expiry_date
    expired_evaluations = Evaluation.joins(:patient, employment: :company)
      .where(patients: { organisation_id: organisation_id })
      .where('medical_expiry_date < ?', Date.current)
      .includes(:patient, employment: :company)
      .order('companies.name ASC, medical_expiry_date ASC')

    if expired_evaluations.empty?
      puts "No expired medicals found for this organisation."
      return
    end

    # Group by company
    grouped_by_company = expired_evaluations.group_by { |eval| eval.employment.company.name }

    grouped_by_company.each do |company_name, evaluations|
      puts "COMPANY: #{company_name}"
      puts "-" * 80
      puts "%-25s %-15s %-15s %-20s %-15s" % ["Full Name", "Contact Number", "ID Number", "Medical Ref", "Expired On"]
      puts "-" * 80

      evaluations.each do |evaluation|
        puts "%-25s %-15s %-15s %-20s %-15s" % [
          evaluation.patient.full_name.truncate(24),
          evaluation.patient.phone_number || "N/A",
          evaluation.patient.identification_number,
          evaluation.name.truncate(19),
          evaluation.medical_expiry_date.strftime('%d %b %Y')
        ]
      end

      puts
      puts "Total expired medicals for #{company_name}: #{evaluations.count}"
      puts
      puts "=" * 80
      puts
    end

    puts "SUMMARY"
    puts "-" * 80
    puts "Total companies with expired medicals: #{grouped_by_company.keys.count}"
    puts "Total expired medicals: #{expired_evaluations.count}"
    puts
  end

  desc "Generate expired medicals CSV report"
  task :expired_medicals_csv, [:organisation_id, :output_file] => :environment do |task, args|
    require 'csv'
    
    organisation_id = args[:organisation_id]
    output_file = args[:output_file] || "expired_medicals_#{Date.current.strftime('%Y%m%d')}.csv"
    
    if organisation_id.blank?
      puts "Usage: rake reports:expired_medicals_csv[organisation_id,output_file]"
      puts "Example: rake reports:expired_medicals_csv[1,expired_report.csv]"
      exit 1
    end

    organisation = Organisation.find_by(id: organisation_id)
    unless organisation
      puts "Organisation with ID #{organisation_id} not found"
      exit 1
    end

    # Get all evaluations with expired medical_expiry_date
    expired_evaluations = Evaluation.joins(:patient, employment: :company)
      .where(patients: { organisation_id: organisation_id })
      .where('medical_expiry_date < ?', Date.current)
      .includes(:patient, employment: :company)
      .order('companies.name ASC, medical_expiry_date ASC')

    if expired_evaluations.empty?
      puts "No expired medicals found for this organisation."
      return
    end

    CSV.open(output_file, 'w', write_headers: true, headers: [
      'Full Name', 'Contact Number', 'ID Number', 'Company', 'Medical Reference', 'Medical Expired On'
    ]) do |csv|
      expired_evaluations.each do |evaluation|
        csv << [
          evaluation.patient.full_name,
          evaluation.patient.phone_number,
          evaluation.patient.identification_number,
          evaluation.employment.company.name,
          evaluation.name,
          evaluation.medical_expiry_date.strftime('%Y-%m-%d')
        ]
      end
    end

    puts "CSV report generated: #{output_file}"
    puts "Total records: #{expired_evaluations.count}"
  end
end
