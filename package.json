{"name": "occumed", "private": true, "scripts": {"build": "yarn run build:js && yarn run build:css", "build:js": "esbuild app/javascript/*.* --bundle --sourcemap=inline --loader:.png=dataurl --platform=browser --outdir=app/assets/builds", "build:css": "tailwindcss -i ./app/assets/stylesheets/application.tailwind.css -o ./app/assets/builds/application.css", "prettier:check": "prettier-standard -- --check \"app/**/*.js\" \"config/**/*.js\" \"app/**/*.scss\"", "prettier:format": "prettier-standard -- \"app/**/*.js\" \"config/**/*.js\" \"app/**/*.scss\"", "prettier:lint": "prettier-standard -- --lint \"app/**/*.js\" \"config/**/*.js\" \"app/**/*.scss\""}, "dependencies": {"@hotwired/stimulus": "^3.2.1", "@hotwired/turbo-rails": "^7.1.0", "@rails/actioncable": "^7.0.0", "@rails/activestorage": "^7.0.3-1", "@tailwindcss/forms": "^0.4.0", "@tailwindcss/typography": "^0.4.0", "autoprefixer": "^10.4.1", "cable_ready": "^4.3.0", "debounced": "^0.0.5", "dropzone": "^5.7.2", "esbuild": "^0.14.10", "flatpickr": "^4.6.3", "nanoid": "^3.3.4", "postcss": "^8.4.5", "postcss-import": "^14.0.0", "postcss-loader": "^4.1.0", "puppeteer": "^23.0.0", "qrcode-with-logos": "^1.0.2", "resolve-url-loader": "^3.1.1", "signature_pad": "4.0.7", "stimulus": "^2.0.0", "stimulus-flatpickr": "^1.3.0", "stimulus_reflex": "3.5.0-pre8", "tailwindcss": "^3.0.8", "tailwindcss-stimulus-components": "^2.0.10"}, "devDependencies": {"prettier-standard": "^16.2.1"}}