# frozen_string_literal: true

require "rails_helper"

RSpec.describe AudioForm, type: :model do
  describe "Invalid Data" do
    it "does not correctly save" do
      patient = create(:patient)
      clinic = create(:clinic)

      data = {
        result: "ok",
        system_used: "MyStem",
        patient_id: patient.id,
        clinic_id: clinic.id
      }

      data[:attachments_attributes] = {"123":
                                      {content: uploaded_file}}

      audio = described_class.new(Audio.new(data))
      audio.save! if audio.validate(data)
      expect(audio.errors.count).to be(1)
    end
  end

  describe "Valid Data" do
    it "correctlies save" do
      patient = create(:patient)
      clinic = create(:clinic)

      data = {
        name: "jack",
        result: "ok",
        note: "someone",
        system_used: "MyStem",
        patient_id: patient.id,
        clinic_id: clinic.id
      }

      data[:attachments_attributes] = {"123":
                                      {content: uploaded_file}}

      audio = described_class.new(Audio.new(data))
      record = audio.save! if audio.validate(data)

      expect(record.system_used).to eql("MyStem")
      expect(record.attachments.count).to be(1)
    end

    it "correctly saves signed document" do
      patient = create(:patient)
      clinic = create(:clinic)

      data = {
        result: "asd",
        name: "asd",
        note: "someone",
        system_used: "MyStem",
        patient_id: patient.id,
        clinic_id: clinic.id,
        signed_by: "JACK",
        signature_date: Date.yesterday,
        signed: "1"
      }

      data[:attachments_attributes] = {"123":
                                      {content: uploaded_file}}

      audio = described_class.new(Audio.new(data))
      record = audio.save! if audio.validate(data)

      expect(record.signed_by).to eql("JACK")
      expect(record.signature_date).to eql(Date.yesterday)
    end

    it "correctly updates signed document" do
      patient = create(:patient)
      clinic = create(:clinic)

      data = {
        result: "asd",
        name: "asd",
        note: "someone",
        system_used: "MyStem",
        patient_id: patient.id,
        clinic_id: clinic.id,
        signed_by: "JACK",
        signature_date: Date.yesterday,
        signed: "1"
      }

      data[:attachments_attributes] = {"123":
                                      {content: uploaded_file}}

      audio = described_class.new(Audio.new(data))
      record_created = audio.save! if audio.validate(data)

      expect(record_created.signed_by).to eql("JACK")
      expect(record_created.signature_date).to eql(Date.yesterday)

      record_created.name = "xyz"
      updated_audio = described_class.new(record_created)
      updated_record = updated_audio.update! if updated_audio.validate(record_created.attributes)

      expect(updated_record.name).to eq("xyz")
    end
  end

  def uploaded_file(data = {})
    uploaded_file =
      Shrine.upload(file_fixture("sample.pdf").open, :store, metadata: false)
    uploaded_file.metadata.merge!(data)

    uploaded_file
  end
end
