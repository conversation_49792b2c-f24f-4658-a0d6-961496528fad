require "rails_helper"

RSpec.describe CaptureMedicalForm do
  describe "Valid Data" do
    it "saves correctly" do
      patient = create(:patient)
      clinic = create(:clinic)
      employment = create(:employment, patient: patient)

      data = {
        audio_performed: true,
        cannabis_performed: true,
        ecg_performed: true,
        heat_performed: false,
        height_performed: false,
        physical_exam_performed: true,
        vision_screening_performed: false,
        spiro_performed: true,
        visual_performed: true,
        xray_performed: true,

        exclusion_comment: "exclusion comment",
        exclusion: true,

        patient_id: patient.id,
        employment_id: employment.id,
        clinic_id: clinic.id,

        medical_type: "PRE_EMPLOYMENT",
        name: "123123",

        referral: true,
        referral_comment: "referral comment",

        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,

        outcome: "FIT",
        outcome_comment: ""
      }

      form = described_class.new(data)
      form.save
      form.errors

      expect(Evaluation.count).to be(1)
    end

    xit "[EXISTING COMPANY] saves correctly" do
      org = create(:organisation)
      company = create(:company, organisation_id: org.id)
      data = {
        first_name: "abc",
        last_name: "xyz",
        identification_number: "123",
        dob: Date.yesterday,
        gender: "male",

        employment_department: "general",
        employment_start_date: Date.today,
        employment_position: "manager",

        company_capture: "existing",
        company_search: {id: company.id},
        organisation_id: org.id
      }

      document = CapturePatientForm.new(data)
      record = document.save

      expect(Patient.count).to be(1)
      expect(Company.count).to be(1)
      expect(Employment.count).to be(1)
    end
  end

  describe "Invalid Data" do
    xit "[EXISTING COMPANY] does not save" do
      org = create(:organisation)
      company = create(:company, organisation_id: org.id)
      data = {
        first_name: "",
        last_name: "",
        identification_number: "123",
        dob: Date.yesterday,
        gender: "",

        employment_department: "general",
        employment_start_date: Date.today,
        employment_position: "manager",

        company_capture: "existing",
        company_search: {id: company.id},
        organisation_id: org.id
      }

      document = CapturePatientForm.new(data)
      record = document.save

      expect(Patient.count).to be(0)
      expect(Company.count).to be(1)
      expect(Employment.count).to be(0)
    end
  end
end
