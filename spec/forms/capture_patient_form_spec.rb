require "rails_helper"

RSpec.describe PatientDocumentForm, type: :model do
  describe "Valid Data" do
    it "[ADD COMPANY] saves correctly" do
      org = create(:organisation)

      data = {
        first_name: "abc",
        last_name: "xyz",
        identification_number: "123",
        dob: Date.yesterday,
        gender: "male",

        employment_department: "general",
        employment_start_date: Date.today,
        employment_position: "manager",

        company_capture: "add_company",
        company_input: "Tesla",
        organisation_id: org.id
      }

      document = CapturePatientForm.new(data)
      record = document.save

      expect(Patient.count).to be(1)
      expect(Employment.count).to be(1)
      expect(Company.count).to be(1)
    end

    it "[EXISTING COMPANY] saves correctly" do
      org = create(:organisation)
      company = create(:company, organisation_id: org.id)
      data = {
        first_name: "abc",
        last_name: "xyz",
        identification_number: "123",
        dob: Date.yesterday,
        gender: "male",

        employment_department: "general",
        employment_start_date: Date.today,
        employment_position: "manager",

        company_capture: "existing",
        company_search: {id: company.id},
        organisation_id: org.id
      }

      document = CapturePatientForm.new(data)
      record = document.save

      expect(Patient.count).to be(1)
      expect(Company.count).to be(1)
      expect(Employment.count).to be(1)
    end
  end

  describe "Invalid Data" do
    it "[EXISTING COMPANY] does not save" do
      org = create(:organisation)
      company = create(:company, organisation_id: org.id)
      data = {
        first_name: "",
        last_name: "",
        identification_number: "123",
        dob: Date.yesterday,
        gender: "",

        employment_department: "general",
        employment_start_date: Date.today,
        employment_position: "manager",

        company_capture: "existing",
        company_search: {id: company.id},
        organisation_id: org.id
      }

      document = CapturePatientForm.new(data)
      record = document.save

      expect(Patient.count).to be(0)
      expect(Company.count).to be(1)
      expect(Employment.count).to be(0)
    end
  end
end
