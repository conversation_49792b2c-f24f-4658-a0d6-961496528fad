# frozen_string_literal: true

require "rails_helper"

RSpec.describe EvaluationForm, type: :model do
  describe "Validation" do
    subject { described_class.new(Evaluation.new) }

    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:outcome) }
    it { is_expected.to validate_presence_of(:medical_type) }
    it { is_expected.to validate_presence_of(:employment_id) }
    it { is_expected.to validate_presence_of(:medical_expiry_date) }
    it { is_expected.to validate_presence_of(:medical_examination_date) }
  end

  describe "Exclusions" do
    it "correctly adds exclusions to exclusions" do
      patient = create(:patient)
      clinic = create(:clinic)
      employment = create(:employment, patient: patient)
      exclusion_1 = create(:exclusion, patient: patient)
      exclusion_2 = create(:exclusion, patient: patient)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        clinic_id: clinic.id,
        employment_id: employment.id,
        exclusion_ids: [exclusion_1.id, exclusion_2.id]
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(record.exclusions.count).to be(2)
    end
  end

  describe "Referrals" do
    it "correctly adds exclusions to exclusions" do
      patient = create(:patient)
      clinic = create(:clinic)
      employment = create(:employment, patient: patient)
      referral_1 = create(:referral, patient: patient)
      referral_2 = create(:referral, patient: patient)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        clinic_id: clinic.id,
        employment_id: employment.id,
        referral_ids: [referral_1.id, referral_2.id]
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(record.referrals.count).to be(2)
    end
  end

  describe "UPDATE" do
    it "correctly updates evaluation" do
      patient = create(:patient)
      clinic = create(:clinic)
      employment = create(:employment, patient: patient)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        clinic_id: clinic.id,
        employment_id: employment.id
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(record.name).to eql("abc123")

      record.name = "xyz789"

      updated_record = described_class.new(record)
      updated_record = updated_record.update! if updated_record.validate(record.attributes)

      expect(updated_record.name).to eq("xyz789")
    end
  end

  describe "Performed corectly checked for" do
    it "Lab Test" do
      patient = create(:patient)
      clinic = create(:clinic)
      employment = create(:employment, patient: patient)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        clinic_id: clinic.id,
        employment_id: employment.id,

        drug_checkbox: "on",
        drug_unlink: "1"
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(record.cannabis_performed).to be_truthy
    end

    it "Audio" do
      patient = create(:patient)
      clinic = create(:clinic)
      employment = create(:employment, patient: patient)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        clinic_id: clinic.id,
        employment_id: employment.id,

        audio_checkbox: "on",
        audio_unlink: "1"
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(record.audio_performed).to be_truthy
    end

    it "ECG" do
      patient = create(:patient)
      clinic = create(:clinic)
      employment = create(:employment, patient: patient)
      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        clinic_id: clinic.id,
        employment_id: employment.id,

        ecg_checkbox: "on",
        ecg_unlink: "1"
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(record.ecg_performed).to be_truthy
    end

    it "Heat Stress" do
      patient = create(:patient)
      clinic = create(:clinic)
      employment = create(:employment, patient: patient)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        clinic_id: clinic.id,
        employment_id: employment.id,

        heat_checkbox: "on",
        heat_unlink: "1"
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(record.heat_performed).to be_truthy
    end

    it "Height" do
      patient = create(:patient)
      clinic = create(:clinic)
      employment = create(:employment, patient: patient)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        clinic_id: clinic.id,
        employment_id: employment.id,

        height_checkbox: "on",
        height_unlink: "1"
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(record.height_performed).to be_truthy
    end

    it "Physical Exam" do
      patient = create(:patient)
      clinic = create(:clinic)
      employment = create(:employment, patient: patient)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        clinic_id: clinic.id,
        employment_id: employment.id,

        physical_exam_checkbox: "on",
        physical_exam_unlink: "1"
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(record.physical_exam_performed).to be_truthy
    end

    it "Spiro" do
      patient = create(:patient)
      clinic = create(:clinic)
      employment = create(:employment, patient: patient)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        clinic_id: clinic.id,
        employment_id: employment.id,

        spiro_checkbox: "on",
        spiro_unlink: "1"
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(record.spiro_performed).to be_truthy
    end

    it "Visual" do
      patient = create(:patient)
      clinic = create(:clinic)
      employment = create(:employment, patient: patient)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        clinic_id: clinic.id,
        employment_id: employment.id,

        visual_checkbox: "on",
        visual_unlink: "1"
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(record.visual_performed).to be_truthy
    end
  end

  describe "Validate the presence of Audio" do
    it "needed - if checkbox is ticked" do
      patient = create(:patient)
      audio = create(:audio)
      clinic = create(:clinic)
      employment = create(:employment, patient: patient)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        audio_checkbox: "on",
        audio_unlink: "",
        audio: nil,
        clinic_id: clinic.id,
        employment_id: employment.id
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(form.errors.messages).not_to be_empty
    end

    it "not needed - if checkbox is ticked, unlinked" do
      patient = create(:patient)
      audio = create(:audio)
      clinic = create(:clinic)
      employment = create(:employment, patient: patient)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        audio_checkbox: "on",
        audio_unlink: "1",
        audio: nil,
        clinic_id: clinic.id,
        employment_id: employment.id
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(form.errors.messages).to be_empty
    end
  end

  describe "Validate the presence of LabTest" do
    it "needed - if checkbox is ticked, linked" do
      patient = create(:patient)
      lab_test = create(:lab_test)
      clinic = create(:clinic)
      employment = create(:employment, patient: patient)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        drug_checkbox: "on",
        drug_unlink: "",
        drug: nil,
        clinic_id: clinic.id,
        employment_id: employment.id
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(form.errors.messages).not_to be_empty
    end

    it "not needed - if checkbox is ticked, unlinked" do
      patient = create(:patient)
      lab_test = create(:lab_test)
      clinic = create(:clinic)
      employment = create(:employment, patient: patient)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        drug_checkbox: "on",
        drug_unlink: "1",
        drug: nil,
        clinic_id: clinic.id,
        employment_id: employment.id
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(form.errors.messages).to be_empty
    end
  end

  describe "associations" do
    it "Audio associates with evaluation when checkbox ticked, linked" do
      patient = create(:patient)
      audio = create(:audio)
      employment = create(:employment, patient: patient)
      clinic = create(:clinic)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        audio_checkbox: "on",
        audio_unlink: "",
        audio: audio,
        clinic_id: clinic.id,
        employment_id: employment.id
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(record.audio.id).to eql(audio.id)
    end

    it "Audio doesn't associates with evaluation when checkbox ticked, unlinked" do
      patient = create(:patient)
      audio = create(:audio)
      employment = create(:employment, patient: patient)
      clinic = create(:clinic)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        audio_checkbox: "on",
        audio_unlink: "1",
        audio: audio,
        clinic_id: clinic.id,
        employment_id: employment.id
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(record.audio).to be_blank
    end

    it "Lab Test associates with evaluation when checkbox ticked, linked" do
      patient = create(:patient)
      lab_test = create(:lab_test)
      employment = create(:employment, patient: patient)
      clinic = create(:clinic)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        drug_checkbox: "on",
        drug_unlink: "",
        drug: lab_test,
        clinic_id: clinic.id,
        employment_id: employment.id
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(record.lab_test.id).to eql(lab_test.id)
    end

    it "Lab Test doesn't associates with evaluation when checkbox ticked, unlinked" do
      patient = create(:patient)
      lab_test = create(:lab_test)
      employment = create(:employment, patient: patient)
      clinic = create(:clinic)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        drug_checkbox: "on",
        drug_unlink: "1",
        drug: lab_test,
        clinic_id: clinic.id,
        employment_id: employment.id
      }

      form = described_class.new(Evaluation.new)
      record = form.save! if form.validate(data)

      expect(record.lab_test).to be_blank
    end
  end
end
