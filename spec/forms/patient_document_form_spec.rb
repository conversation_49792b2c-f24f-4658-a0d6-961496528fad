require "rails_helper"

RSpec.describe PatientDocumentForm, type: :model do
  describe "Invalid Data" do
    it "errors with invalid data" do
      patient = create(:patient)

      data = {
        name: "",
        uploaded_by: "name",
        description: "abc123",
        patient_id: patient.id
      }

      document = described_class.new(data)

      expect {
        document.save
      }.not_to change(PatientDocument, :count)

      expect(document.errors.count).to be_truthy
    end
  end

  describe "Valid Data" do
    it "saves correctly with 1 attachment" do
      patient = create(:patient)

      data = {
        name: "abc",
        description: "abc123",
        uploaded_by: "name",
        patient_id: patient.id,
        attachments: [uploaded_file]
      }

      document = described_class.new(data)
      record = {}

      expect {
        record = document.save
      }.to change(PatientDocument, :count).by(1)

      expect(record.attachments).to be_attached
      expect(record.description).to eq("abc123")
      expect(record.uploaded_by).to eq("name")
    end

    it "saves correctly with 2 attachment" do
      patient = create(:patient)

      data = {
        name: "abc",
        uploaded_by: "name",
        description: "abc123",
        patient_id: patient.id,
        attachments: [uploaded_file, uploaded_file]
      }

      document = described_class.new(data)
      record = {}

      expect {
        record = document.save
      }.to change(PatientDocument, :count).by(1)

      expect(record.description).to eq("abc123")
      expect(record.uploaded_by).to eq("name")
      expect(record.attachments.count).to be(2)
    end

    it "correctly updates patient document" do
      patient = create(:patient)

      data = {
        name: "abc",
        uploaded_by: "name",
        description: "abc123",
        patient_id: patient.id,
        attachments: [uploaded_file, uploaded_file]
      }

      document = described_class.new(data)
      record_created = document.save

      expect(record_created.uploaded_by).to eql("name")

      record_created.name = "xyz"

      updated_doc = described_class.new(record_created.attributes
                                            .merge!(attachments: [uploaded_file]))
      updated_record = updated_doc.update

      expect(updated_record.name).to eq("xyz")
      expect(updated_record.attachments.count).to be(3)
    end
  end

  def uploaded_file(data = {})
    ActionDispatch::Http::UploadedFile.new({
      filename: "sample.pdf",
      content_type: "application/pdf",
      tempfile: file_fixture("sample.pdf")
    })
  end
end
