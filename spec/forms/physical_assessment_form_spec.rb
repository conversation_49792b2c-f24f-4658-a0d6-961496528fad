# frozen_string_literal: true

require "rails_helper"

RSpec.describe PhysicalAssessmentForm, type: :model do
  describe "Validation" do
    xit { is_expected.to validate_presence_of(:height) }
    xit { is_expected.to validate_presence_of(:weight) }
    xit { is_expected.to validate_presence_of(:blood_pressure) }
    xit { is_expected.to validate_presence_of(:pulse) }
    xit { is_expected.to validate_presence_of(:body_mass_index) }
    xit { is_expected.to validate_presence_of(:blood_sugar) }
    xit { is_expected.to validate_presence_of(:appearance_and_exam_comment) }

    xit "Lab Test must be present if checkbox is ticked" do
      patient = create(:patient)
      lab_test = create(:lab_test)
      employment = create(:employment, patient: patient)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        drug_checkbox: "on",
        drug: "",
        employment: employment.id
      }

      form = EvaluationForm.new(data)

      form.save!

      expect(form.errors.messages).not_to be_empty
    end
  end

  describe "associations" do
    xit "Lab Test is correctly associated when checkbox is on" do
      patient = create(:patient)
      lab_test = create(:lab_test)
      employment = create(:employment, patient: patient)

      data = {
        name: "abc123",
        patient_id: patient.id,
        medical_type: ENUMS::EVALUATION_TYPES::PRE_EMPLOYMENT,
        outcome: ENUMS::OUTCOMES::FIT,
        medical_examination_date: Date.yesterday,
        medical_expiry_date: Date.tomorrow,
        drug_checkbox: "on",
        drug: lab_test.id,
        employment: employment
      }

      form = EvaluationForm.new(data)
      evalution = form.save!

      expect(evalution.lab_test.id).to eql(lab_test.id)
    end
  end
end
