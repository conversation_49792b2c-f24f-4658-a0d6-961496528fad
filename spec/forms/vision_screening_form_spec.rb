# frozen_string_literal: true

require "rails_helper"

RSpec.describe VisionScreeningForm, type: :model do
  describe "Validation" do
    it { is_expected.to validate_presence_of(:snellen_right_eye) }
    it { is_expected.to validate_presence_of(:snellen_left_eye) }
    it { is_expected.to validate_presence_of(:temporal_left) }
    it { is_expected.to validate_presence_of(:temporal_right) }
    it { is_expected.to validate_presence_of(:total_left) }
    it { is_expected.to validate_presence_of(:total_right) }
    it { is_expected.to validate_presence_of(:color_discrimination) }
    it { is_expected.to validate_presence_of(:date_of_screening) }
    it { is_expected.to validate_presence_of(:patient_id) }
    it { is_expected.to validate_presence_of(:performed_by) }
  end

  describe "with valid inputs" do
    xit "should correctly save" do
      patient = create(:patient)

      data = {
        snellen_left_eye: 1,
        snellen_right_eye: 1,
        temporal_right: 123,
        temporal_left: 123,
        total_right: 123,
        total_left: 123,
        color_discrimination: "0",
        comment: "All Good",
        performed_by: "someone",
        date_of_screening: Date.yesterday,
        patient_id: patient.id
      }

      vision = described_class.new(data)
      vision.save!

      expect(VisionScreening.count).to be(1)
      expect(VisionScreening.last.snellen_right_eye).to eql("1")
      expect(VisionScreening.last.color_discrimination).to eql("no")
    end
  end

  describe "With Invalid inputs" do
    xit "Should not save" do
      patient = create(:patient)

      data = {
        snellen_left_eye: "",
        snellen_right_eye: "",
        temporal_right: 123,
        temporal_left: 123,
        total_right: 123,
        total_left: 123,
        color_discrimination: "yes",
        comment: "All Good",
        performed_by: "someone",
        date_of_screening: Date.yesterday,
        patient_id: patient.id
      }

      vision = described_class.new(data)
      vision.save!

      expect(vision.errors.count).to be(2)
      expect(VisionScreening.count).to be(0)
    end
  end
end
