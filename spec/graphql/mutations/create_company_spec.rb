require "rails_helper"

RSpec.describe "mutation create company" do
  it "successfully" do
    org_user = create(:organisation_user)
    company = create(:company, organisation: org_user.organisation)

    result = OccumedSchema.execute(query, context: {requester: org_user.user}, variables: {
      organisationId: org_user.organisation.id,
      input: {
        name: "Tesla"
      }
    })

    expect(result["data"]["createCompany"]["company"]["name"]).to eql("Tesla")
    expect(result["errors"]).to be_nil
    expect(result["data"]["createCompany"]["success"]).to be_truthy
  end

  it "return an error when not valid" do
    org_user = create(:organisation_user)
    company = create(:company, organisation: org_user.organisation)

    result = OccumedSchema.execute(query, context: {requester: org_user.user}, variables: {
      organisationId: org_user.organisation.id,
      input: {}
    })

    expect(result["data"]["createCompany"]["success"]).to be_falsy
    expect(result["data"]["createCompany"]["company"]).to be_nil
    expect(result["data"]["createCompany"]["errors"][0]["path"]).to eql("name")
  end

  it "return an error when not found" do
    org_user = create(:organisation_user)
    result = OccumedSchema.execute(query, context: {requester: org_user.user}, variables: {
      organisationId: "not-exist",
      input: {
        name: "Tesla"
      }
    })

    expect(result["data"]["createCompany"]["success"]).to be_falsy
    expect(result["data"]["createCompany"]["patient"]).to be_nil
    expect(result["data"]["createCompany"]["errors"][0]["path"]).to eql("id")
  end

  private

  def query
    <<~GQL
      mutation CreateCompany($organisationId: ID!, $input: CompanyInput!) {
        createCompany(organisationId: $organisationId, input: $input) {
          errors {
            path
            message
          }
          company {
            name
            id
          }
          success
        }
      }  
    GQL
  end
end
