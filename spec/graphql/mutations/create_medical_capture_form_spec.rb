require "rails_helper"

RSpec.describe "CreateMedicalCaptureForm" do
  it "successfully" do
    patient = create(:patient)
    clinic = create(:clinic)
    employment = create(:employment, patient: patient)

    result = OccumedSchema.execute(query, variables: {
      input: {
        audioPerformed: true,
        cannabisPerformed: true,
        ecgPerformed: true,
        heatPerformed: false,
        heightPerformed: false,
        physicalExamPerformed: true,
        visionScreeningPerformed: false,
        spiroPerformed: true,
        xrayPerformed: true,

        exclusionComment: "exclusion comment",
        exclusion: true,

        patientId: patient.id,
        employmentId: employment.id,
        clinicId: clinic.id,

        medicalType: "PRE_EMPLOYMENT",
        name: "123123",

        referral: true,
        referralComment: "referral comment",

        medicalExaminationDate: Date.yesterday,
        medicalExpiryDate: Date.tomorrow,

        outcome: "FIT",
        outcomeComment: ""
      }
    })

    # expect(result["data"]["createMedicalCaptureForm"]["patient"]["lastName"]).to eql("xyz")
    expect(result["errors"]).to be_nil
    expect(result["data"]["createMedicalCaptureForm"]["success"]).to be_truthy
  end

  xit "return an error when not valid" do
    patient = create(:patient)
    clinic = create(:clinic)
    employment = create(:employment, patient: patient)

    result = OccumedSchema.execute(query, variables: {
      organisationId: org_user.organisation.id,
      input: {
        firstName: "abc",
        lastName: "xyz",
        identificationNumber: "qweqwe123",
        dob: Date.yesterday,
        gender: "",

        employmentDepartment: "general",
        employmentStartDate: Date.today,
        employmentPosition: "manager",

        companyCapture: "add_company",
        companyInput: "Tesla"
      }
    })

    expect(result["data"]["createPatientCaptureForm"]["success"]).to be_falsy
    expect(result["data"]["createPatientCaptureForm"]["patient"]).to be_nil
    expect(result["data"]["createPatientCaptureForm"]["errors"][0]["path"]).to eql("gender")
  end

  xit "return an error when not found" do
    result = OccumedSchema.execute(query, variables: {
      organisationId: "not-exist",
      input: {
        firstName: "abc",
        lastName: "xyz",
        identificationNumber: "qweqwe123",
        dob: Date.yesterday,
        gender: "male",

        employmentDepartment: "general",
        employmentStartDate: Date.today,
        employmentPosition: "manager",

        companyCapture: "add_company",
        companyInput: "Tesla"
      }
    })

    expect(result["data"]["createPatientCaptureForm"]["success"]).to be_falsy
    expect(result["data"]["createPatientCaptureForm"]["patient"]).to be_nil
    expect(result["data"]["createPatientCaptureForm"]["errors"][0]["path"]).to eql("id")
  end

  private

  def query
    <<~GQL
      mutation createMedical($input: MedicalCaptureFormInput!) {
        createMedicalCaptureForm(input: $input) {
          success
          errors {
            path
            message
          }
          medical {
            status
          }
        }
      } 
    GQL
  end
end
