require "rails_helper"

RSpec.describe "mutation create patient document" do
  it "successfully" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)

    result = OccumedSchema.execute(query, variables: {
      patientId: patient.id,
      input: {
        name: "Notes"
      }
    })

    expect(result["data"]["createPatientDocument"]["patientDocument"]["name"])
      .to eql("Notes")
    expect(result["errors"]).to be_nil
    expect(result["data"]["createPatientDocument"]["success"]).to be_truthy
  end

  it "return an error when not valid" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)

    result = OccumedSchema.execute(query, variables: {
      patientId: patient.id,
      input: {
        name: ""
      }
    })

    expect(result["data"]["createPatientDocument"]["success"]).to be_falsy
    expect(result["data"]["createPatientDocument"]["patientDocument"]).to be_nil
    expect(result["data"]["createPatientDocument"]["errors"][0]["path"]).to eql("name")
  end

  it "return an error when not found" do
    result = OccumedSchema.execute(query, variables: {
      patientId: "not-id",
      input: {
        name: "Notes"
      }
    })

    expect(result["data"]["createPatientDocument"]["success"]).to be_falsy
    expect(result["data"]["createPatientDocument"]["patientDocument"]).to be_nil
    expect(result["data"]["createPatientDocument"]["errors"][0]["path"]).to eql("id")
  end

  private

  def query
    <<~GQL
       mutation createPatientDocument($patientId: ID!, $input: PatientDocumentInput!) {
        createPatientDocument(patientId: $patientId, input: $input) {
          errors {
            message
            path
          }
          patientDocument{
            id
            name
            uploadedBy
          }
          success
        }
      } 
    GQL
  end
end
