require "rails_helper"

RSpec.describe "mutation delete patient" do
  it "successfully" do
    org_user = create(:organisation_user)
    company = create(:company, organisation: org_user.organisation)

    result = OccumedSchema.execute(query, variables: {
      id: company.id
    })

    expect(result["data"]["deleteCompany"]["company"]["id"]).to eql(company.id.to_s)
    expect(result["errors"]).to be_nil
    expect(result["data"]["deleteCompany"]["success"]).to be_truthy
  end

  it "return an error when not found" do
    result = OccumedSchema.execute(query, variables: {
      id: "not_id"
    })

    expect(result["data"]["deleteCompany"]["success"]).to be_falsy
    expect(result["data"]["deleteCompany"]["company"]).to be_nil
    expect(result["data"]["deleteCompany"]["errors"][0]["path"]).to eql("id")
  end

  private

  def query
    <<~GQL
      mutation deleteCompany($id: ID!) {
        deleteCompany(id: $id) {
          errors {
            path
            message
          }
          company {
            name
            id
          }
          success
        }
      }    
    GQL
  end
end
