require "rails_helper"

RSpec.describe "mutation update company" do
  it "successfully" do
    org_user = create(:organisation_user)
    company = create(:company, organisation: org_user.organisation)

    result = OccumedSchema.execute(query, variables: {
      id: company.id,
      input: {
        name: "<PERSON><PERSON>"
      }
    })

    expect(result["data"]["updateCompany"]["company"]["name"]).to eql("Tesla")
    expect(result["errors"]).to be_nil
    expect(result["data"]["updateCompany"]["success"]).to be_truthy
  end

  it "return an error when not valid" do
    org_user = create(:organisation_user)
    company = create(:company, organisation: org_user.organisation)

    result = OccumedSchema.execute(query, variables: {
      id: company.id,
      input: {
        name: ""
      }
    })

    expect(result["data"]["updateCompany"]["success"]).to be_falsy
    expect(result["data"]["updateCompany"]["company"]).to be_nil
    expect(result["data"]["updateCompany"]["errors"][0]["path"]).to eql("name")
  end

  it "return an error when not found" do
    result = OccumedSchema.execute(query, variables: {
      id: 1,
      input: {
        name: "<PERSON><PERSON>"
      }
    })

    expect(result["data"]["updateCompany"]["success"]).to be_falsy
    expect(result["data"]["updateCompany"]["company"]).to be_nil
    expect(result["data"]["updateCompany"]["errors"][0]["path"]).to eql("id")
  end

  private

  def query
    <<~GQL
      mutation updateCompany($id: ID!, $input:CompanyInput!){
        updateCompany(id: $id, input: $input){
          success
          company{
            id 
            name 
          }
          errors{
            path
            message
          }
        }
      }
    GQL
  end
end
