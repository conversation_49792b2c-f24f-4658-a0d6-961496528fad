require "rails_helper"

RSpec.describe "mutation update patient" do
  it "successfully" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)

    result = OccumedSchema.execute(query, variables: {
      id: patient.id,
      input: {
        lastName: "jacks"
      }
    })

    expect(result["data"]["updatePatient"]["patient"]["lastName"]).to eql("jacks")
    expect(result["errors"]).to be_nil
    expect(result["data"]["updatePatient"]["success"]).to be_truthy
  end

  it "return an error when not valid" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)

    result = OccumedSchema.execute(query, variables: {
      id: patient.id,
      input: {
        lastName: ""
      }
    })

    expect(result["data"]["updatePatient"]["success"]).to be_falsy
    expect(result["data"]["updatePatient"]["patient"]).to be_nil
    expect(result["data"]["updatePatient"]["errors"][0]["path"]).to eql("lastName")
  end

  it "return an error when not found" do
    result = OccumedSchema.execute(query, variables: {
      id: 1,
      input: {
        lastName: "jacks"
      }
    })

    expect(result["data"]["updatePatient"]["success"]).to be_falsy
    expect(result["data"]["updatePatient"]["patient"]).to be_nil
    expect(result["data"]["updatePatient"]["errors"][0]["path"]).to eql("id")
  end

  private

  def query
    <<~GQL
      mutation updatePatient($id: ID!, $input:PatientInput!){
        updatePatient(id: $id, input: $input){
          success
          patient{
            identificationNumber
            firstName
            lastName
          }
          errors{
            path
            message
          }
        }
      }
    GQL
  end
end
