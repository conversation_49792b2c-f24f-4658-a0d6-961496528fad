require "rails_helper"

RSpec.describe "query patient" do
  it "retrieves patients" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)

    result = OccumedSchema.execute(query, variables: {
      id: patient.id
    })

    expect(result.dig("data", "patient", "identificationNumber")).not_to be_blank
  end

  it "return an error when request fails" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)

    result = OccumedSchema.execute(query, variables: {
      id: 2
    })

    expect(result["data"]["patient"]).to be_nil
    expect(result["error"]).to be_nil
  end

  private

  def query
    <<~GQL
      query getPatient($id: ID!) {  
        patient(id: $id ) {
          identificationNumber
          firstName
          lastName
        }
      }
    GQL
  end
end
