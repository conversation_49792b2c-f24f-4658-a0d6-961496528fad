require "rails_helper"

RSpec.describe "query companys" do
  it "retrieves companys" do
    org_user = create(:organisation_user)
    org_user_2 = create(:organisation_user)

    company_1 = create(:company, organisation: org_user.organisation)
    company_2 = create(:company, organisation: org_user.organisation)
    company_3 = create(:company, organisation: org_user_2.organisation)

    result = OccumedSchema.execute(query, variables: {
      organisationId: org_user.organisation.id
    })

    expect(result.dig("data", "companies").size).to be(2)
  end

  it "return an error when request fails" do
    result = OccumedSchema.execute(query, variables: {
      organisationId: "not_id"
    })

    expect(result["data"]["companies"]).to be_empty
  end

  private

  def query
    <<~GQL
      query Companies($organisationId: ID!) {
        companies(organisationId: $organisationId) {
          name
        }
      }
    GQL
  end
end
