require "rails_helper"

RSpec.describe "query patients" do
  it "retrieves patients" do
    org_user = create(:organisation_user)
    org_user_2 = create(:organisation_user)

    patient_1 = create(:patient, organisation: org_user.organisation)
    patient_2 = create(:patient, organisation: org_user.organisation)
    patient_2 = create(:patient, organisation: org_user_2.organisation)

    result = OccumedSchema.execute(query, variables: {
      id: org_user.organisation.id
    })

    expect(result.dig("data", "patients").size).to be(2)
  end

  it "return an error when request fails" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)

    result = OccumedSchema.execute(query, variables: {
      id: "not_id"
    })

    expect(result["data"]["patients"]).to be_empty
  end

  private

  def query
    <<~GQL
      query listPatients($id: ID!){
          patients(organisationId: $id){
          id
          identificationNumber
        }
      }
    GQL
  end
end
