# == Schema Information
#
# Table name: assessment_reports
#
#  id              :bigint           not null, primary key
#  assessable_type :string           not null
#  date_generated  :date
#  generated_by    :string
#  report_data     :text
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  assessable_id   :bigint           not null
#
# Indexes
#
#  index_assessment_reports_on_assessable_type_and_assessable_id  (assessable_type,assessable_id)
#
require "rails_helper"

describe "AssessmentReport" do
  describe "validations" do
    subject { build(:assessment_report) }

    it { is_expected.to validate_presence_of(:date_generated) }
    it { is_expected.to validate_presence_of(:report) }
    it { is_expected.to validate_presence_of(:generated_by) }
  end

  describe "associations" do
    subject { build(:assessment_report) }

    it { is_expected.to belong_to(:assessable) }
  end

  describe "reports" do
    it "attaches a PDF" do
      lab_test = build_stubbed(:lab_test)

      expect {
        lab_test.assessment_reports.create!(
          report: uploaded_file,
          date_generated: Date.today,
          generated_by: "user"
        )
      }.to change(AssessmentReport, :count).by(1)
    end
  end

  private

  def uploaded_file(data = {})
    ActionDispatch::Http::UploadedFile.new({
      filename: "signature.svg",
      content_type: "image/svg+xml",
      tempfile: file_fixture("signature.svg")
    })
  end
end
