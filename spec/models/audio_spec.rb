# frozen_string_literal: true

# == Schema Information
#
# Table name: audios
#
#  id             :bigint           not null, primary key
#  date_performed :date
#  name           :string
#  note           :text
#  performed_by   :string
#  result         :string
#  signature_date :date
#  signed_by      :text
#  status         :text
#  system_used    :text
#  created_at     :datetime         not null
#  updated_at     :datetime         not null
#  clinic_id      :bigint
#  patient_id     :bigint
#
# Indexes
#
#  index_audios_on_clinic_id   (clinic_id)
#  index_audios_on_patient_id  (patient_id)
#
require "rails_helper"

RSpec.describe Audio, type: :model do
  it "#delete" do
    audio = create(:audio)
    expect {
      audio.destroy
    }.to change(described_class, :count).by(-1)
  end
end
