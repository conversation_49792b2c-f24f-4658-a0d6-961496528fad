# == Schema Information
#
# Table name: companies
#
#  id              :bigint           not null, primary key
#  about           :string
#  city            :string
#  email           :string
#  industry_sector :string
#  name            :string
#  phone_number    :string
#  street_address  :string
#  suburb          :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  organisation_id :bigint           default(1), not null
#
# Indexes
#
#  index_companies_on_organisation_id  (organisation_id)
#
# Foreign Keys
#
#  fk_rails_...  (organisation_id => organisations.id)
#
require "rails_helper"

describe "Company" do
  describe "validations" do
    subject { build(:company) }

    it { is_expected.to validate_presence_of(:name) }
  end

  describe "associations" do
    subject { build(:company) }

    it { is_expected.to have_many(:employees) }
    it { is_expected.to have_many(:employments) }
    it { is_expected.to belong_to(:organisation) }
  end
end
