# frozen_string_literal: true

# == Schema Information
#
# Table name: evaluations
#
#  id                       :bigint           not null, primary key
#  audio_performed          :boolean          default(FALSE), not null
#  cannabis_performed       :boolean          default(FALSE), not null
#  ecg_performed            :boolean          default(FALSE), not null
#  exclusion                :boolean          default(FALSE), not null
#  exclusion_comment        :string
#  heat_performed           :boolean          default(FALSE), not null
#  height_performed         :boolean          default(FALSE), not null
#  medical_examination_date :date
#  medical_expiry_date      :date
#  medical_type             :string
#  name                     :string
#  outcome                  :text
#  outcome_comment          :text
#  physical_exam_performed  :boolean          default(FALSE), not null
#  referral                 :boolean
#  referral_comment         :string
#  spiro_performed          :boolean          default(FALSE), not null
#  status                   :string
#  visual_performed         :boolean          default(FALSE), not null
#  xray_performed           :boolean          default(FALSE), not null
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#  audio_id                 :bigint
#  clinic_id                :bigint
#  employment_id            :bigint
#  lab_test_id              :bigint
#  patient_id               :bigint           not null
#
# Indexes
#
#  index_evaluations_on_audio_id       (audio_id)
#  index_evaluations_on_clinic_id      (clinic_id)
#  index_evaluations_on_employment_id  (employment_id)
#  index_evaluations_on_lab_test_id    (lab_test_id)
#  index_evaluations_on_patient_id     (patient_id)
#
# Foreign Keys
#
#  fk_rails_...  (clinic_id => clinics.id)
#  fk_rails_...  (employment_id => employments.id)
#  fk_rails_...  (lab_test_id => lab_tests.id)
#  fk_rails_...  (patient_id => patients.id)
#
require "rails_helper"

RSpec.describe Evaluation do
  describe "Validation" do
    subject { build(:evaluation) }

    it { is_expected.to validate_uniqueness_of(:name) }

    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:outcome) }
    it { is_expected.to validate_presence_of(:medical_type) }
    it { is_expected.to validate_presence_of(:employment) }
    it { is_expected.to validate_presence_of(:medical_expiry_date) }
    it { is_expected.to validate_presence_of(:medical_examination_date) }
  end

  describe "DESTROY" do
    context "success" do
      it "deletes an evaluation" do
        evaluation = create(:evaluation)
        expect {
          evaluation.destroy
        }.to change(described_class, :count).by(-1)
      end

      it "deletes a signed_off evaluation" do
        signoff = create(:evaluation_signoff)
        # binding.pry
        expect {
          signoff.evaluation.destroy
        }.to change(described_class, :count).by(-1)
      end
    end
  end
end
