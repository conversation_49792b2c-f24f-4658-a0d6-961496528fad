# == Schema Information
#
# Table name: patient_documents
#
#  id          :bigint           not null, primary key
#  description :text
#  name        :string
#  uploaded_by :string
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  patient_id  :bigint           not null
#
# Indexes
#
#  index_patient_documents_on_patient_id  (patient_id)
#
# Foreign Keys
#
#  fk_rails_...  (patient_id => patients.id)
#
require "rails_helper"

RSpec.describe PatientDocument, type: :model do
  it "#delete" do
    doc = create(:patient_document_with_attachment)
    expect {
      doc.destroy
    }.to change(described_class, :count).by(-1)
  end
end
