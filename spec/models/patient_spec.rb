# == Schema Information
#
# Table name: patients
#
#  id                    :bigint           not null, primary key
#  dob                   :date
#  email                 :string
#  first_name            :string
#  gender                :string
#  identification_number :string
#  last_name             :string
#  phone_number          :string
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#  organisation_id       :bigint           default(1), not null
#
# Indexes
#
#  index_patients_on_organisation_id  (organisation_id)
#
# Foreign Keys
#
#  fk_rails_...  (organisation_id => organisations.id)
#
require "rails_helper"

RSpec.describe "Patient" do
  describe "validations" do
    subject { build(:patient) }

    it { is_expected.to validate_presence_of(:first_name) }
    it { is_expected.to validate_presence_of(:last_name) }
    it { is_expected.to validate_presence_of(:dob) }
    it { is_expected.to validate_presence_of(:gender) }
    it { is_expected.to validate_presence_of(:identification_number) }

    it { is_expected.to validate_uniqueness_of(:identification_number).case_insensitive }
  end

  describe "associations" do
    subject { build(:patient) }

    it { is_expected.to have_many(:evaluations) }
    it { is_expected.to have_many(:patient_notes) }
    it { is_expected.to have_many(:exclusions) }
    it { is_expected.to have_many(:referrals) }
    it { is_expected.to have_many(:assessments) }
    it { is_expected.to have_many(:employments) }
    it { is_expected.to have_many(:employers).through(:employments).source(:company) }

    it { is_expected.to belong_to(:organisation) }
  end
end
