require "rails_helper"

RSpec.describe "RegistrationsController", type: :request do
  xit "adds last and first name to the user" do
    expect {
      post user_registration_path, params: {user: valid_params}
    }.to change(User, :count).by(1)

    expect(User.last.first_name).to eq("FIRST_NAME")
    expect(User.last.last_name).to eq("LAST_NAME")
  end

  xit "creates an organisation" do
    expect {
      post user_registration_path, params: {user: valid_params}
    }.to change(Organisation, :count).by(1)

    expect(Organisation.last.organisation_name).to eq("ORGName")
  end

  xit "creates an organisation using user name if organisation name is blank" do
    expect {
      post user_registration_path,
        params: {user: valid_params.merge!(organisation_name: "")}
    }.to change(Organisation, :count).by(1)

    expect(Organisation.last.organisation_name).to eq("LAST_NAME Organisation")
  end

  xit "creates a clinic with a default name" do
    expect {
      post user_registration_path, params: {user: valid_params}
    }.to change(Clinic, :count).by(1)

    expect(Clinic.last.clinic_name).to eq("<PERSON><PERSON><PERSON><PERSON>'s clinic")
  end

  private

  def valid_params
    {
      email: "<EMAIL>",
      password: "password",
      password_confirmation: "password",
      first_name: "FIRST_NAME",
      last_name: "LAST_NAME",
      organisation_name: "ORGName"
    }
  end
end
