module TestData
  module_function

  def image_data
    attacher = Shrine::Attacher.new
    attacher.set(uploaded_image)
    attacher.column_data
  end

  def pdf_data
    attacher = Shrine::Attacher.new
    attacher.set(uploaded_pdf)
    attacher.column_data
  end

  def uploaded_image
    file =
      File.open(
        Rails.root.join("spec/fixtures/files/image.jpeg").to_s,
        binmode: true
      )

    # for performance we skip metadata extraction and assign test metadata
    uploaded_file = Shrine.upload(file, :store, metadata: false)
    uploaded_file.metadata.merge!(
      "size" => File.size(file.path),
      "mime_type" => "image/jpeg",
      "filename" => "image.jpeg"
    )

    uploaded_file
  end

  def uploaded_pdf
    file =
      File.open(
        Rails.root.join("spec/fixtures/files/sample.pdf").to_s,
        binmode: true
      )

    # for performance we skip metadata extraction and assign test metadata
    uploaded_file = Shrine.upload(file, :store, metadata: false)
    uploaded_file.metadata.merge!(
      "size" => File.size(file.path),
      "mime_type" => "application/pdf",
      "filename" => "sample.pdf"
    )

    uploaded_file
  end
end
