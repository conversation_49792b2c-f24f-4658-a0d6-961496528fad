require "rails_helper"

describe "Create Audio" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it("with valid inputs") do
    org_user = create(:organisation_user)
    sign_in org_user.user

    patient = create(:patient, organisation: org_user.organisation)
    clinic = create(:clinic, organisation: org_user.organisation)

    visit new_patient_audio_path(patient)

    fill_in "audio[name]", with: "name"
    fill_in "audio[note]", with: "note"
    fill_in "audio[result]", with: "result"
    fill_in "audio[performed_by]", with: "person"
    fill_in "audio[system_used]", with: "system"
    select clinic.clinic_name, from: "audio[clinic_id]"

    drop_in_dropzone("audio[attachments_attributes][123][content]", file_fixture("sample.pdf"))

    click_button "commit"
    expect(page).to have_text "Audiometry assessment was successfully created"
  end

  it("with invalid inputs") do
    org_user = create(:organisation_user)
    sign_in org_user.user

    patient = create(:patient)

    visit new_patient_audio_path(patient)
    click_button "commit"

    expect(page).to have_text "Name can't be blank"
  end

  def drop_in_dropzone(name, file)
    uploaded_file = Shrine.upload(file.open, :cache, metadata: false)
    data = {id: uploaded_file.id, storage: "cache", metadata: {}}
    page.execute_script <<-JS
      const hiddenField = document.createElement('input')

      hiddenField.id = 'test-file-upload'
      hiddenField.type = 'hidden'
      hiddenField.name = `#{name}` 
      hiddenField.value = `#{data.to_json}` 

      document.querySelector('form').appendChild(hiddenField)
    JS
  end
end
