require "rails_helper"

describe "Delete Audio" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it "successfully deletes" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)
    clinic = create(:clinic, organisation: org_user.organisation)
    audio = create(:audio, clinic: clinic, patient: patient)

    sign_in org_user.user

    visit edit_audio_path(audio)

    accept_confirm do
      click_link "Delete"
    end

    expect(page).to have_text "Audiometry assessment was successfully deleted"
  end
end
