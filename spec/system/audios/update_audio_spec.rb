require "rails_helper"

describe "Update Audio" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it "successfully updates" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)
    clinic = create(:clinic, organisation: org_user.organisation)
    audio = create(:audio_with_attachment, clinic: clinic, patient: patient)

    sign_in org_user.user

    visit edit_audio_path(audio)

    fill_in "audio[result]", with: "Good Result"

    click_button "commit"

    expect(page).to have_text "Good Result"
  end
end
