require "rails_helper"

describe "User creates a company" do
  before do
    driven_by(:rack_test)
  end

  it "with valid inputs" do
    org_user = create(:organisation_user)
    user = org_user.user
    organisation = org_user.organisation

    sign_in user

    visit new_organisation_company_path(organisation)
    fill_in "company_form_name", with: "company"
    fill_in "company_form_phone_number", with: "telephone"
    fill_in "company_form_email", with: "<EMAIL>"
    fill_in "company_form_industry_sector", with: "sector"
    fill_in "company_form_about", with: "about"
    fill_in "company_form_street_address", with: "street address"
    fill_in "company_form_suburb", with: "suburb"
    fill_in "company_form_city", with: "city"
    click_button "Save"

    expect(page).to have_text "Company was successfully created"
    expect(organisation.companies.last.attributes).to include(
      "name" => "company",
      "phone_number" => "telephone",
      "email" => "<EMAIL>",
      "industry_sector" => "sector",
      "about" => "about",
      "street_address" => "street address",
      "suburb" => "suburb",
      "city" => "city"
    )
  end

  it "shows errors on invalid data" do
    user = create(:organisation_user)
    sign_in user.user

    visit new_organisation_company_path(user.organisation)

    click_button "Save"
    expect(page).to have_text("Name can't be blank")
  end
end
