require "rails_helper"

describe "Create Employment" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it "with invalid input" do
    org_user = create(:organisation_user)
    sign_in org_user.user
    patient = create(:patient, organisation: org_user.organisation)

    visit new_patient_employment_path(patient)
    click_button "commit"

    expect(page).to have_text "Company must exist"
  end

  it "with valid input" do
    org_user = create(:organisation_user)
    sign_in org_user.user

    patient = create(:patient, organisation: org_user.organisation)
    company = create(:company, organisation: org_user.organisation)

    visit new_patient_employment_path(patient)

    fill_in "employment[department]", with: "department"
    fill_in "employment[position]", with: "position"
    select "Employee", from: "employment[employment_type]"
    select company.name, from: "employment[company_id]"
    click_button "commit"

    expect(page).to have_text "Employment was successfully created"
  end
end
