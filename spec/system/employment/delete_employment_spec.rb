require "rails_helper"

describe "Delete Employment" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it "successfully deletes" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)
    company = create(:company, organisation: org_user.organisation)
    employment = create(:employment, company: company, patient: patient)

    sign_in org_user.user
    visit edit_employment_path(employment)

    accept_confirm do
      click_link "Delete"
    end

    expect(page).to have_text "Employment successfully deleted"
  end
end
