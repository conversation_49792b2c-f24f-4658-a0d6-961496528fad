require "rails_helper"

describe "Update Employment" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it "with valid inputs - successfully updates" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)
    company = create(:company, organisation: org_user.organisation)
    employment = create(:employment, company: company, patient: patient)

    sign_in org_user.user
    visit edit_employment_path(employment)

    fill_in "employment[department]", with: "other department"

    click_button "commit"

    expect(page).to have_text "Employment was successfully updated"
  end

  it "with invalid inputs - display errors" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)
    company = create(:company, organisation: org_user.organisation)
    employment = create(:employment, company: company, patient: patient)

    sign_in org_user.user
    visit edit_employment_path(employment)

    fill_in "employment[department]", with: "other department"
    select "Select company", from: "employment[company_id]"
    click_button "commit"

    expect(page).to have_text "Company must exist"
  end
end
