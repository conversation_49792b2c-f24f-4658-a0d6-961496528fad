require "rails_helper"

describe "Create Audio" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it("with invalid inputs") do
    org_user = create(:organisation_user)
    sign_in org_user.user

    patient = create(:patient)
    clinic = create(:clinic, organisation: org_user.organisation)

    visit new_patient_evaluation_path(patient)
    click_button "commit"

    expect(page).to have_text "Clinic can't be blank"
  end

  it("with valid inputs - all tests unlinked") do
    org_user = create(:organisation_user)
    sign_in org_user.user

    patient = create(:patient, organisation: org_user.organisation)
    clinic = create(:clinic, organisation: org_user.organisation)
    employment = create(:employment, patient: patient)

    visit new_patient_evaluation_path(patient)

    select clinic.clinic_name, from: "evaluation_form[clinic_id]"
    fill_in "evaluation_form[name]", with: "name"
    select ENUMS::EVALUATION_TYPES::ANNUAL, from: "evaluation_form[medical_type]"

    check("evaluation_form[physical_exam_checkbox]")
    check("evaluation_form[physical_exam_unlink]")

    check("evaluation_form[visual_checkbox]")
    check("evaluation_form[visual_unlink]")

    check("evaluation_form[audio_checkbox]")
    check("evaluation_form[audio_unlink]")

    check("evaluation_form[spiro_checkbox]")
    check("evaluation_form[spiro_unlink]")

    check("evaluation_form[drug_checkbox]")
    check("evaluation_form[drug_unlink]")

    select ENUMS::OUTCOMES::FIT, from: "evaluation_form[outcome]"

    click_button "commit"
    expect(page).to have_text "Certificate was successfully created"
  end
end
