require "rails_helper"

describe "Update Evaluation" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it("successfully deletes") do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)
    company = create(:company, organisation: org_user.organisation)
    clinic = create(:clinic, organisation: org_user.organisation)
    employment = create(:employment, company: company, patient: patient)
    evaluation = create(:evaluation,
      patient: patient,
      clinic: clinic,
      employment: employment)

    sign_in org_user.user
    visit evaluation_path(evaluation)

    click_link "Delete Medical"

    expect(page).to have_text "Medical successfully deleted"
  end
end
