require "rails_helper"

describe "Update Evaluation" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it("with invalid inputs") do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)
    company = create(:company, organisation: org_user.organisation)
    clinic = create(:clinic, organisation: org_user.organisation)
    employment = create(:employment, company: company, patient: patient)
    evaluation = create(:evaluation,
      patient: patient,
      clinic: clinic,
      employment: employment)

    sign_in org_user.user
    visit edit_evaluation_path(evaluation)

    fill_in "evaluation_form[name]", with: ""

    click_button "commit"

    expect(page).to have_text "errors prohibited"
  end

  it("with valid inputs - all tests unlinked") do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)
    company = create(:company, organisation: org_user.organisation)
    clinic = create(:clinic, organisation: org_user.organisation)
    employment = create(:employment, company: company, patient: patient)
    evaluation = create(:evaluation,
      patient: patient,
      clinic: clinic,
      employment: employment)

    sign_in org_user.user
    visit edit_evaluation_path(evaluation)

    fill_in "evaluation_form[name]", with: "123123xxx"

    check("evaluation_form[physical_exam_checkbox]")
    check("evaluation_form[physical_exam_unlink]")

    check("evaluation_form[visual_checkbox]")
    check("evaluation_form[visual_unlink]")

    check("evaluation_form[audio_checkbox]")
    check("evaluation_form[audio_unlink]")

    check("evaluation_form[spiro_checkbox]")
    check("evaluation_form[spiro_unlink]")

    check("evaluation_form[drug_checkbox]")
    check("evaluation_form[drug_unlink]")

    click_button "commit"
    expect(page).to have_text "Certificate was successfully updated"
  end
end
