require "rails_helper"

describe "Create Exclusion" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it("with valid inputs - successfully") do
    org_user = create(:organisation_user)
    sign_in org_user.user

    patient = create(:patient, organisation: org_user.organisation)

    visit new_patient_exclusion_path(patient)

    fill_in "exclusion[category]", with: "category"
    fill_in "exclusion[note]", with: "note"
    click_button "commit"

    expect(page).to have_text "Exclusion was successfully created"
  end

  it("with invalid inputs - fails") do
    org_user = create(:organisation_user)
    sign_in org_user.user

    patient = create(:patient, organisation: org_user.organisation)

    visit new_patient_exclusion_path(patient)

    click_button "commit"

    expect(page).to have_text "Note can't be blank"
  end
end
