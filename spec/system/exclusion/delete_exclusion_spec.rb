require "rails_helper"

describe "Delete Exclusion" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it "successfully deletes" do
    org_user = create(:organisation_user)
    sign_in org_user.user
    patient = create(:patient, organisation: org_user.organisation)
    exclusion = create(:exclusion, patient: patient)

    visit edit_exclusion_path(exclusion)

    accept_confirm do
      click_link "Delete"
    end

    expect(page).to have_text "Exclusion was successfully deleted"
  end
end
