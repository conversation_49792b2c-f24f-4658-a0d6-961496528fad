require "rails_helper"

describe "Create Patient Document" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it("with valid inputs - 1 Attachment") do
    org_user = create(:organisation_user)
    sign_in org_user.user

    patient = create(:patient, organisation: org_user.organisation)

    visit new_patient_patient_document_path(patient)

    fill_in "patient_document[name]", with: "name"
    drop_in_dropzone("patient_document[attachments_attributes][123][content]", file_fixture("sample.pdf"))

    click_button "commit"
    expect(page).to have_text "Patient Document was successfully created"
  end

  it("with valid inputs - 2 Attachment") do
    org_user = create(:organisation_user)
    sign_in org_user.user

    patient = create(:patient, organisation: org_user.organisation)

    visit new_patient_patient_document_path(patient)

    fill_in "patient_document[name]", with: "name"
    drop_in_dropzone("patient_document[attachments_attributes][123][content]", file_fixture("sample.pdf"))
    drop_in_dropzone("patient_document[attachments_attributes][567][content]", file_fixture("sample.pdf"))

    click_button "commit"
    expect(page).to have_text "Patient Document was successfully created"
  end

  it("with invalid inputs") do
    org_user = create(:organisation_user)
    sign_in org_user.user

    patient = create(:patient)

    visit new_patient_audio_path(patient)
    click_button "commit"

    expect(page).to have_text "Attachments can't be blank"
  end

  def drop_in_dropzone(name, file)
    uploaded_file = Shrine.upload(file.open, :cache, metadata: false)
    data = {id: uploaded_file.id, storage: "cache", metadata: {}}
    page.execute_script <<-JS
      const hiddenField = document.createElement('input')

      hiddenField.id = 'test-file-upload'
      hiddenField.type = 'hidden'
      hiddenField.name = `#{name}` 
      hiddenField.value = `#{data.to_json}` 

      document.querySelector('form').appendChild(hiddenField)
    JS
  end
end
