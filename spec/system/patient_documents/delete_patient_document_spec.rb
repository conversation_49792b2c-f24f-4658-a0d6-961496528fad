require "rails_helper"

describe "Delete Patient Document" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it "successfully deletes" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)
    patient_doc = create(:patient_document_with_attachment, name: "name", patient: patient)

    sign_in org_user.user

    visit edit_patient_document_path(patient_doc)

    accept_confirm do
      click_link "Delete"
    end

    expect(page).to have_text "Patient Document was successfully deleted"
  end
end
