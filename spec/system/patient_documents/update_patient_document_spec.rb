require "rails_helper"

describe "Update Patient Document" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it "successfully updates" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)
    patient_doc = create(:patient_document_with_attachment, name: "name", patient: patient)

    sign_in org_user.user

    visit edit_patient_document_path(patient_doc)

    fill_in "patient_document[name]", with: "good name"

    click_button "commit"

    expect(page).to have_text "Patient Document was successfully updated."
    expect(patient_doc.reload.name).to eql("good name")
  end
end
