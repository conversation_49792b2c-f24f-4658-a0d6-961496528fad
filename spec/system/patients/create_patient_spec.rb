require "rails_helper"

describe "User creates a patient" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it "user does not create a patient with invalid data" do
    user = create(:organisation_user)
    sign_in user.user

    visit new_organisation_patient_path(user.organisation)
    click_button "commit"

    expect(page).to have_text "First name can't be blank"
    expect(page).to have_text "Last name can't be blank"
    expect(page).to have_text "Identification number can't be blank"
  end

  it "user creates a patient with valid data" do
    user = create(:organisation_user)
    sign_in user.user

    visit new_organisation_patient_path(user.organisation)

    fill_in "patient[first_name]", with: "test"
    fill_in "patient[last_name]", with: "tester"
    fill_in "patient_dob", with: "2022/01/01"
    fill_in "patient[identification_number]", with: "212345"
    select "Male", from: "patient[gender]"

    click_button "commit"

    expect(Patient.count).to eq(1)
  end
end
