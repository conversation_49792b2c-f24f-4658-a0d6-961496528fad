require "rails_helper"

describe "Update Patient" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it "successfully updates" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)

    sign_in org_user.user
    visit edit_patient_path(patient)
    fill_in "patient[identification_number]", with: "abc123xyz"
    select "Male", from: "patient[gender]"

    click_button "commit"

    expect(page).to have_text "Patient details was successfully updated"
    expect(page).to have_text "abc123xyz"
  end
end
