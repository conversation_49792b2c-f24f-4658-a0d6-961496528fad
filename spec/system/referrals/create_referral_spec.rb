require "rails_helper"

describe "Create Referral" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it("with valid inputs - successfully") do
    org_user = create(:organisation_user)
    sign_in org_user.user

    patient = create(:patient, organisation: org_user.organisation)

    visit new_patient_referral_path(patient)

    fill_in "referral[attention]", with: "attention"
    fill_in "referral[medical_centre]", with: "medical_centre"
    fill_in "referral[address]", with: "address"
    fill_in "referral[specialist_type]", with: "specialist_type"
    fill_in "referral[issues]", with: "issues"
    click_button "commit"

    expect(page).to have_text "Referral was successfully created"
  end

  it("with invalid inputs - fails") do
    org_user = create(:organisation_user)
    sign_in org_user.user

    patient = create(:patient, organisation: org_user.organisation)

    visit new_patient_referral_path(patient)

    click_button "commit"

    expect(page).to have_text "Issues can't be blank"
  end
end
