require "rails_helper"

describe "Update Referral" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it("with valid inputs - successfully") do
    org_user = create(:organisation_user)
    sign_in org_user.user

    patient = create(:patient, organisation: org_user.organisation)
    referral = create(:referral, patient: patient)

    visit edit_referral_path(referral)

    fill_in "referral[issues]", with: "issues"
    click_button "commit"

    expect(page).to have_text "Referral was successfully updated"
  end

  it("with invalid inputs - fails") do
    org_user = create(:organisation_user)
    sign_in org_user.user

    patient = create(:patient, organisation: org_user.organisation)
    referral = create(:referral, patient: patient)

    visit edit_referral_path(referral)

    fill_in "referral[issues]", with: ""
    click_button "commit"

    expect(page).to have_text "Issues can't be blank"
  end
end
