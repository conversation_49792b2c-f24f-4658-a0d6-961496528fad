require "rails_helper"

describe "User login" do
  before do
    @user =
      create(
        :user,
        email: "<EMAIL>",
        password: "PASSWORD",
        password_confirmation: "PASSWORD"
      )

    driven_by :selenium_chrome_headless
  end

  it "logs you in successfull with valid credentials" do
    visit new_user_session_path

    fill_in "user[email]", with: "<EMAIL>"
    fill_in "user[password]", with: "PASSWORD"

    click_on "commit"

    expect(page).to have_current_path root_path
  end

  xit "will not log you in with invalid credentials" do
    visit new_user_session_path

    fill_in "user[email]", with: "<EMAIL>"
    fill_in "user[password]", with: "NOT_PASSWORD"

    click_on "commit"

    expect(page).to have_text "Invalid Email or password"
    expect(page).to have_current_path new_user_session_path
  end

  it "logs you into your only organisation choice screen if have one organsiation" do
    marian_org = create(:organisation, organisation_name: "MarianMed")
    create(:organisation_user, user: @user, organisation: marian_org)

    visit new_user_session_path

    fill_in "user[email]", with: "<EMAIL>"
    fill_in "user[password]", with: "PASSWORD"

    click_on "commit"

    expect(page).to have_text "MarianMed"
  end

  it "logs you into your default organisation choice screen if have multiple organsiation" do
    carewell_org = create(:organisation, organisation_name: "CareWell")
    marian_org = create(:organisation, organisation_name: "MarianMed")
    create(:organisation_user, user: @user, organisation: carewell_org)
    create(:organisation_user, user: @user, organisation: marian_org)
    @user.update(default_organisation_id: carewell_org.id)

    visit new_user_session_path

    fill_in "user[email]", with: "<EMAIL>"
    fill_in "user[password]", with: "PASSWORD"

    click_on "commit"

    expect(page).to have_text "CareWell"
  end
end
