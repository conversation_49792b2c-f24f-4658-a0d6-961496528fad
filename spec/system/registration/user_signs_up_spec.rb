require "rails_helper"

describe "User sign up" do
  xit "User signs up with valid info" do
    driven_by :selenium_chrome_headless

    visit new_user_registration_path

    fill_in "First Name", with: "name"
    fill_in "Last Name", with: "surname"
    fill_in "Email", with: "<EMAIL>"
    fill_in "user[password]", with: "password"
    fill_in "user[password_confirmation]", with: "password"
    fill_in "user[organisation_name]", with: "org name"

    expect { click_on "commit" }.to change {
      ActionMailer::Base.deliveries.count
    }.by(1)

    expect(page).to have_text "Log in to your account"
    expect(page).to have_current_path new_user_session_path
  end
end
