require "rails_helper"

describe "User creates a signature" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it "user does not create a signature with invalid data" do
    sign_in create(:user)
    visit new_settings_signature_path
    click_button "save_button"

    expect(page).to have_text "First line can't be blank"
    expect(page).to have_text "Image can't be blank"
  end

  it "user creates a signature with valid data" do
    sign_in create(:user)

    visit new_settings_signature_path

    page.execute_script(
      "document.getElementById('signature_image_data_uri').value='#{dataURL_png}'"
    )
    fill_in "First Line", with: "abc"

    click_button "save_button"

    expect(page).to have_text "Signature was successfully created"
  end

  def dataURL_png
    "data:image/png;base64,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"
  end
end
