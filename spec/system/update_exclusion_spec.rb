require "rails_helper"

describe "Update Exclusion" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it("with valid inputs - successfully") do
    org_user = create(:organisation_user)
    sign_in org_user.user

    patient = create(:patient, organisation: org_user.organisation)
    exclusion = create(:exclusion, patient: patient)

    visit edit_exclusion_path(exclusion)

    fill_in "exclusion[note]", with: "more issues"
    click_button "commit"

    expect(page).to have_text "Exclusion was successfully updated"
  end

  it("with invalid inputs - fails") do
    org_user = create(:organisation_user)
    sign_in org_user.user

    patient = create(:patient, organisation: org_user.organisation)
    exclusion = create(:exclusion, patient: patient)

    visit edit_exclusion_path(exclusion)

    fill_in "exclusion[note]", with: ""
    click_button "commit"

    expect(page).to have_text "Note can't be blank"
  end
end
