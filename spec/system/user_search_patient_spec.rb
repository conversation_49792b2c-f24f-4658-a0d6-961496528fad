# frozen_string_literal: true

require "rails_helper"

describe "User searches for a patient" do
  it "successfully" do
    user = create(:organisation_user)

    create(:patient, first_name: "<PERSON><PERSON>", last_name: "<PERSON>", organisation: user.organisation)

    sign_in user.user
    visit organisation_patients_path user.organisation

    fill_in "patient_search", with: "Toe"

    expect(page).to have_text "Toe Man"
  end
end
