require "rails_helper"

describe "Update Vision Screening" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  it "successfully updates" do
    org_user = create(:organisation_user)
    patient = create(:patient, organisation: org_user.organisation)
    clinic = create(:clinic, organisation: org_user.organisation)
    vision = create(:vision_screening_upload_with_attachment, clinic: clinic, patient: patient)

    sign_in org_user.user

    visit edit_upload_vision_screening_path(vision)

    fill_in "vision[result]", with: "Good Result"

    click_button "commit"

    expect(page).to have_text "Good Result"
  end
end
