require "rails_helper"

describe "User creates a vision screenings assessment" do
  before do
    driven_by(:selenium_chrome_headless)
  end

  describe "Invalid Data" do
    xit "Displays HTML5 error without snellen right eye input" do
      user = create(:organisation_user)
      patient = create(:patient)
      sign_in user.user

      visit new_patient_vision_screening_path(patient)
      click_button "commit"

      message = page.find("#vision_snellen_right_eye_0_1").native.attribute("validationMessage")
      expect(message).to eq "Please select one of these options."
    end

    xit "Displays HTML5 error without snellen left eye input" do
      user = create(:organisation_user)
      patient = create(:patient)
      sign_in user.user

      visit new_patient_vision_screening_path(patient)
      choose("vision_snellen_right_eye_0_1")
      click_button "commit"

      message = page.find("#vision_snellen_left_eye_0_1").native.attribute("validationMessage")
      expect(message).to eq "Please select one of these options."
    end

    xit "Displays validation error without blank inputs" do
      user = create(:organisation_user)
      patient = create(:patient)
      sign_in user.user

      visit new_patient_vision_screening_path(patient)
      choose("vision_snellen_right_eye_0_1")
      choose("vision_snellen_left_eye_0_1")
      click_button "commit"

      expect(page).to have_text "Total right can't be blank"
      expect(page).to have_text "Total left can't be blank"
      expect(page).to have_text "Temporal right can't be blank"
      expect(page).to have_text "Temporal left can't be blank"
    end
  end

  describe "Valid Inputs"
  xit "user creates a valid vision screening assessment" do
    user = create(:organisation_user)
    patient = create(:patient)
    sign_in user.user

    visit new_patient_vision_screening_path(patient)
    choose("vision_snellen_right_eye_0_1")
    choose("vision_snellen_left_eye_0_1")
    fill_in("vision[temporal_right]", with: "90")
    fill_in("vision[temporal_left]", with: "90")
    fill_in("vision[total_right]", with: "90")
    fill_in("vision[total_left]", with: "90")
    check("vision[color_discrimination]")
    click_button "commit"

    expect(page).to have_text "Vision Screening was successfully created"
    expect(VisionScreening.last.color_discrimination).to eql("yes")
  end
end
