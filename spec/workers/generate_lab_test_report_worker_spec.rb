require "rails_helper"

describe "GenerateLabTestReportWorker" do
  describe "#perform" do
    it "add pdf to Assessment Report" do
      report = create(:lab_test)
      user = create(:user)

      options = {report_id: report.id, user_id: user.id}

      allow_any_instance_of(Download).to receive(:to_pdf).and_return(file_fixture("sample.pdf"))

      expect {
        Sidekiq::Testing.inline! do
          GenerateLabTestReportWorker.perform_async(options)
        end
      }.to change(AssessmentReport, :count).by(1)
    end
  end
end
