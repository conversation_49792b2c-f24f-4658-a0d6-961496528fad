# == Schema Information
#
# Table name: annexure_threes
#
#  id                   :bigint           not null, primary key
#  exposures            :string           default([]), is an Array
#  job_requirements     :string           default([]), is an Array
#  protective_equipment :string           default([]), is an Array
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  employment_id        :bigint           not null
#  patient_id           :bigint           not null
#
# Indexes
#
#  index_annexure_threes_on_employment_id  (employment_id)
#  index_annexure_threes_on_patient_id     (patient_id)
#
# Foreign Keys
#
#  fk_rails_...  (employment_id => employments.id)
#  fk_rails_...  (patient_id => patients.id)
#

one:
  patient: one
  employment: one
  exposures: MyString
  job_requirements: MyString
  protective_equipment: MyString

two:
  patient: two
  employment: two
  exposures: MyString
  job_requirements: MyString
  protective_equipment: MyString
