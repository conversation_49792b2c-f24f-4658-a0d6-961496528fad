# == Schema Information
#
# Table name: assessment_reports
#
#  id              :bigint           not null, primary key
#  assessable_type :string           not null
#  date_generated  :date
#  generated_by    :string
#  report_data     :text
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  assessable_id   :bigint           not null
#
# Indexes
#
#  index_assessment_reports_on_assessable_type_and_assessable_id  (assessable_type,assessable_id)
#

one:
  report_data: MyText
  date_generated: 2020-08-27
  generated_by: MyString
  assessable: one
  assessable_type: Assessable

two:
  report_data: MyText
  date_generated: 2020-08-27
  generated_by: MyString
  assessable: two
  assessable_type: Assessable
