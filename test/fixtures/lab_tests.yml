# == Schema Information
#
# Table name: lab_tests
#
#  id             :bigint           not null, primary key
#  ast            :string
#  cannabis       :string
#  comment        :text
#  date_performed :date
#  date_signed    :date
#  description    :text
#  fbc            :string
#  gamma          :string
#  hiv            :string
#  name           :string
#  performed_by   :string
#  signed_by      :string
#  six_panel      :string
#  status         :string
#  created_at     :datetime
#  updated_at     :datetime
#  clinic_id      :bigint           not null
#  patient_id     :bigint
#
# Indexes
#
#  index_lab_tests_on_clinic_id   (clinic_id)
#  index_lab_tests_on_patient_id  (patient_id)
#
# Foreign Keys
#
#  fk_rails_...  (clinic_id => clinics.id)
#  fk_rails_...  (patient_id => patients.id)
#

valid:
  ast: Not Performed
  gamma: Postive
  cannabis: Negative
  clinic: main
  patient: regular
