# == Schema Information
#
# Table name: patients
#
#  id                    :bigint           not null, primary key
#  dob                   :date
#  email                 :string
#  first_name            :string
#  gender                :string
#  identification_number :string
#  last_name             :string
#  phone_number          :string
#  created_at            :datetime         not null
#  updated_at            :datetime         not null
#  organisation_id       :bigint           default(1), not null
#
# Indexes
#
#  index_patients_on_organisation_id  (organisation_id)
#
# Foreign Keys
#
#  fk_rails_...  (organisation_id => organisations.id)
#

regular:
  first_name: Jack 
  last_name: Black
  email: <EMAIL>
