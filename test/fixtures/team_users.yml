# == Schema Information
#
# Table name: team_users
#
#  id         :bigint           not null, primary key
#  is_active  :boolean
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  team_id    :bigint           not null
#  user_id    :bigint           not null
#
# Indexes
#
#  index_team_users_on_team_id  (team_id)
#  index_team_users_on_user_id  (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (team_id => teams.id)
#  fk_rails_...  (user_id => users.id)
#

one:
  team: one
  user: one
  is_active: false

two:
  team: two
  user: two
  is_active: false
