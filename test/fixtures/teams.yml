# == Schema Information
#
# Table name: teams
#
#  id              :bigint           not null, primary key
#  team_name       :string
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  organisation_id :bigint           not null
#
# Indexes
#
#  index_teams_on_organisation_id  (organisation_id)
#
# Foreign Keys
#
#  fk_rails_...  (organisation_id => organisations.id)
#

one:
  team_name: MyString
  organisation: one

two:
  team_name: MyString
  organisation: two
