# == Schema Information
#
# Table name: users
#
#  id                      :bigint           not null, primary key
#  confirmation_sent_at    :datetime
#  confirmation_token      :string
#  confirmed_at            :datetime
#  current_sign_in_at      :datetime
#  current_sign_in_ip      :inet
#  email                   :string           default(""), not null
#  encrypted_password      :string           default(""), not null
#  first_name              :string
#  invitation_accepted_at  :datetime
#  invitation_created_at   :datetime
#  invitation_limit        :integer
#  invitation_sent_at      :datetime
#  invitation_token        :string
#  invitations_count       :integer          default(0)
#  invited_by_type         :string
#  is_active               :boolean
#  last_name               :string
#  last_sign_in_at         :datetime
#  last_sign_in_ip         :inet
#  remember_created_at     :datetime
#  reset_password_sent_at  :datetime
#  reset_password_token    :string
#  sign_in_count           :integer          default(0), not null
#  unconfirmed_email       :string
#  user_mobile             :string
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  default_organisation_id :integer
#  invited_by_id           :bigint
#
# Indexes
#
#  index_users_on_email                              (email) UNIQUE
#  index_users_on_invitation_token                   (invitation_token) UNIQUE
#  index_users_on_invitations_count                  (invitations_count)
#  index_users_on_invited_by_id                      (invited_by_id)
#  index_users_on_invited_by_type_and_invited_by_id  (invited_by_type,invited_by_id)
#  index_users_on_reset_password_token               (reset_password_token) UNIQUE
#

# This model initially had no columns defined. If you add columns to the
# model remove the '{}' from the fixture names and add the columns immediately
# below each fixture, per the syntax in the comments below
#
regular:
  first_name: jack
  last_name: black
  email: <EMAIL>
  confirmation_sent_at: <%= Date.yesterday %>
  confirmed_at: <%= Date.yesterday %> 
  encrypted_password: <%= Devise::Encryptor.digest(User,'password') %>
