# frozen_string_literal: true

ENV["RAILS_ENV"] ||= "test"
require_relative "../config/environment"
require "rails/test_help"
require_relative "./support/shrine"
require_relative "./support/cuprite"
require "sidekiq/testing"
require "mocha/minitest"

class ActiveSupport::TestCase
  fixtures :all

  include Devise::Test::IntegrationHelpers
  include ActionMailer::TestHelper
  include ActionCable::TestHelper
  include CupriteHelpers

  def setup
    Sidekiq::Worker.clear_all
  end

  Shoulda::Matchers.configure do |config|
    config.integrate do |with|
      with.test_framework :minitest
      with.library :rails
    end
  end
end
